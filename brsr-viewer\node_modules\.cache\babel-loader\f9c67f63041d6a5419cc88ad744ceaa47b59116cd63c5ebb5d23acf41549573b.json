{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  module.exports = {\n    Element: 1,\n    Attribute: 2,\n    Text: 3,\n    CData: 4,\n    EntityReference: 5,\n    EntityDeclaration: 6,\n    ProcessingInstruction: 7,\n    Comment: 8,\n    Document: 9,\n    DocType: 10,\n    DocumentFragment: 11,\n    NotationDeclaration: 12,\n    Declaration: 201,\n    Raw: 202,\n    AttributeDeclaration: 203,\n    ElementDeclaration: 204,\n    Dummy: 205\n  };\n}).call(this);", "map": {"version": 3, "names": ["module", "exports", "Element", "Attribute", "Text", "CData", "EntityReference", "EntityDeclaration", "ProcessingInstruction", "Comment", "Document", "DocType", "DocumentFragment", "NotationDeclaration", "Declaration", "Raw", "AttributeDeclaration", "ElementDeclaration", "Dummy", "call"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/NodeType.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Element: 1,\n    Attribute: 2,\n    Text: 3,\n    CData: 4,\n    EntityReference: 5,\n    EntityDeclaration: 6,\n    ProcessingInstruction: 7,\n    Comment: 8,\n    Document: 9,\n    DocType: 10,\n    DocumentFragment: 11,\n    NotationDeclaration: 12,\n    Declaration: 201,\n    Raw: 202,\n    AttributeDeclaration: 203,\n    ElementDeclaration: 204,\n    Dummy: 205\n  };\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACVA,MAAM,CAACC,OAAO,GAAG;IACfC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,CAAC;IACpBC,qBAAqB,EAAE,CAAC;IACxBC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,EAAE;IACpBC,mBAAmB,EAAE,EAAE;IACvBC,WAAW,EAAE,GAAG;IAChBC,GAAG,EAAE,GAAG;IACRC,oBAAoB,EAAE,GAAG;IACzBC,kBAAkB,EAAE,GAAG;IACvBC,KAAK,EAAE;EACT,CAAC;AAEH,CAAC,EAAEC,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}