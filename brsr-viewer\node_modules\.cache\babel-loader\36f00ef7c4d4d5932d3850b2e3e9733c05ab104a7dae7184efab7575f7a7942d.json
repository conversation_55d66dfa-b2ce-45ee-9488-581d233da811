{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLDOMErrorHandler;\n  module.exports = XMLDOMErrorHandler = function () {\n    function XMLDOMErrorHandler() {}\n    XMLDOMErrorHandler.prototype.handleError = function (error) {\n      throw new Error(error);\n    };\n    return XMLDOMErrorHandler;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["XMLDOMErrorHandler", "module", "exports", "prototype", "handleError", "error", "Error", "call"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMErrorHandler;\n\n  module.exports = XMLDOMErrorHandler = (function() {\n    function XMLDOMErrorHandler() {}\n\n    XMLDOMErrorHandler.prototype.handleError = function(error) {\n      throw new Error(error);\n    };\n\n    return XMLDOMErrorHandler;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,kBAAkB;EAEtBC,MAAM,CAACC,OAAO,GAAGF,kBAAkB,GAAI,YAAW;IAChD,SAASA,kBAAkBA,CAAA,EAAG,CAAC;IAE/BA,kBAAkB,CAACG,SAAS,CAACC,WAAW,GAAG,UAASC,KAAK,EAAE;MACzD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAAC;IACxB,CAAC;IAED,OAAOL,kBAAkB;EAE3B,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}