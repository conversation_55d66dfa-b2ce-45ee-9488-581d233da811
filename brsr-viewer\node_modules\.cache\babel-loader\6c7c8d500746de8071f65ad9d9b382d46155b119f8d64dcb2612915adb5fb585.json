{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLDOMConfiguration, XMLDOM<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, XMLDOMStringList;\n  XMLDOMErrorHandler = require('./XMLDOMErrorHandler');\n  XMLDOMStringList = require('./XMLDOMStringList');\n  module.exports = XMLDOMConfiguration = function () {\n    function XMLDOMConfiguration() {\n      var clonedSelf;\n      this.defaultParams = {\n        \"canonical-form\": false,\n        \"cdata-sections\": false,\n        \"comments\": false,\n        \"datatype-normalization\": false,\n        \"element-content-whitespace\": true,\n        \"entities\": true,\n        \"error-handler\": new XMLDOMErrorHandler(),\n        \"infoset\": true,\n        \"validate-if-schema\": false,\n        \"namespaces\": true,\n        \"namespace-declarations\": true,\n        \"normalize-characters\": false,\n        \"schema-location\": '',\n        \"schema-type\": '',\n        \"split-cdata-sections\": true,\n        \"validate\": false,\n        \"well-formed\": true\n      };\n      this.params = clonedSelf = Object.create(this.defaultParams);\n    }\n    Object.defineProperty(XMLDOMConfiguration.prototype, 'parameterNames', {\n      get: function () {\n        return new XMLDOMStringList(Object.keys(this.defaultParams));\n      }\n    });\n    XMLDOMConfiguration.prototype.getParameter = function (name) {\n      if (this.params.hasOwnProperty(name)) {\n        return this.params[name];\n      } else {\n        return null;\n      }\n    };\n    XMLDOMConfiguration.prototype.canSetParameter = function (name, value) {\n      return true;\n    };\n    XMLDOMConfiguration.prototype.setParameter = function (name, value) {\n      if (value != null) {\n        return this.params[name] = value;\n      } else {\n        return delete this.params[name];\n      }\n    };\n    return XMLDOMConfiguration;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["XMLDOMConfiguration", "XMLDOMErrorHandler", "XMLDOMStringList", "require", "module", "exports", "clonedSelf", "defaultParams", "params", "Object", "create", "defineProperty", "prototype", "get", "keys", "getParameter", "name", "hasOwnProperty", "canSetParameter", "value", "setParameter", "call"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDOMConfiguration.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMConfiguration, XMLDOM<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, XMLDOMStringList;\n\n  XMLDOMErrorHandler = require('./XMLDOMErrorHandler');\n\n  XMLDOMStringList = require('./XMLDOMStringList');\n\n  module.exports = XMLDOMConfiguration = (function() {\n    function XMLDOMConfiguration() {\n      var clonedSelf;\n      this.defaultParams = {\n        \"canonical-form\": false,\n        \"cdata-sections\": false,\n        \"comments\": false,\n        \"datatype-normalization\": false,\n        \"element-content-whitespace\": true,\n        \"entities\": true,\n        \"error-handler\": new XMLDOMErrorHandler(),\n        \"infoset\": true,\n        \"validate-if-schema\": false,\n        \"namespaces\": true,\n        \"namespace-declarations\": true,\n        \"normalize-characters\": false,\n        \"schema-location\": '',\n        \"schema-type\": '',\n        \"split-cdata-sections\": true,\n        \"validate\": false,\n        \"well-formed\": true\n      };\n      this.params = clonedSelf = Object.create(this.defaultParams);\n    }\n\n    Object.defineProperty(XMLDOMConfiguration.prototype, 'parameterNames', {\n      get: function() {\n        return new XMLDOMStringList(Object.keys(this.defaultParams));\n      }\n    });\n\n    XMLDOMConfiguration.prototype.getParameter = function(name) {\n      if (this.params.hasOwnProperty(name)) {\n        return this.params[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLDOMConfiguration.prototype.canSetParameter = function(name, value) {\n      return true;\n    };\n\n    XMLDOMConfiguration.prototype.setParameter = function(name, value) {\n      if (value != null) {\n        return this.params[name] = value;\n      } else {\n        return delete this.params[name];\n      }\n    };\n\n    return XMLDOMConfiguration;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB;EAE7DD,kBAAkB,GAAGE,OAAO,CAAC,sBAAsB,CAAC;EAEpDD,gBAAgB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EAEhDC,MAAM,CAACC,OAAO,GAAGL,mBAAmB,GAAI,YAAW;IACjD,SAASA,mBAAmBA,CAAA,EAAG;MAC7B,IAAIM,UAAU;MACd,IAAI,CAACC,aAAa,GAAG;QACnB,gBAAgB,EAAE,KAAK;QACvB,gBAAgB,EAAE,KAAK;QACvB,UAAU,EAAE,KAAK;QACjB,wBAAwB,EAAE,KAAK;QAC/B,4BAA4B,EAAE,IAAI;QAClC,UAAU,EAAE,IAAI;QAChB,eAAe,EAAE,IAAIN,kBAAkB,CAAC,CAAC;QACzC,SAAS,EAAE,IAAI;QACf,oBAAoB,EAAE,KAAK;QAC3B,YAAY,EAAE,IAAI;QAClB,wBAAwB,EAAE,IAAI;QAC9B,sBAAsB,EAAE,KAAK;QAC7B,iBAAiB,EAAE,EAAE;QACrB,aAAa,EAAE,EAAE;QACjB,sBAAsB,EAAE,IAAI;QAC5B,UAAU,EAAE,KAAK;QACjB,aAAa,EAAE;MACjB,CAAC;MACD,IAAI,CAACO,MAAM,GAAGF,UAAU,GAAGG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACH,aAAa,CAAC;IAC9D;IAEAE,MAAM,CAACE,cAAc,CAACX,mBAAmB,CAACY,SAAS,EAAE,gBAAgB,EAAE;MACrEC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAIX,gBAAgB,CAACO,MAAM,CAACK,IAAI,CAAC,IAAI,CAACP,aAAa,CAAC,CAAC;MAC9D;IACF,CAAC,CAAC;IAEFP,mBAAmB,CAACY,SAAS,CAACG,YAAY,GAAG,UAASC,IAAI,EAAE;MAC1D,IAAI,IAAI,CAACR,MAAM,CAACS,cAAc,CAACD,IAAI,CAAC,EAAE;QACpC,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IAEDhB,mBAAmB,CAACY,SAAS,CAACM,eAAe,GAAG,UAASF,IAAI,EAAEG,KAAK,EAAE;MACpE,OAAO,IAAI;IACb,CAAC;IAEDnB,mBAAmB,CAACY,SAAS,CAACQ,YAAY,GAAG,UAASJ,IAAI,EAAEG,KAAK,EAAE;MACjE,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,IAAI,CAACX,MAAM,CAACQ,IAAI,CAAC,GAAGG,KAAK;MAClC,CAAC,MAAM;QACL,OAAO,OAAO,IAAI,CAACX,MAAM,CAACQ,IAAI,CAAC;MACjC;IACF,CAAC;IAED,OAAOhB,mBAAmB;EAE5B,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEqB,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}