{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLCharacterData,\n    XMLText,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  NodeType = require('./NodeType');\n  XMLCharacterData = require('./XMLCharacterData');\n  module.exports = XMLText = function (superClass) {\n    extend(XMLText, superClass);\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.name = \"#text\";\n      this.type = NodeType.Text;\n      this.value = this.stringify.text(text);\n    }\n    Object.defineProperty(XMLText.prototype, 'isElementContentWhitespace', {\n      get: function () {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n    Object.defineProperty(XMLText.prototype, 'wholeText', {\n      get: function () {\n        var next, prev, str;\n        str = '';\n        prev = this.previousSibling;\n        while (prev) {\n          str = prev.data + str;\n          prev = prev.previousSibling;\n        }\n        str += this.data;\n        next = this.nextSibling;\n        while (next) {\n          str = str + next.data;\n          next = next.nextSibling;\n        }\n        return str;\n      }\n    });\n    XMLText.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLText.prototype.toString = function (options) {\n      return this.options.writer.text(this, this.options.writer.filterOptions(options));\n    };\n    XMLText.prototype.splitText = function (offset) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLText.prototype.replaceWholeText = function (content) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    return XMLText;\n  }(XMLCharacterData);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLCharacterData", "XMLText", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "text", "Error", "debugInfo", "name", "type", "Text", "value", "stringify", "Object", "defineProperty", "get", "next", "prev", "str", "previousSibling", "data", "nextS<PERSON>ling", "clone", "create", "toString", "options", "writer", "filterOptions", "splitText", "offset", "replaceWholeText", "content"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLText.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.name = \"#text\";\n      this.type = NodeType.Text;\n      this.value = this.stringify.text(text);\n    }\n\n    Object.defineProperty(XMLText.prototype, 'isElementContentWhitespace', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLText.prototype, 'wholeText', {\n      get: function() {\n        var next, prev, str;\n        str = '';\n        prev = this.previousSibling;\n        while (prev) {\n          str = prev.data + str;\n          prev = prev.previousSibling;\n        }\n        str += this.data;\n        next = this.nextSibling;\n        while (next) {\n          str = str + next.data;\n          next = next.nextSibling;\n        }\n        return str;\n      }\n    });\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.text(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLText.prototype.splitText = function(offset) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLText.prototype.replaceWholeText = function(content) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLText;\n\n  })(XMLCharacterData);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,gBAAgB;IAAEC,OAAO;IACrCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7Bb,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCb,gBAAgB,GAAGa,OAAO,CAAC,oBAAoB,CAAC;EAEhDC,MAAM,CAACC,OAAO,GAAGd,OAAO,GAAI,UAASe,UAAU,EAAE;IAC/Cd,MAAM,CAACD,OAAO,EAAEe,UAAU,CAAC;IAE3B,SAASf,OAAOA,CAACG,MAAM,EAAEa,IAAI,EAAE;MAC7BhB,OAAO,CAACU,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MAChD,IAAIa,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAC9D;MACA,IAAI,CAACC,IAAI,GAAG,OAAO;MACnB,IAAI,CAACC,IAAI,GAAGtB,QAAQ,CAACuB,IAAI;MACzB,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAACP,IAAI,CAACA,IAAI,CAAC;IACxC;IAEAQ,MAAM,CAACC,cAAc,CAACzB,OAAO,CAACS,SAAS,EAAE,4BAA4B,EAAE;MACrEiB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,MAAM,IAAIT,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3E;IACF,CAAC,CAAC;IAEFM,MAAM,CAACC,cAAc,CAACzB,OAAO,CAACS,SAAS,EAAE,WAAW,EAAE;MACpDiB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,IAAIC,IAAI,EAAEC,IAAI,EAAEC,GAAG;QACnBA,GAAG,GAAG,EAAE;QACRD,IAAI,GAAG,IAAI,CAACE,eAAe;QAC3B,OAAOF,IAAI,EAAE;UACXC,GAAG,GAAGD,IAAI,CAACG,IAAI,GAAGF,GAAG;UACrBD,IAAI,GAAGA,IAAI,CAACE,eAAe;QAC7B;QACAD,GAAG,IAAI,IAAI,CAACE,IAAI;QAChBJ,IAAI,GAAG,IAAI,CAACK,WAAW;QACvB,OAAOL,IAAI,EAAE;UACXE,GAAG,GAAGA,GAAG,GAAGF,IAAI,CAACI,IAAI;UACrBJ,IAAI,GAAGA,IAAI,CAACK,WAAW;QACzB;QACA,OAAOH,GAAG;MACZ;IACF,CAAC,CAAC;IAEF7B,OAAO,CAACS,SAAS,CAACwB,KAAK,GAAG,YAAW;MACnC,OAAOT,MAAM,CAACU,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAEDlC,OAAO,CAACS,SAAS,CAAC0B,QAAQ,GAAG,UAASC,OAAO,EAAE;MAC7C,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACrB,IAAI,CAAC,IAAI,EAAE,IAAI,CAACoB,OAAO,CAACC,MAAM,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC;IACnF,CAAC;IAEDpC,OAAO,CAACS,SAAS,CAAC8B,SAAS,GAAG,UAASC,MAAM,EAAE;MAC7C,MAAM,IAAIvB,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAEDlB,OAAO,CAACS,SAAS,CAACgC,gBAAgB,GAAG,UAASC,OAAO,EAAE;MACrD,MAAM,IAAIzB,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,OAAOlB,OAAO;EAEhB,CAAC,CAAED,gBAAgB,CAAC;AAEtB,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}