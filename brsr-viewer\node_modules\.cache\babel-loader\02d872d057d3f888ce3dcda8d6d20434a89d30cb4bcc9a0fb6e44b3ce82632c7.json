{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLDeclaration,\n    XMLNode,\n    isObject,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  isObject = require('./Utility').isObject;\n  XMLNode = require('./XMLNode');\n  NodeType = require('./NodeType');\n  module.exports = XMLDeclaration = function (superClass) {\n    extend(XMLDeclaration, superClass);\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.type = NodeType.Declaration;\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n    XMLDeclaration.prototype.toString = function (options) {\n      return this.options.writer.declaration(this, this.options.writer.filterOptions(options));\n    };\n    return XMLDeclaration;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLDeclaration", "XMLNode", "isObject", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "version", "encoding", "standalone", "ref", "type", "Declaration", "stringify", "xmlVersion", "xmlEncoding", "xmlStandalone", "toString", "options", "writer", "declaration", "filterOptions"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDeclaration.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.type = NodeType.Declaration;\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.declaration(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,cAAc;IAAEC,OAAO;IAAEC,QAAQ;IAC7CC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,QAAQ,GAAGY,OAAO,CAAC,WAAW,CAAC,CAACZ,QAAQ;EAExCD,OAAO,GAAGa,OAAO,CAAC,WAAW,CAAC;EAE9Bf,QAAQ,GAAGe,OAAO,CAAC,YAAY,CAAC;EAEhCC,MAAM,CAACC,OAAO,GAAGhB,cAAc,GAAI,UAASiB,UAAU,EAAE;IACtDd,MAAM,CAACH,cAAc,EAAEiB,UAAU,CAAC;IAElC,SAASjB,cAAcA,CAACK,MAAM,EAAEa,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;MAC7D,IAAIC,GAAG;MACPrB,cAAc,CAACY,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACvD,IAAIH,QAAQ,CAACgB,OAAO,CAAC,EAAE;QACrBG,GAAG,GAAGH,OAAO,EAAEA,OAAO,GAAGG,GAAG,CAACH,OAAO,EAAEC,QAAQ,GAAGE,GAAG,CAACF,QAAQ,EAAEC,UAAU,GAAGC,GAAG,CAACD,UAAU;MAC5F;MACA,IAAI,CAACF,OAAO,EAAE;QACZA,OAAO,GAAG,KAAK;MACjB;MACA,IAAI,CAACI,IAAI,GAAGvB,QAAQ,CAACwB,WAAW;MAChC,IAAI,CAACL,OAAO,GAAG,IAAI,CAACM,SAAS,CAACC,UAAU,CAACP,OAAO,CAAC;MACjD,IAAIC,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACK,SAAS,CAACE,WAAW,CAACP,QAAQ,CAAC;MACtD;MACA,IAAIC,UAAU,IAAI,IAAI,EAAE;QACtB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACI,SAAS,CAACG,aAAa,CAACP,UAAU,CAAC;MAC5D;IACF;IAEApB,cAAc,CAACW,SAAS,CAACiB,QAAQ,GAAG,UAASC,OAAO,EAAE;MACpD,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC,IAAI,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM,CAACE,aAAa,CAACH,OAAO,CAAC,CAAC;IAC1F,CAAC;IAED,OAAO7B,cAAc;EAEvB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}