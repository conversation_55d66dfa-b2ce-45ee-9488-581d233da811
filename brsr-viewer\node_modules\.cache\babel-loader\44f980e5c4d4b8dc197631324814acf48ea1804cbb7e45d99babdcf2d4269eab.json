{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLStringWriter,\n    XMLWriterBase,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  XMLWriterBase = require('./XMLWriterBase');\n  module.exports = XMLStringWriter = function (superClass) {\n    extend(XMLStringWriter, superClass);\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n    XMLStringWriter.prototype.document = function (doc, options) {\n      var child, i, len, r, ref;\n      options = this.filterOptions(options);\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        r += this.writeChildNode(child, options, 0);\n      }\n      if (options.pretty && r.slice(-options.newline.length) === options.newline) {\n        r = r.slice(0, -options.newline.length);\n      }\n      return r;\n    };\n    return XMLStringWriter;\n  }(XMLWriterBase);\n}).call(this);", "map": {"version": 3, "names": ["XMLStringWriter", "XMLWriterBase", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "options", "document", "doc", "i", "len", "r", "ref", "filterOptions", "children", "length", "writeChildNode", "pretty", "slice", "newline"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLStringWriter.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLWriterBase = require('./XMLWriterBase');\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc, options) {\n      var child, i, len, r, ref;\n      options = this.filterOptions(options);\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        r += this.writeChildNode(child, options, 0);\n      }\n      if (options.pretty && r.slice(-options.newline.length) === options.newline) {\n        r = r.slice(0, -options.newline.length);\n      }\n      return r;\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,eAAe;IAAEC,aAAa;IAChCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,aAAa,GAAGY,OAAO,CAAC,iBAAiB,CAAC;EAE1CC,MAAM,CAACC,OAAO,GAAGf,eAAe,GAAI,UAASgB,UAAU,EAAE;IACvDd,MAAM,CAACF,eAAe,EAAEgB,UAAU,CAAC;IAEnC,SAAShB,eAAeA,CAACiB,OAAO,EAAE;MAChCjB,eAAe,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEU,OAAO,CAAC;IAC3D;IAEAjB,eAAe,CAACU,SAAS,CAACQ,QAAQ,GAAG,UAASC,GAAG,EAAEF,OAAO,EAAE;MAC1D,IAAId,KAAK,EAAEiB,CAAC,EAAEC,GAAG,EAAEC,CAAC,EAAEC,GAAG;MACzBN,OAAO,GAAG,IAAI,CAACO,aAAa,CAACP,OAAO,CAAC;MACrCK,CAAC,GAAG,EAAE;MACNC,GAAG,GAAGJ,GAAG,CAACM,QAAQ;MAClB,KAAKL,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGE,GAAG,CAACG,MAAM,EAAEN,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC1CjB,KAAK,GAAGoB,GAAG,CAACH,CAAC,CAAC;QACdE,CAAC,IAAI,IAAI,CAACK,cAAc,CAACxB,KAAK,EAAEc,OAAO,EAAE,CAAC,CAAC;MAC7C;MACA,IAAIA,OAAO,CAACW,MAAM,IAAIN,CAAC,CAACO,KAAK,CAAC,CAACZ,OAAO,CAACa,OAAO,CAACJ,MAAM,CAAC,KAAKT,OAAO,CAACa,OAAO,EAAE;QAC1ER,CAAC,GAAGA,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAACZ,OAAO,CAACa,OAAO,CAACJ,MAAM,CAAC;MACzC;MACA,OAAOJ,CAAC;IACV,CAAC;IAED,OAAOtB,eAAe;EAExB,CAAC,CAAEC,aAAa,CAAC;AAEnB,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}