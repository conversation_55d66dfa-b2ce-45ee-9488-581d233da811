{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    WriterState,\n    XMLAttribute,\n    XMLCData,\n    XMLComment,\n    XMLDTDAttList,\n    XMLDTDElement,\n    XMLDTDEntity,\n    XMLDTDNotation,\n    XMLDeclaration,\n    XMLDocType,\n    XMLDocument,\n    XMLDocumentCB,\n    XMLElement,\n    XMLProcessingInstruction,\n    XMLRaw,\n    XMLStringWriter,\n    XMLStringifier,\n    XMLText,\n    getValue,\n    isFunction,\n    isObject,\n    isPlainObject,\n    ref,\n    hasProp = {}.hasOwnProperty;\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n  NodeType = require('./NodeType');\n  XMLDocument = require('./XMLDocument');\n  XMLElement = require('./XMLElement');\n  XMLCData = require('./XMLCData');\n  XMLComment = require('./XMLComment');\n  XMLRaw = require('./XMLRaw');\n  XMLText = require('./XMLText');\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n  XMLDeclaration = require('./XMLDeclaration');\n  XMLDocType = require('./XMLDocType');\n  XMLDTDAttList = require('./XMLDTDAttList');\n  XMLDTDEntity = require('./XMLDTDEntity');\n  XMLDTDElement = require('./XMLDTDElement');\n  XMLDTDNotation = require('./XMLDTDNotation');\n  XMLAttribute = require('./XMLAttribute');\n  XMLStringifier = require('./XMLStringifier');\n  XMLStringWriter = require('./XMLStringWriter');\n  WriterState = require('./WriterState');\n  module.exports = XMLDocumentCB = function () {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      this.type = NodeType.Document;\n      options || (options = {});\n      writerOptions = {};\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.writerOptions = this.writer.filterOptions(writerOptions);\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function () {};\n      this.onEndCallback = onEnd || function () {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n    XMLDocumentCB.prototype.createChildNode = function (node) {\n      var att, attName, attributes, child, i, len, ref1, ref2;\n      switch (node.type) {\n        case NodeType.CData:\n          this.cdata(node.value);\n          break;\n        case NodeType.Comment:\n          this.comment(node.value);\n          break;\n        case NodeType.Element:\n          attributes = {};\n          ref1 = node.attribs;\n          for (attName in ref1) {\n            if (!hasProp.call(ref1, attName)) continue;\n            att = ref1[attName];\n            attributes[attName] = att.value;\n          }\n          this.node(node.name, attributes);\n          break;\n        case NodeType.Dummy:\n          this.dummy();\n          break;\n        case NodeType.Raw:\n          this.raw(node.value);\n          break;\n        case NodeType.Text:\n          this.text(node.value);\n          break;\n        case NodeType.ProcessingInstruction:\n          this.instruction(node.target, node.value);\n          break;\n        default:\n          throw new Error(\"This XML node type is not supported in a JS object: \" + node.constructor.name);\n      }\n      ref2 = node.children;\n      for (i = 0, len = ref2.length; i < len; i++) {\n        child = ref2[i];\n        this.createChildNode(child);\n        if (child.type === NodeType.Element) {\n          this.up();\n        }\n      }\n      return this;\n    };\n    XMLDocumentCB.prototype.dummy = function () {\n      return this;\n    };\n    XMLDocumentCB.prototype.node = function (name, attributes, text) {\n      var ref1;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n    XMLDocumentCB.prototype.element = function (name, attributes, text) {\n      var child, i, len, oldValidationFlag, ref1, root;\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        this.dtdElement.apply(this, arguments);\n      } else {\n        if (Array.isArray(name) || isObject(name) || isFunction(name)) {\n          oldValidationFlag = this.options.noValidation;\n          this.options.noValidation = true;\n          root = new XMLDocument(this.options).element('TEMP_ROOT');\n          root.element(name);\n          this.options.noValidation = oldValidationFlag;\n          ref1 = root.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            this.createChildNode(child);\n            if (child.type === NodeType.Element) {\n              this.up();\n            }\n          }\n        } else {\n          this.node(name, attributes, text);\n        }\n      }\n      return this;\n    };\n    XMLDocumentCB.prototype.attribute = function (name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && value == null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n    XMLDocumentCB.prototype.text = function (value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.cdata = function (value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.comment = function (value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.raw = function (value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.instruction = function (target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n    XMLDocumentCB.prototype.declaration = function (version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.doctype = function (root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n    XMLDocumentCB.prototype.dtdElement = function (name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.attList = function (elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.entity = function (name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.pEntity = function (name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.notation = function (name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n    XMLDocumentCB.prototype.up = function () {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n    XMLDocumentCB.prototype.end = function () {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n    XMLDocumentCB.prototype.openCurrent = function () {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n    XMLDocumentCB.prototype.openNode = function (node) {\n      var att, chunk, name, ref1;\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node.type === NodeType.Element) {\n          this.root = node;\n        }\n        chunk = '';\n        if (node.type === NodeType.Element) {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<' + node.name;\n          ref1 = node.attribs;\n          for (name in ref1) {\n            if (!hasProp.call(ref1, name)) continue;\n            att = ref1[name];\n            chunk += this.writer.attribute(att, this.writerOptions, this.currentLevel);\n          }\n          chunk += (node.children ? '>' : '/>') + this.writer.endline(node, this.writerOptions, this.currentLevel);\n          this.writerOptions.state = WriterState.InsideTag;\n        } else {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<!DOCTYPE ' + node.rootNodeName;\n          if (node.pubID && node.sysID) {\n            chunk += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n          } else if (node.sysID) {\n            chunk += ' SYSTEM \"' + node.sysID + '\"';\n          }\n          if (node.children) {\n            chunk += ' [';\n            this.writerOptions.state = WriterState.InsideTag;\n          } else {\n            this.writerOptions.state = WriterState.CloseTag;\n            chunk += '>';\n          }\n          chunk += this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.onData(chunk, this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n    XMLDocumentCB.prototype.closeNode = function (node) {\n      var chunk;\n      if (!node.isClosed) {\n        chunk = '';\n        this.writerOptions.state = WriterState.CloseTag;\n        if (node.type === NodeType.Element) {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '</' + node.name + '>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        } else {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + ']>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.writerOptions.state = WriterState.None;\n        this.onData(chunk, this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n    XMLDocumentCB.prototype.onData = function (chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n    XMLDocumentCB.prototype.onEnd = function () {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n    XMLDocumentCB.prototype.debugInfo = function (name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n    XMLDocumentCB.prototype.ele = function () {\n      return this.element.apply(this, arguments);\n    };\n    XMLDocumentCB.prototype.nod = function (name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n    XMLDocumentCB.prototype.txt = function (value) {\n      return this.text(value);\n    };\n    XMLDocumentCB.prototype.dat = function (value) {\n      return this.cdata(value);\n    };\n    XMLDocumentCB.prototype.com = function (value) {\n      return this.comment(value);\n    };\n    XMLDocumentCB.prototype.ins = function (target, value) {\n      return this.instruction(target, value);\n    };\n    XMLDocumentCB.prototype.dec = function (version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n    XMLDocumentCB.prototype.dtd = function (root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n    XMLDocumentCB.prototype.e = function (name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n    XMLDocumentCB.prototype.n = function (name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n    XMLDocumentCB.prototype.t = function (value) {\n      return this.text(value);\n    };\n    XMLDocumentCB.prototype.d = function (value) {\n      return this.cdata(value);\n    };\n    XMLDocumentCB.prototype.c = function (value) {\n      return this.comment(value);\n    };\n    XMLDocumentCB.prototype.r = function (value) {\n      return this.raw(value);\n    };\n    XMLDocumentCB.prototype.i = function (target, value) {\n      return this.instruction(target, value);\n    };\n    XMLDocumentCB.prototype.att = function () {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n    XMLDocumentCB.prototype.a = function () {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n    XMLDocumentCB.prototype.ent = function (name, value) {\n      return this.entity(name, value);\n    };\n    XMLDocumentCB.prototype.pent = function (name, value) {\n      return this.pEntity(name, value);\n    };\n    XMLDocumentCB.prototype.not = function (name, value) {\n      return this.notation(name, value);\n    };\n    return XMLDocumentCB;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "WriterState", "XMLAttribute", "XMLCData", "XMLComment", "XMLDTDAttList", "XMLDTDElement", "XMLDTDEntity", "XMLDTDNotation", "XMLDeclaration", "XMLDocType", "XMLDocument", "XMLDocumentCB", "XMLElement", "XMLProcessingInstruction", "XMLRaw", "XMLStringWriter", "XMLStringifier", "XMLText", "getValue", "isFunction", "isObject", "isPlainObject", "ref", "hasProp", "hasOwnProperty", "require", "module", "exports", "options", "onData", "onEnd", "writerOptions", "name", "type", "Document", "writer", "filterOptions", "stringify", "onDataCallback", "onEndCallback", "currentNode", "currentLevel", "openTags", "documentStarted", "documentCompleted", "root", "prototype", "createChildNode", "node", "att", "attName", "attributes", "child", "i", "len", "ref1", "ref2", "CData", "cdata", "value", "Comment", "comment", "Element", "attribs", "call", "Dummy", "dummy", "Raw", "raw", "Text", "text", "ProcessingInstruction", "instruction", "target", "Error", "constructor", "children", "length", "up", "debugInfo", "openCurrent", "element", "oldValidationFlag", "DocType", "dtdElement", "apply", "arguments", "Array", "isArray", "noValidation", "attribute", "attValue", "keepNullAttributes", "insTarget", "insValue", "processingInstruction", "declaration", "version", "encoding", "standalone", "doctype", "pubID", "sysID", "rootNodeName", "attList", "elementName", "attributeName", "attributeType", "defaultValueType", "defaultValue", "dtdAttList", "entity", "dtdEntity", "pEntity", "notation", "dtdNotation", "closeNode", "openNode", "end", "chunk", "isOpen", "state", "OpenTag", "indent", "endline", "InsideTag", "CloseTag", "isClosed", "None", "level", "ele", "nod", "txt", "dat", "com", "ins", "dec", "dtd", "e", "n", "t", "d", "c", "r", "a", "ent", "pent", "not"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDocumentCB.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocument, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  NodeType = require('./NodeType');\n\n  XMLDocument = require('./XMLDocument');\n\n  XMLElement = require('./XMLElement');\n\n  XMLCData = require('./XMLCData');\n\n  XMLComment = require('./XMLComment');\n\n  XMLRaw = require('./XMLRaw');\n\n  XMLText = require('./XMLText');\n\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n\n  XMLDeclaration = require('./XMLDeclaration');\n\n  XMLDocType = require('./XMLDocType');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  XMLAttribute = require('./XMLAttribute');\n\n  XMLStringifier = require('./XMLStringifier');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  WriterState = require('./WriterState');\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      this.type = NodeType.Document;\n      options || (options = {});\n      writerOptions = {};\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.writerOptions = this.writer.filterOptions(writerOptions);\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.createChildNode = function(node) {\n      var att, attName, attributes, child, i, len, ref1, ref2;\n      switch (node.type) {\n        case NodeType.CData:\n          this.cdata(node.value);\n          break;\n        case NodeType.Comment:\n          this.comment(node.value);\n          break;\n        case NodeType.Element:\n          attributes = {};\n          ref1 = node.attribs;\n          for (attName in ref1) {\n            if (!hasProp.call(ref1, attName)) continue;\n            att = ref1[attName];\n            attributes[attName] = att.value;\n          }\n          this.node(node.name, attributes);\n          break;\n        case NodeType.Dummy:\n          this.dummy();\n          break;\n        case NodeType.Raw:\n          this.raw(node.value);\n          break;\n        case NodeType.Text:\n          this.text(node.value);\n          break;\n        case NodeType.ProcessingInstruction:\n          this.instruction(node.target, node.value);\n          break;\n        default:\n          throw new Error(\"This XML node type is not supported in a JS object: \" + node.constructor.name);\n      }\n      ref2 = node.children;\n      for (i = 0, len = ref2.length; i < len; i++) {\n        child = ref2[i];\n        this.createChildNode(child);\n        if (child.type === NodeType.Element) {\n          this.up();\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dummy = function() {\n      return this;\n    };\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      var child, i, len, oldValidationFlag, ref1, root;\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        this.dtdElement.apply(this, arguments);\n      } else {\n        if (Array.isArray(name) || isObject(name) || isFunction(name)) {\n          oldValidationFlag = this.options.noValidation;\n          this.options.noValidation = true;\n          root = new XMLDocument(this.options).element('TEMP_ROOT');\n          root.element(name);\n          this.options.noValidation = oldValidationFlag;\n          ref1 = root.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            this.createChildNode(child);\n            if (child.type === NodeType.Element) {\n              this.up();\n            }\n          }\n        } else {\n          this.node(name, attributes, text);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      var att, chunk, name, ref1;\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node.type === NodeType.Element) {\n          this.root = node;\n        }\n        chunk = '';\n        if (node.type === NodeType.Element) {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<' + node.name;\n          ref1 = node.attribs;\n          for (name in ref1) {\n            if (!hasProp.call(ref1, name)) continue;\n            att = ref1[name];\n            chunk += this.writer.attribute(att, this.writerOptions, this.currentLevel);\n          }\n          chunk += (node.children ? '>' : '/>') + this.writer.endline(node, this.writerOptions, this.currentLevel);\n          this.writerOptions.state = WriterState.InsideTag;\n        } else {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<!DOCTYPE ' + node.rootNodeName;\n          if (node.pubID && node.sysID) {\n            chunk += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n          } else if (node.sysID) {\n            chunk += ' SYSTEM \"' + node.sysID + '\"';\n          }\n          if (node.children) {\n            chunk += ' [';\n            this.writerOptions.state = WriterState.InsideTag;\n          } else {\n            this.writerOptions.state = WriterState.CloseTag;\n            chunk += '>';\n          }\n          chunk += this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.onData(chunk, this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      var chunk;\n      if (!node.isClosed) {\n        chunk = '';\n        this.writerOptions.state = WriterState.CloseTag;\n        if (node.type === NodeType.Element) {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '</' + node.name + '>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        } else {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + ']>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.writerOptions.state = WriterState.None;\n        this.onData(chunk, this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,WAAW;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,UAAU;IAAEC,aAAa;IAAEC,aAAa;IAAEC,YAAY;IAAEC,cAAc;IAAEC,cAAc;IAAEC,UAAU;IAAEC,WAAW;IAAEC,aAAa;IAAEC,UAAU;IAAEC,wBAAwB;IAAEC,MAAM;IAAEC,eAAe;IAAEC,cAAc;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,GAAG;IAC3TC,OAAO,GAAG,CAAC,CAAC,CAACC,cAAc;EAE7BF,GAAG,GAAGG,OAAO,CAAC,WAAW,CAAC,EAAEL,QAAQ,GAAGE,GAAG,CAACF,QAAQ,EAAED,UAAU,GAAGG,GAAG,CAACH,UAAU,EAAEE,aAAa,GAAGC,GAAG,CAACD,aAAa,EAAEH,QAAQ,GAAGI,GAAG,CAACJ,QAAQ;EAE5InB,QAAQ,GAAG0B,OAAO,CAAC,YAAY,CAAC;EAEhCf,WAAW,GAAGe,OAAO,CAAC,eAAe,CAAC;EAEtCb,UAAU,GAAGa,OAAO,CAAC,cAAc,CAAC;EAEpCvB,QAAQ,GAAGuB,OAAO,CAAC,YAAY,CAAC;EAEhCtB,UAAU,GAAGsB,OAAO,CAAC,cAAc,CAAC;EAEpCX,MAAM,GAAGW,OAAO,CAAC,UAAU,CAAC;EAE5BR,OAAO,GAAGQ,OAAO,CAAC,WAAW,CAAC;EAE9BZ,wBAAwB,GAAGY,OAAO,CAAC,4BAA4B,CAAC;EAEhEjB,cAAc,GAAGiB,OAAO,CAAC,kBAAkB,CAAC;EAE5ChB,UAAU,GAAGgB,OAAO,CAAC,cAAc,CAAC;EAEpCrB,aAAa,GAAGqB,OAAO,CAAC,iBAAiB,CAAC;EAE1CnB,YAAY,GAAGmB,OAAO,CAAC,gBAAgB,CAAC;EAExCpB,aAAa,GAAGoB,OAAO,CAAC,iBAAiB,CAAC;EAE1ClB,cAAc,GAAGkB,OAAO,CAAC,kBAAkB,CAAC;EAE5CxB,YAAY,GAAGwB,OAAO,CAAC,gBAAgB,CAAC;EAExCT,cAAc,GAAGS,OAAO,CAAC,kBAAkB,CAAC;EAE5CV,eAAe,GAAGU,OAAO,CAAC,mBAAmB,CAAC;EAE9CzB,WAAW,GAAGyB,OAAO,CAAC,eAAe,CAAC;EAEtCC,MAAM,CAACC,OAAO,GAAGhB,aAAa,GAAI,YAAW;IAC3C,SAASA,aAAaA,CAACiB,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAE;MAC7C,IAAIC,aAAa;MACjB,IAAI,CAACC,IAAI,GAAG,MAAM;MAClB,IAAI,CAACC,IAAI,GAAGlC,QAAQ,CAACmC,QAAQ;MAC7BN,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC;MACzBG,aAAa,GAAG,CAAC,CAAC;MAClB,IAAI,CAACH,OAAO,CAACO,MAAM,EAAE;QACnBP,OAAO,CAACO,MAAM,GAAG,IAAIpB,eAAe,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIM,aAAa,CAACO,OAAO,CAACO,MAAM,CAAC,EAAE;QACxCJ,aAAa,GAAGH,OAAO,CAACO,MAAM;QAC9BP,OAAO,CAACO,MAAM,GAAG,IAAIpB,eAAe,CAAC,CAAC;MACxC;MACA,IAAI,CAACa,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACO,MAAM,GAAGP,OAAO,CAACO,MAAM;MAC5B,IAAI,CAACJ,aAAa,GAAG,IAAI,CAACI,MAAM,CAACC,aAAa,CAACL,aAAa,CAAC;MAC7D,IAAI,CAACM,SAAS,GAAG,IAAIrB,cAAc,CAACY,OAAO,CAAC;MAC5C,IAAI,CAACU,cAAc,GAAGT,MAAM,IAAI,YAAW,CAAC,CAAC;MAC7C,IAAI,CAACU,aAAa,GAAGT,KAAK,IAAI,YAAW,CAAC,CAAC;MAC3C,IAAI,CAACU,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,IAAI,GAAG,IAAI;IAClB;IAEAlC,aAAa,CAACmC,SAAS,CAACC,eAAe,GAAG,UAASC,IAAI,EAAE;MACvD,IAAIC,GAAG,EAAEC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI;MACvD,QAAQR,IAAI,CAACf,IAAI;QACf,KAAKlC,QAAQ,CAAC0D,KAAK;UACjB,IAAI,CAACC,KAAK,CAACV,IAAI,CAACW,KAAK,CAAC;UACtB;QACF,KAAK5D,QAAQ,CAAC6D,OAAO;UACnB,IAAI,CAACC,OAAO,CAACb,IAAI,CAACW,KAAK,CAAC;UACxB;QACF,KAAK5D,QAAQ,CAAC+D,OAAO;UACnBX,UAAU,GAAG,CAAC,CAAC;UACfI,IAAI,GAAGP,IAAI,CAACe,OAAO;UACnB,KAAKb,OAAO,IAAIK,IAAI,EAAE;YACpB,IAAI,CAAChC,OAAO,CAACyC,IAAI,CAACT,IAAI,EAAEL,OAAO,CAAC,EAAE;YAClCD,GAAG,GAAGM,IAAI,CAACL,OAAO,CAAC;YACnBC,UAAU,CAACD,OAAO,CAAC,GAAGD,GAAG,CAACU,KAAK;UACjC;UACA,IAAI,CAACX,IAAI,CAACA,IAAI,CAAChB,IAAI,EAAEmB,UAAU,CAAC;UAChC;QACF,KAAKpD,QAAQ,CAACkE,KAAK;UACjB,IAAI,CAACC,KAAK,CAAC,CAAC;UACZ;QACF,KAAKnE,QAAQ,CAACoE,GAAG;UACf,IAAI,CAACC,GAAG,CAACpB,IAAI,CAACW,KAAK,CAAC;UACpB;QACF,KAAK5D,QAAQ,CAACsE,IAAI;UAChB,IAAI,CAACC,IAAI,CAACtB,IAAI,CAACW,KAAK,CAAC;UACrB;QACF,KAAK5D,QAAQ,CAACwE,qBAAqB;UACjC,IAAI,CAACC,WAAW,CAACxB,IAAI,CAACyB,MAAM,EAAEzB,IAAI,CAACW,KAAK,CAAC;UACzC;QACF;UACE,MAAM,IAAIe,KAAK,CAAC,sDAAsD,GAAG1B,IAAI,CAAC2B,WAAW,CAAC3C,IAAI,CAAC;MACnG;MACAwB,IAAI,GAAGR,IAAI,CAAC4B,QAAQ;MACpB,KAAKvB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGE,IAAI,CAACqB,MAAM,EAAExB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC3CD,KAAK,GAAGI,IAAI,CAACH,CAAC,CAAC;QACf,IAAI,CAACN,eAAe,CAACK,KAAK,CAAC;QAC3B,IAAIA,KAAK,CAACnB,IAAI,KAAKlC,QAAQ,CAAC+D,OAAO,EAAE;UACnC,IAAI,CAACgB,EAAE,CAAC,CAAC;QACX;MACF;MACA,OAAO,IAAI;IACb,CAAC;IAEDnE,aAAa,CAACmC,SAAS,CAACoB,KAAK,GAAG,YAAW;MACzC,OAAO,IAAI;IACb,CAAC;IAEDvD,aAAa,CAACmC,SAAS,CAACE,IAAI,GAAG,UAAShB,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,EAAE;MAC9D,IAAIf,IAAI;MACR,IAAIvB,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAI0C,KAAK,CAAC,oBAAoB,CAAC;MACvC;MACA,IAAI,IAAI,CAAC7B,IAAI,IAAI,IAAI,CAACJ,YAAY,KAAK,CAAC,CAAC,EAAE;QACzC,MAAM,IAAIiC,KAAK,CAAC,wCAAwC,GAAG,IAAI,CAACK,SAAS,CAAC/C,IAAI,CAAC,CAAC;MAClF;MACA,IAAI,CAACgD,WAAW,CAAC,CAAC;MAClBhD,IAAI,GAAGd,QAAQ,CAACc,IAAI,CAAC;MACrB,IAAImB,UAAU,IAAI,IAAI,EAAE;QACtBA,UAAU,GAAG,CAAC,CAAC;MACjB;MACAA,UAAU,GAAGjC,QAAQ,CAACiC,UAAU,CAAC;MACjC,IAAI,CAAC/B,QAAQ,CAAC+B,UAAU,CAAC,EAAE;QACzBI,IAAI,GAAG,CAACJ,UAAU,EAAEmB,IAAI,CAAC,EAAEA,IAAI,GAAGf,IAAI,CAAC,CAAC,CAAC,EAAEJ,UAAU,GAAGI,IAAI,CAAC,CAAC,CAAC;MACjE;MACA,IAAI,CAACf,WAAW,GAAG,IAAI5B,UAAU,CAAC,IAAI,EAAEoB,IAAI,EAAEmB,UAAU,CAAC;MACzD,IAAI,CAACX,WAAW,CAACoC,QAAQ,GAAG,KAAK;MACjC,IAAI,CAACnC,YAAY,EAAE;MACnB,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACD,YAAY,CAAC,GAAG,IAAI,CAACD,WAAW;MACnD,IAAI8B,IAAI,IAAI,IAAI,EAAE;QAChB,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC;MACjB;MACA,OAAO,IAAI;IACb,CAAC;IAED3D,aAAa,CAACmC,SAAS,CAACmC,OAAO,GAAG,UAASjD,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,EAAE;MACjE,IAAIlB,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAE4B,iBAAiB,EAAE3B,IAAI,EAAEV,IAAI;MAChD,IAAI,IAAI,CAACL,WAAW,IAAI,IAAI,CAACA,WAAW,CAACP,IAAI,KAAKlC,QAAQ,CAACoF,OAAO,EAAE;QAClE,IAAI,CAACC,UAAU,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACxC,CAAC,MAAM;QACL,IAAIC,KAAK,CAACC,OAAO,CAACxD,IAAI,CAAC,IAAIZ,QAAQ,CAACY,IAAI,CAAC,IAAIb,UAAU,CAACa,IAAI,CAAC,EAAE;UAC7DkD,iBAAiB,GAAG,IAAI,CAACtD,OAAO,CAAC6D,YAAY;UAC7C,IAAI,CAAC7D,OAAO,CAAC6D,YAAY,GAAG,IAAI;UAChC5C,IAAI,GAAG,IAAInC,WAAW,CAAC,IAAI,CAACkB,OAAO,CAAC,CAACqD,OAAO,CAAC,WAAW,CAAC;UACzDpC,IAAI,CAACoC,OAAO,CAACjD,IAAI,CAAC;UAClB,IAAI,CAACJ,OAAO,CAAC6D,YAAY,GAAGP,iBAAiB;UAC7C3B,IAAI,GAAGV,IAAI,CAAC+B,QAAQ;UACpB,KAAKvB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACsB,MAAM,EAAExB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;YAC3CD,KAAK,GAAGG,IAAI,CAACF,CAAC,CAAC;YACf,IAAI,CAACN,eAAe,CAACK,KAAK,CAAC;YAC3B,IAAIA,KAAK,CAACnB,IAAI,KAAKlC,QAAQ,CAAC+D,OAAO,EAAE;cACnC,IAAI,CAACgB,EAAE,CAAC,CAAC;YACX;UACF;QACF,CAAC,MAAM;UACL,IAAI,CAAC9B,IAAI,CAAChB,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,CAAC;QACnC;MACF;MACA,OAAO,IAAI;IACb,CAAC;IAED3D,aAAa,CAACmC,SAAS,CAAC4C,SAAS,GAAG,UAAS1D,IAAI,EAAE2B,KAAK,EAAE;MACxD,IAAIT,OAAO,EAAEyC,QAAQ;MACrB,IAAI,CAAC,IAAI,CAACnD,WAAW,IAAI,IAAI,CAACA,WAAW,CAACoC,QAAQ,EAAE;QAClD,MAAM,IAAIF,KAAK,CAAC,2EAA2E,GAAG,IAAI,CAACK,SAAS,CAAC/C,IAAI,CAAC,CAAC;MACrH;MACA,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,GAAGd,QAAQ,CAACc,IAAI,CAAC;MACvB;MACA,IAAIZ,QAAQ,CAACY,IAAI,CAAC,EAAE;QAClB,KAAKkB,OAAO,IAAIlB,IAAI,EAAE;UACpB,IAAI,CAACT,OAAO,CAACyC,IAAI,CAAChC,IAAI,EAAEkB,OAAO,CAAC,EAAE;UAClCyC,QAAQ,GAAG3D,IAAI,CAACkB,OAAO,CAAC;UACxB,IAAI,CAACwC,SAAS,CAACxC,OAAO,EAAEyC,QAAQ,CAAC;QACnC;MACF,CAAC,MAAM;QACL,IAAIxE,UAAU,CAACwC,KAAK,CAAC,EAAE;UACrBA,KAAK,GAAGA,KAAK,CAAC0B,KAAK,CAAC,CAAC;QACvB;QACA,IAAI,IAAI,CAACzD,OAAO,CAACgE,kBAAkB,IAAKjC,KAAK,IAAI,IAAK,EAAE;UACtD,IAAI,CAACnB,WAAW,CAACuB,OAAO,CAAC/B,IAAI,CAAC,GAAG,IAAI/B,YAAY,CAAC,IAAI,EAAE+B,IAAI,EAAE,EAAE,CAAC;QACnE,CAAC,MAAM,IAAI2B,KAAK,IAAI,IAAI,EAAE;UACxB,IAAI,CAACnB,WAAW,CAACuB,OAAO,CAAC/B,IAAI,CAAC,GAAG,IAAI/B,YAAY,CAAC,IAAI,EAAE+B,IAAI,EAAE2B,KAAK,CAAC;QACtE;MACF;MACA,OAAO,IAAI;IACb,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACwB,IAAI,GAAG,UAASX,KAAK,EAAE;MAC7C,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI/B,OAAO,CAAC,IAAI,EAAE0C,KAAK,CAAC;MAC/B,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAACmC,IAAI,CAACtB,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MACrG,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACY,KAAK,GAAG,UAASC,KAAK,EAAE;MAC9C,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI9C,QAAQ,CAAC,IAAI,EAAEyD,KAAK,CAAC;MAChC,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAACuB,KAAK,CAACV,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MACtG,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACe,OAAO,GAAG,UAASF,KAAK,EAAE;MAChD,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI7C,UAAU,CAAC,IAAI,EAAEwD,KAAK,CAAC;MAClC,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAAC0B,OAAO,CAACb,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MACxG,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACsB,GAAG,GAAG,UAAST,KAAK,EAAE;MAC5C,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAIlC,MAAM,CAAC,IAAI,EAAE6C,KAAK,CAAC;MAC9B,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAACiC,GAAG,CAACpB,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MACpG,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAAC0B,WAAW,GAAG,UAASC,MAAM,EAAEd,KAAK,EAAE;MAC5D,IAAIN,CAAC,EAAEwC,SAAS,EAAEC,QAAQ,EAAExC,GAAG,EAAEN,IAAI;MACrC,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClB,IAAIP,MAAM,IAAI,IAAI,EAAE;QAClBA,MAAM,GAAGvD,QAAQ,CAACuD,MAAM,CAAC;MAC3B;MACA,IAAId,KAAK,IAAI,IAAI,EAAE;QACjBA,KAAK,GAAGzC,QAAQ,CAACyC,KAAK,CAAC;MACzB;MACA,IAAI4B,KAAK,CAACC,OAAO,CAACf,MAAM,CAAC,EAAE;QACzB,KAAKpB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGmB,MAAM,CAACI,MAAM,EAAExB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UAC7CwC,SAAS,GAAGpB,MAAM,CAACpB,CAAC,CAAC;UACrB,IAAI,CAACmB,WAAW,CAACqB,SAAS,CAAC;QAC7B;MACF,CAAC,MAAM,IAAIzE,QAAQ,CAACqD,MAAM,CAAC,EAAE;QAC3B,KAAKoB,SAAS,IAAIpB,MAAM,EAAE;UACxB,IAAI,CAAClD,OAAO,CAACyC,IAAI,CAACS,MAAM,EAAEoB,SAAS,CAAC,EAAE;UACtCC,QAAQ,GAAGrB,MAAM,CAACoB,SAAS,CAAC;UAC5B,IAAI,CAACrB,WAAW,CAACqB,SAAS,EAAEC,QAAQ,CAAC;QACvC;MACF,CAAC,MAAM;QACL,IAAI3E,UAAU,CAACwC,KAAK,CAAC,EAAE;UACrBA,KAAK,GAAGA,KAAK,CAAC0B,KAAK,CAAC,CAAC;QACvB;QACArC,IAAI,GAAG,IAAInC,wBAAwB,CAAC,IAAI,EAAE4D,MAAM,EAAEd,KAAK,CAAC;QACxD,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAAC4D,qBAAqB,CAAC/C,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MACxH;MACA,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACkD,WAAW,GAAG,UAASC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;MAC5E,IAAInD,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClB,IAAI,IAAI,CAACrC,eAAe,EAAE;QACxB,MAAM,IAAI+B,KAAK,CAAC,uCAAuC,CAAC;MAC1D;MACA1B,IAAI,GAAG,IAAIxC,cAAc,CAAC,IAAI,EAAEyF,OAAO,EAAEC,QAAQ,EAAEC,UAAU,CAAC;MAC9D,IAAI,CAACtE,MAAM,CAAC,IAAI,CAACM,MAAM,CAAC6D,WAAW,CAAChD,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MAC5G,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACsD,OAAO,GAAG,UAASvD,IAAI,EAAEwD,KAAK,EAAEC,KAAK,EAAE;MAC7D,IAAI,CAACtB,WAAW,CAAC,CAAC;MAClB,IAAInC,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAI6B,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACA,IAAI,IAAI,CAAC7B,IAAI,EAAE;QACb,MAAM,IAAI6B,KAAK,CAAC,uCAAuC,CAAC;MAC1D;MACA,IAAI,CAAClC,WAAW,GAAG,IAAI/B,UAAU,CAAC,IAAI,EAAE4F,KAAK,EAAEC,KAAK,CAAC;MACrD,IAAI,CAAC9D,WAAW,CAAC+D,YAAY,GAAG1D,IAAI;MACpC,IAAI,CAACL,WAAW,CAACoC,QAAQ,GAAG,KAAK;MACjC,IAAI,CAACnC,YAAY,EAAE;MACnB,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACD,YAAY,CAAC,GAAG,IAAI,CAACD,WAAW;MACnD,OAAO,IAAI;IACb,CAAC;IAED7B,aAAa,CAACmC,SAAS,CAACsC,UAAU,GAAG,UAASpD,IAAI,EAAE2B,KAAK,EAAE;MACzD,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI3C,aAAa,CAAC,IAAI,EAAE2B,IAAI,EAAE2B,KAAK,CAAC;MAC3C,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAACiD,UAAU,CAACpC,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MAC3G,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAAC0D,OAAO,GAAG,UAASC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,EAAE;MACpH,IAAI7D,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI5C,aAAa,CAAC,IAAI,EAAEqG,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,CAAC;MACzG,IAAI,CAAChF,MAAM,CAAC,IAAI,CAACM,MAAM,CAAC2E,UAAU,CAAC9D,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MAC3G,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACiE,MAAM,GAAG,UAAS/E,IAAI,EAAE2B,KAAK,EAAE;MACrD,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI1C,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE0B,IAAI,EAAE2B,KAAK,CAAC;MACjD,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAAC6E,SAAS,CAAChE,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MAC1G,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACmE,OAAO,GAAG,UAASjF,IAAI,EAAE2B,KAAK,EAAE;MACtD,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAI1C,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE0B,IAAI,EAAE2B,KAAK,CAAC;MAChD,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAAC6E,SAAS,CAAChE,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MAC1G,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACoE,QAAQ,GAAG,UAASlF,IAAI,EAAE2B,KAAK,EAAE;MACvD,IAAIX,IAAI;MACR,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClBhC,IAAI,GAAG,IAAIzC,cAAc,CAAC,IAAI,EAAEyB,IAAI,EAAE2B,KAAK,CAAC;MAC5C,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAACM,MAAM,CAACgF,WAAW,CAACnE,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;MAC5G,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACgC,EAAE,GAAG,YAAW;MACtC,IAAI,IAAI,CAACrC,YAAY,GAAG,CAAC,EAAE;QACzB,MAAM,IAAIiC,KAAK,CAAC,kCAAkC,CAAC;MACrD;MACA,IAAI,IAAI,CAAClC,WAAW,EAAE;QACpB,IAAI,IAAI,CAACA,WAAW,CAACoC,QAAQ,EAAE;UAC7B,IAAI,CAACwC,SAAS,CAAC,IAAI,CAAC5E,WAAW,CAAC;QAClC,CAAC,MAAM;UACL,IAAI,CAAC6E,QAAQ,CAAC,IAAI,CAAC7E,WAAW,CAAC;QACjC;QACA,IAAI,CAACA,WAAW,GAAG,IAAI;MACzB,CAAC,MAAM;QACL,IAAI,CAAC4E,SAAS,CAAC,IAAI,CAAC1E,QAAQ,CAAC,IAAI,CAACD,YAAY,CAAC,CAAC;MAClD;MACA,OAAO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACD,YAAY,CAAC;MACvC,IAAI,CAACA,YAAY,EAAE;MACnB,OAAO,IAAI;IACb,CAAC;IAED9B,aAAa,CAACmC,SAAS,CAACwE,GAAG,GAAG,YAAW;MACvC,OAAO,IAAI,CAAC7E,YAAY,IAAI,CAAC,EAAE;QAC7B,IAAI,CAACqC,EAAE,CAAC,CAAC;MACX;MACA,OAAO,IAAI,CAAChD,KAAK,CAAC,CAAC;IACrB,CAAC;IAEDnB,aAAa,CAACmC,SAAS,CAACkC,WAAW,GAAG,YAAW;MAC/C,IAAI,IAAI,CAACxC,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACoC,QAAQ,GAAG,IAAI;QAChC,OAAO,IAAI,CAACyC,QAAQ,CAAC,IAAI,CAAC7E,WAAW,CAAC;MACxC;IACF,CAAC;IAED7B,aAAa,CAACmC,SAAS,CAACuE,QAAQ,GAAG,UAASrE,IAAI,EAAE;MAChD,IAAIC,GAAG,EAAEsE,KAAK,EAAEvF,IAAI,EAAEuB,IAAI;MAC1B,IAAI,CAACP,IAAI,CAACwE,MAAM,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC3E,IAAI,IAAI,IAAI,CAACJ,YAAY,KAAK,CAAC,IAAIO,IAAI,CAACf,IAAI,KAAKlC,QAAQ,CAAC+D,OAAO,EAAE;UAC3E,IAAI,CAACjB,IAAI,GAAGG,IAAI;QAClB;QACAuE,KAAK,GAAG,EAAE;QACV,IAAIvE,IAAI,CAACf,IAAI,KAAKlC,QAAQ,CAAC+D,OAAO,EAAE;UAClC,IAAI,CAAC/B,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAAC0H,OAAO;UAC9CH,KAAK,GAAG,IAAI,CAACpF,MAAM,CAACwF,MAAM,CAAC3E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC,GAAG,GAAG,GAAGO,IAAI,CAAChB,IAAI;UACzFuB,IAAI,GAAGP,IAAI,CAACe,OAAO;UACnB,KAAK/B,IAAI,IAAIuB,IAAI,EAAE;YACjB,IAAI,CAAChC,OAAO,CAACyC,IAAI,CAACT,IAAI,EAAEvB,IAAI,CAAC,EAAE;YAC/BiB,GAAG,GAAGM,IAAI,CAACvB,IAAI,CAAC;YAChBuF,KAAK,IAAI,IAAI,CAACpF,MAAM,CAACuD,SAAS,CAACzC,GAAG,EAAE,IAAI,CAAClB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC;UAC5E;UACA8E,KAAK,IAAI,CAACvE,IAAI,CAAC4B,QAAQ,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,CAACzC,MAAM,CAACyF,OAAO,CAAC5E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC;UACxG,IAAI,CAACV,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAAC6H,SAAS;QAClD,CAAC,MAAM;UACL,IAAI,CAAC9F,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAAC0H,OAAO;UAC9CH,KAAK,GAAG,IAAI,CAACpF,MAAM,CAACwF,MAAM,CAAC3E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC,GAAG,YAAY,GAAGO,IAAI,CAACuD,YAAY;UAC1G,IAAIvD,IAAI,CAACqD,KAAK,IAAIrD,IAAI,CAACsD,KAAK,EAAE;YAC5BiB,KAAK,IAAI,WAAW,GAAGvE,IAAI,CAACqD,KAAK,GAAG,KAAK,GAAGrD,IAAI,CAACsD,KAAK,GAAG,GAAG;UAC9D,CAAC,MAAM,IAAItD,IAAI,CAACsD,KAAK,EAAE;YACrBiB,KAAK,IAAI,WAAW,GAAGvE,IAAI,CAACsD,KAAK,GAAG,GAAG;UACzC;UACA,IAAItD,IAAI,CAAC4B,QAAQ,EAAE;YACjB2C,KAAK,IAAI,IAAI;YACb,IAAI,CAACxF,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAAC6H,SAAS;UAClD,CAAC,MAAM;YACL,IAAI,CAAC9F,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAAC8H,QAAQ;YAC/CP,KAAK,IAAI,GAAG;UACd;UACAA,KAAK,IAAI,IAAI,CAACpF,MAAM,CAACyF,OAAO,CAAC5E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC;QAC3E;QACA,IAAI,CAACZ,MAAM,CAAC0F,KAAK,EAAE,IAAI,CAAC9E,YAAY,CAAC;QACrC,OAAOO,IAAI,CAACwE,MAAM,GAAG,IAAI;MAC3B;IACF,CAAC;IAED7G,aAAa,CAACmC,SAAS,CAACsE,SAAS,GAAG,UAASpE,IAAI,EAAE;MACjD,IAAIuE,KAAK;MACT,IAAI,CAACvE,IAAI,CAAC+E,QAAQ,EAAE;QAClBR,KAAK,GAAG,EAAE;QACV,IAAI,CAACxF,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAAC8H,QAAQ;QAC/C,IAAI9E,IAAI,CAACf,IAAI,KAAKlC,QAAQ,CAAC+D,OAAO,EAAE;UAClCyD,KAAK,GAAG,IAAI,CAACpF,MAAM,CAACwF,MAAM,CAAC3E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC,GAAG,IAAI,GAAGO,IAAI,CAAChB,IAAI,GAAG,GAAG,GAAG,IAAI,CAACG,MAAM,CAACyF,OAAO,CAAC5E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC;QACrK,CAAC,MAAM;UACL8E,KAAK,GAAG,IAAI,CAACpF,MAAM,CAACwF,MAAM,CAAC3E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC,GAAG,IAAI,GAAG,IAAI,CAACN,MAAM,CAACyF,OAAO,CAAC5E,IAAI,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAACU,YAAY,CAAC;QACnJ;QACA,IAAI,CAACV,aAAa,CAAC0F,KAAK,GAAGzH,WAAW,CAACgI,IAAI;QAC3C,IAAI,CAACnG,MAAM,CAAC0F,KAAK,EAAE,IAAI,CAAC9E,YAAY,CAAC;QACrC,OAAOO,IAAI,CAAC+E,QAAQ,GAAG,IAAI;MAC7B;IACF,CAAC;IAEDpH,aAAa,CAACmC,SAAS,CAACjB,MAAM,GAAG,UAAS0F,KAAK,EAAEU,KAAK,EAAE;MACtD,IAAI,CAACtF,eAAe,GAAG,IAAI;MAC3B,OAAO,IAAI,CAACL,cAAc,CAACiF,KAAK,EAAEU,KAAK,GAAG,CAAC,CAAC;IAC9C,CAAC;IAEDtH,aAAa,CAACmC,SAAS,CAAChB,KAAK,GAAG,YAAW;MACzC,IAAI,CAACc,iBAAiB,GAAG,IAAI;MAC7B,OAAO,IAAI,CAACL,aAAa,CAAC,CAAC;IAC7B,CAAC;IAED5B,aAAa,CAACmC,SAAS,CAACiC,SAAS,GAAG,UAAS/C,IAAI,EAAE;MACjD,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,EAAE;MACX,CAAC,MAAM;QACL,OAAO,SAAS,GAAGA,IAAI,GAAG,GAAG;MAC/B;IACF,CAAC;IAEDrB,aAAa,CAACmC,SAAS,CAACoF,GAAG,GAAG,YAAW;MACvC,OAAO,IAAI,CAACjD,OAAO,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5C,CAAC;IAED3E,aAAa,CAACmC,SAAS,CAACqF,GAAG,GAAG,UAASnG,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,EAAE;MAC7D,OAAO,IAAI,CAACtB,IAAI,CAAChB,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,CAAC;IAC1C,CAAC;IAED3D,aAAa,CAACmC,SAAS,CAACsF,GAAG,GAAG,UAASzE,KAAK,EAAE;MAC5C,OAAO,IAAI,CAACW,IAAI,CAACX,KAAK,CAAC;IACzB,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACuF,GAAG,GAAG,UAAS1E,KAAK,EAAE;MAC5C,OAAO,IAAI,CAACD,KAAK,CAACC,KAAK,CAAC;IAC1B,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACwF,GAAG,GAAG,UAAS3E,KAAK,EAAE;MAC5C,OAAO,IAAI,CAACE,OAAO,CAACF,KAAK,CAAC;IAC5B,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACyF,GAAG,GAAG,UAAS9D,MAAM,EAAEd,KAAK,EAAE;MACpD,OAAO,IAAI,CAACa,WAAW,CAACC,MAAM,EAAEd,KAAK,CAAC;IACxC,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAAC0F,GAAG,GAAG,UAASvC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;MACpE,OAAO,IAAI,CAACH,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,CAAC;IACxD,CAAC;IAEDxF,aAAa,CAACmC,SAAS,CAAC2F,GAAG,GAAG,UAAS5F,IAAI,EAAEwD,KAAK,EAAEC,KAAK,EAAE;MACzD,OAAO,IAAI,CAACF,OAAO,CAACvD,IAAI,EAAEwD,KAAK,EAAEC,KAAK,CAAC;IACzC,CAAC;IAED3F,aAAa,CAACmC,SAAS,CAAC4F,CAAC,GAAG,UAAS1G,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,EAAE;MAC3D,OAAO,IAAI,CAACW,OAAO,CAACjD,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,CAAC;IAC7C,CAAC;IAED3D,aAAa,CAACmC,SAAS,CAAC6F,CAAC,GAAG,UAAS3G,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,EAAE;MAC3D,OAAO,IAAI,CAACtB,IAAI,CAAChB,IAAI,EAAEmB,UAAU,EAAEmB,IAAI,CAAC;IAC1C,CAAC;IAED3D,aAAa,CAACmC,SAAS,CAAC8F,CAAC,GAAG,UAASjF,KAAK,EAAE;MAC1C,OAAO,IAAI,CAACW,IAAI,CAACX,KAAK,CAAC;IACzB,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAAC+F,CAAC,GAAG,UAASlF,KAAK,EAAE;MAC1C,OAAO,IAAI,CAACD,KAAK,CAACC,KAAK,CAAC;IAC1B,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACgG,CAAC,GAAG,UAASnF,KAAK,EAAE;MAC1C,OAAO,IAAI,CAACE,OAAO,CAACF,KAAK,CAAC;IAC5B,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACiG,CAAC,GAAG,UAASpF,KAAK,EAAE;MAC1C,OAAO,IAAI,CAACS,GAAG,CAACT,KAAK,CAAC;IACxB,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACO,CAAC,GAAG,UAASoB,MAAM,EAAEd,KAAK,EAAE;MAClD,OAAO,IAAI,CAACa,WAAW,CAACC,MAAM,EAAEd,KAAK,CAAC;IACxC,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACG,GAAG,GAAG,YAAW;MACvC,IAAI,IAAI,CAACT,WAAW,IAAI,IAAI,CAACA,WAAW,CAACP,IAAI,KAAKlC,QAAQ,CAACoF,OAAO,EAAE;QAClE,OAAO,IAAI,CAACqB,OAAO,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC5C,CAAC,MAAM;QACL,OAAO,IAAI,CAACI,SAAS,CAACL,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C;IACF,CAAC;IAED3E,aAAa,CAACmC,SAAS,CAACkG,CAAC,GAAG,YAAW;MACrC,IAAI,IAAI,CAACxG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACP,IAAI,KAAKlC,QAAQ,CAACoF,OAAO,EAAE;QAClE,OAAO,IAAI,CAACqB,OAAO,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC5C,CAAC,MAAM;QACL,OAAO,IAAI,CAACI,SAAS,CAACL,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C;IACF,CAAC;IAED3E,aAAa,CAACmC,SAAS,CAACmG,GAAG,GAAG,UAASjH,IAAI,EAAE2B,KAAK,EAAE;MAClD,OAAO,IAAI,CAACoD,MAAM,CAAC/E,IAAI,EAAE2B,KAAK,CAAC;IACjC,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACoG,IAAI,GAAG,UAASlH,IAAI,EAAE2B,KAAK,EAAE;MACnD,OAAO,IAAI,CAACsD,OAAO,CAACjF,IAAI,EAAE2B,KAAK,CAAC;IAClC,CAAC;IAEDhD,aAAa,CAACmC,SAAS,CAACqG,GAAG,GAAG,UAASnH,IAAI,EAAE2B,KAAK,EAAE;MAClD,OAAO,IAAI,CAACuD,QAAQ,CAAClF,IAAI,EAAE2B,KAAK,CAAC;IACnC,CAAC;IAED,OAAOhD,aAAa;EAEtB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEqD,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}