{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLCData,\n    XMLCharacterData,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  NodeType = require('./NodeType');\n  XMLCharacterData = require('./XMLCharacterData');\n  module.exports = XMLCData = function (superClass) {\n    extend(XMLCData, superClass);\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.name = \"#cdata-section\";\n      this.type = NodeType.CData;\n      this.value = this.stringify.cdata(text);\n    }\n    XMLCData.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLCData.prototype.toString = function (options) {\n      return this.options.writer.cdata(this, this.options.writer.filterOptions(options));\n    };\n    return XMLCData;\n  }(XMLCharacterData);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLCData", "XMLCharacterData", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "text", "Error", "debugInfo", "name", "type", "CData", "value", "stringify", "cdata", "clone", "Object", "create", "toString", "options", "writer", "filterOptions"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLCData.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCData, XMLCharacterData,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.name = \"#cdata-section\";\n      this.type = NodeType.CData;\n      this.value = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.cdata(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLCData;\n\n  })(XMLCharacterData);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,QAAQ;IAAEC,gBAAgB;IACtCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7Bb,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCZ,gBAAgB,GAAGY,OAAO,CAAC,oBAAoB,CAAC;EAEhDC,MAAM,CAACC,OAAO,GAAGf,QAAQ,GAAI,UAASgB,UAAU,EAAE;IAChDd,MAAM,CAACF,QAAQ,EAAEgB,UAAU,CAAC;IAE5B,SAAShB,QAAQA,CAACI,MAAM,EAAEa,IAAI,EAAE;MAC9BjB,QAAQ,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACjD,IAAIa,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAC5D;MACA,IAAI,CAACC,IAAI,GAAG,gBAAgB;MAC5B,IAAI,CAACC,IAAI,GAAGtB,QAAQ,CAACuB,KAAK;MAC1B,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAACC,KAAK,CAACR,IAAI,CAAC;IACzC;IAEAjB,QAAQ,CAACU,SAAS,CAACgB,KAAK,GAAG,YAAW;MACpC,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED5B,QAAQ,CAACU,SAAS,CAACmB,QAAQ,GAAG,UAASC,OAAO,EAAE;MAC9C,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACN,KAAK,CAAC,IAAI,EAAE,IAAI,CAACK,OAAO,CAACC,MAAM,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC;IACpF,CAAC;IAED,OAAO9B,QAAQ;EAEjB,CAAC,CAAEC,gBAAgB,CAAC;AAEtB,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}