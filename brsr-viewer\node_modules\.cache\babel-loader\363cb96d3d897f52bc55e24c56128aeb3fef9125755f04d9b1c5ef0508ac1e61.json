{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\brsr_reports\\\\brsr-viewer\\\\src\\\\BRSRViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BRSRViewer() {\n  _s();\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({\n    Environment: [],\n    Social: [],\n    Governance: []\n  });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = e => {\n    console.log(\"File upload triggered\");\n    const file = e.target.files[0];\n    if (!file) {\n      console.log(\"No file selected\");\n      return;\n    }\n    console.log(\"File selected:\", file.name, file.type, file.size);\n    const reader = new FileReader();\n    reader.onload = evt => {\n      try {\n        console.log(\"File read successfully, parsing...\");\n        let json;\n        if (file.name.toLowerCase().endsWith('.csv')) {\n          // Handle CSV files with proper parsing\n          console.log(\"Processing CSV file\");\n          const csvText = evt.target.result;\n\n          // Better CSV parsing function\n          const parseCSVLine = line => {\n            const result = [];\n            let current = '';\n            let inQuotes = false;\n            for (let i = 0; i < line.length; i++) {\n              const char = line[i];\n              if (char === '\"') {\n                inQuotes = !inQuotes;\n              } else if (char === ',' && !inQuotes) {\n                result.push(current.trim());\n                current = '';\n              } else {\n                current += char;\n              }\n            }\n            result.push(current.trim());\n            return result;\n          };\n          const lines = csvText.split(/\\r?\\n/).filter(line => line.trim());\n          console.log(\"First line raw:\", lines[0]);\n          const headers = parseCSVLine(lines[0]).map(h => h.replace(/\"/g, '').trim());\n          console.log(\"CSV headers after parsing:\", headers);\n          console.log(\"Number of headers:\", headers.length);\n          json = [];\n          for (let i = 1; i < lines.length; i++) {\n            const values = parseCSVLine(lines[i]).map(v => v.replace(/\"/g, '').trim());\n            const row = {};\n            headers.forEach((header, index) => {\n              row[header] = values[index] || '';\n            });\n            if (Object.values(row).some(val => val)) {\n              // Only add non-empty rows\n              json.push(row);\n            }\n          }\n        } else {\n          // Handle Excel files\n          console.log(\"Processing Excel file\");\n          const workbook = XLSX.read(evt.target.result, {\n            type: \"binary\"\n          });\n          console.log(\"Workbook created, sheet names:\", workbook.SheetNames);\n          const sheetName = workbook.SheetNames[0];\n          const worksheet = workbook.Sheets[sheetName];\n          json = XLSX.utils.sheet_to_json(worksheet);\n        }\n        console.log(\"JSON data parsed:\", json.length, \"rows\");\n        console.log(\"Sample data:\", json.slice(0, 2));\n\n        // Log all column names to help debug\n        if (json.length > 0) {\n          console.log(\"Available columns:\", Object.keys(json[0]));\n        }\n        setCompanyData(json);\n      } catch (error) {\n        console.error(\"Error parsing file:\", error);\n        alert(\"Error parsing file: \" + error.message);\n      }\n    };\n    reader.onerror = error => {\n      console.error(\"Error reading file:\", error);\n      alert(\"Error reading file\");\n    };\n\n    // Use different reading methods for different file types\n    if (file.name.toLowerCase().endsWith('.csv')) {\n      reader.readAsText(file);\n    } else {\n      reader.readAsBinaryString(file);\n    }\n  };\n\n  /** Categorize field names **/\n  const categorizeField = name => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\")) return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\")) return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = xmlText => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach(ctx => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\"\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach(u => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = {\n      Environment: [],\n      Social: [],\n      Governance: []\n    };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach(element => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: (context === null || context === void 0 ? void 0 : context.entity) || \"\"\n        });\n      }\n    });\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async row => {\n    console.log(\"Company selected:\", row);\n    console.log(\"All row keys:\", Object.keys(row));\n    console.log(\"Row values:\", Object.values(row));\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({\n      Environment: [],\n      Social: [],\n      Governance: []\n    });\n    const xmlLink = getColumnValue(row, [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n    console.log(\"XML Link found:\", xmlLink);\n    console.log(\"Checking each possible column:\");\n    [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"].forEach(col => {\n      console.log(`  ${col}:`, row[col]);\n    });\n    if (xmlLink && xmlLink.trim() && xmlLink !== 'No Link') {\n      try {\n        console.log(\"Fetching XML from:\", xmlLink);\n        const res = await axios.get(xmlLink);\n        console.log(\"XML response received, length:\", res.data.length);\n        const categories = parseBRSRXML(res.data);\n        console.log(\"Parsed categories:\", categories);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n        alert(\"Error loading XML: \" + err.message);\n      } finally {\n        setLoadingSingle(false);\n      }\n    } else {\n      console.log(\"No valid XML Link found for this company\");\n      console.log(\"xmlLink value:\", `\"${xmlLink}\"`);\n      setLoadingSingle(false);\n      alert(\"No XML Link found for this company\");\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach(cat => {\n            categories[cat].forEach(d => allData.push({\n              ...d,\n              category: cat,\n              companyName: row[\"Company Name\"],\n              year: row[\"Year\"]\n            }));\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Helper function to get column value with flexible naming **/\n  const getColumnValue = (row, possibleNames) => {\n    for (const name of possibleNames) {\n      if (row[name] !== undefined && row[name] !== null && row[name] !== '') {\n        return row[name];\n      }\n    }\n    return '';\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(row => row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) || row.value.toLowerCase().includes(searchBRSR.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        fontSize: '2rem',\n        fontWeight: 'bold',\n        color: '#333',\n        marginBottom: '24px',\n        textAlign: 'center'\n      },\n      children: \"\\uD83D\\uDCCA BRSR Report Viewer (Excel + XML)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".xlsx,.xls,.csv\",\n        onChange: handleExcelUpload,\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), companyData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#28a745',\n            fontWeight: '500'\n          },\n          children: [\"\\u2705 \", companyData.length, \" companies loaded\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#6c757d',\n            marginTop: '4px'\n          },\n          children: [\"Available columns: \", Object.keys(companyData[0]).join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            // Add sample data for testing\n            const sampleData = [{\n              \"Company Name\": \"Sample Corp Ltd\",\n              \"Year\": \"2023\",\n              \"XML Link\": \"https://httpbin.org/xml\" // This returns sample XML for testing\n            }, {\n              \"Company Name\": \"Test Industries\",\n              \"Year\": \"2023\",\n              \"XML Link\": \"https://httpbin.org/xml\"\n            }];\n            setCompanyData(sampleData);\n            console.log(\"Sample data loaded:\", sampleData);\n          },\n          style: {\n            padding: '8px 16px',\n            backgroundColor: '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          },\n          children: \"Load Sample Data (for testing)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '16px',\n        marginBottom: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Search company...\",\n        value: searchCompany,\n        onChange: e => setSearchCompany(e.target.value),\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchAllCompanies,\n        disabled: loadingAll || companyData.length === 0,\n        style: {\n          padding: '12px 16px',\n          borderRadius: '8px',\n          border: 'none',\n          color: 'white',\n          backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n          cursor: loadingAll ? 'not-allowed' : 'pointer',\n          opacity: loadingAll ? 0.6 : 1\n        },\n        children: loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), companyData.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '16px',\n        marginBottom: '32px'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataTable, {\n        title: `Companies (${companyData.length} loaded)`,\n        columns: [{\n          name: \"Company\",\n          selector: row => getColumnValue(row, [\"COMPANY\", \"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]),\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => getColumnValue(row, [\"FROM YEAR\", \"TO YEAR\", \"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"]),\n          sortable: true\n        }, {\n          name: \"XML Link\",\n          selector: row => getColumnValue(row, [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]),\n          sortable: true,\n          cell: row => {\n            const link = getColumnValue(row, [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n            return link ? /*#__PURE__*/_jsxDEV(\"a\", {\n              href: link,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                color: '#007bff',\n                fontSize: '12px'\n              },\n              children: link.length > 30 ? link.substring(0, 30) + '...' : link\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 21\n            }, this) : 'No Link';\n          }\n        }, {\n          name: \"Actions\",\n          cell: row => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCompanySelect(row),\n            style: {\n              backgroundColor: '#007bff',\n              color: 'white',\n              padding: '6px 12px',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            },\n            children: \"View BRSR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 19\n          }, this)\n        }],\n        data: companyData.filter(r => {\n          const companyName = getColumnValue(r, [\"COMPANY\", \"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]);\n          return companyName.toLowerCase().includes(searchCompany.toLowerCase());\n        }),\n        pagination: true,\n        highlightOnHover: true,\n        dense: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '32px',\n        marginBottom: '32px',\n        textAlign: 'center',\n        color: '#6c757d'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\uD83D\\uDCC1 No companies loaded yet. Please upload an Excel file to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '14px',\n          marginTop: '8px'\n        },\n        children: \"Expected columns: Company Name, Year, XML Link\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 9\n    }, this), unifiedData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px',\n        marginBottom: '32px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: \"Unified BRSR Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Company\",\n          selector: row => row.companyName,\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => row.year,\n          sortable: true\n        }, {\n          name: \"Category\",\n          selector: row => row.category,\n          sortable: true\n        }, {\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }],\n        data: unifiedData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 9\n    }, this), selectedCompany && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: [getColumnValue(selectedCompany, [\"COMPANY\", \"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]), \"(\", getColumnValue(selectedCompany, [\"FROM YEAR\", \"TO YEAR\", \"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"]), \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '16px',\n          marginBottom: '16px'\n        },\n        children: [\"Environment\", \"Social\", \"Governance\"].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab),\n          style: {\n            padding: '8px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            fontWeight: '500',\n            cursor: 'pointer',\n            backgroundColor: activeTab === tab ? tab === \"Environment\" ? \"#28a745\" : tab === \"Social\" ? \"#6f42c1\" : \"#fd7e14\" : \"#e9ecef\",\n            color: activeTab === tab ? \"white\" : \"#495057\"\n          },\n          children: tab\n        }, tab, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: `Search in ${activeTab}...`,\n          value: searchBRSR,\n          onChange: e => setSearchBRSR(e.target.value),\n          style: {\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 11\n      }, this), loadingSingle ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '40px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2rem',\n            color: '#007bff'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }, {\n          name: \"Unit\",\n          selector: row => row.unit || \"-\",\n          sortable: true\n        }, {\n          name: \"Period\",\n          selector: row => row.period,\n          sortable: true\n        }],\n        data: filteredTabData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n}\n_s(BRSRViewer, \"XqWeNAlGtEHiedqF0JxGSKVABwY=\");\n_c = BRSRViewer;\nvar _c;\n$RefreshReg$(_c, \"BRSRViewer\");", "map": {"version": 3, "names": ["React", "useState", "XLSX", "axios", "DataTable", "jsxDEV", "_jsxDEV", "BRSRViewer", "_s", "companyData", "setCompanyData", "searchCompany", "setSearchCompany", "selectedCompany", "setSelectedCompany", "brsrData", "setBrsrData", "Environment", "Social", "Governance", "searchBRSR", "setSearchBRSR", "activeTab", "setActiveTab", "unifiedData", "setUnifiedData", "loadingAll", "setLoadingAll", "loadingSingle", "setLoading<PERSON>ingle", "handleExcelUpload", "e", "console", "log", "file", "target", "files", "name", "type", "size", "reader", "FileReader", "onload", "evt", "json", "toLowerCase", "endsWith", "csvText", "result", "parseCSVLine", "line", "current", "inQuotes", "i", "length", "char", "push", "trim", "lines", "split", "filter", "headers", "map", "h", "replace", "values", "v", "row", "for<PERSON>ach", "header", "index", "Object", "some", "val", "workbook", "read", "SheetNames", "sheetName", "worksheet", "Sheets", "utils", "sheet_to_json", "slice", "keys", "error", "alert", "message", "onerror", "readAsText", "readAsBinaryString", "categorizeField", "lower", "includes", "parseBRSRXML", "xmlText", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "contexts", "units", "contextElements", "getElementsByTagName", "Array", "from", "ctx", "id", "getAttribute", "entityElement", "startDateElement", "endDateElement", "entity", "textContent", "startDate", "endDate", "unitElements", "u", "measureElement", "categories", "allElements", "element", "tagName", "startsWith", "contextRef", "unitRef", "context", "unit", "category", "metric", "value", "period", "company", "handleCompanySelect", "xmlLink", "getColumnValue", "col", "res", "get", "data", "err", "fetchAllCompanies", "allData", "cat", "d", "companyName", "year", "warn", "possibleNames", "undefined", "filteredTabData", "style", "padding", "backgroundColor", "minHeight", "children", "fontSize", "fontWeight", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "alignItems", "accept", "onChange", "width", "border", "borderRadius", "boxShadow", "marginTop", "join", "onClick", "sampleData", "cursor", "justifyContent", "gap", "placeholder", "disabled", "opacity", "title", "columns", "selector", "sortable", "cell", "link", "href", "rel", "substring", "r", "pagination", "highlightOnHover", "dense", "tab", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/src/BRSRViewer.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\n\nexport default function BRSRViewer() {\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = (e) => {\n    console.log(\"File upload triggered\");\n    const file = e.target.files[0];\n    if (!file) {\n      console.log(\"No file selected\");\n      return;\n    }\n\n    console.log(\"File selected:\", file.name, file.type, file.size);\n\n    const reader = new FileReader();\n    reader.onload = (evt) => {\n      try {\n        console.log(\"File read successfully, parsing...\");\n        let json;\n\n        if (file.name.toLowerCase().endsWith('.csv')) {\n          // Handle CSV files with proper parsing\n          console.log(\"Processing CSV file\");\n          const csvText = evt.target.result;\n\n          // Better CSV parsing function\n          const parseCSVLine = (line) => {\n            const result = [];\n            let current = '';\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n              const char = line[i];\n\n              if (char === '\"') {\n                inQuotes = !inQuotes;\n              } else if (char === ',' && !inQuotes) {\n                result.push(current.trim());\n                current = '';\n              } else {\n                current += char;\n              }\n            }\n            result.push(current.trim());\n            return result;\n          };\n\n          const lines = csvText.split(/\\r?\\n/).filter(line => line.trim());\n          console.log(\"First line raw:\", lines[0]);\n\n          const headers = parseCSVLine(lines[0]).map(h => h.replace(/\"/g, '').trim());\n          console.log(\"CSV headers after parsing:\", headers);\n          console.log(\"Number of headers:\", headers.length);\n\n          json = [];\n          for (let i = 1; i < lines.length; i++) {\n            const values = parseCSVLine(lines[i]).map(v => v.replace(/\"/g, '').trim());\n            const row = {};\n            headers.forEach((header, index) => {\n              row[header] = values[index] || '';\n            });\n            if (Object.values(row).some(val => val)) { // Only add non-empty rows\n              json.push(row);\n            }\n          }\n        } else {\n          // Handle Excel files\n          console.log(\"Processing Excel file\");\n          const workbook = XLSX.read(evt.target.result, { type: \"binary\" });\n          console.log(\"Workbook created, sheet names:\", workbook.SheetNames);\n\n          const sheetName = workbook.SheetNames[0];\n          const worksheet = workbook.Sheets[sheetName];\n          json = XLSX.utils.sheet_to_json(worksheet);\n        }\n\n        console.log(\"JSON data parsed:\", json.length, \"rows\");\n        console.log(\"Sample data:\", json.slice(0, 2));\n\n        // Log all column names to help debug\n        if (json.length > 0) {\n          console.log(\"Available columns:\", Object.keys(json[0]));\n        }\n\n        setCompanyData(json);\n      } catch (error) {\n        console.error(\"Error parsing file:\", error);\n        alert(\"Error parsing file: \" + error.message);\n      }\n    };\n\n    reader.onerror = (error) => {\n      console.error(\"Error reading file:\", error);\n      alert(\"Error reading file\");\n    };\n\n    // Use different reading methods for different file types\n    if (file.name.toLowerCase().endsWith('.csv')) {\n      reader.readAsText(file);\n    } else {\n      reader.readAsBinaryString(file);\n    }\n  };\n\n  /** Categorize field names **/\n  const categorizeField = (name) => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\"))\n      return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\"))\n      return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = (xmlText) => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach((ctx) => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\",\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach((u) => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = { Environment: [], Social: [], Governance: [] };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach((element) => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: context?.entity || \"\",\n        });\n      }\n    });\n\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async (row) => {\n    console.log(\"Company selected:\", row);\n    console.log(\"All row keys:\", Object.keys(row));\n    console.log(\"Row values:\", Object.values(row));\n\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({ Environment: [], Social: [], Governance: [] });\n\n    const xmlLink = getColumnValue(row, [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n    console.log(\"XML Link found:\", xmlLink);\n    console.log(\"Checking each possible column:\");\n    [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"].forEach(col => {\n      console.log(`  ${col}:`, row[col]);\n    });\n\n    if (xmlLink && xmlLink.trim() && xmlLink !== 'No Link') {\n      try {\n        console.log(\"Fetching XML from:\", xmlLink);\n        const res = await axios.get(xmlLink);\n        console.log(\"XML response received, length:\", res.data.length);\n\n        const categories = parseBRSRXML(res.data);\n        console.log(\"Parsed categories:\", categories);\n\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n        alert(\"Error loading XML: \" + err.message);\n      } finally {\n        setLoadingSingle(false);\n      }\n    } else {\n      console.log(\"No valid XML Link found for this company\");\n      console.log(\"xmlLink value:\", `\"${xmlLink}\"`);\n      setLoadingSingle(false);\n      alert(\"No XML Link found for this company\");\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach((cat) => {\n            categories[cat].forEach((d) =>\n              allData.push({\n                ...d,\n                category: cat,\n                companyName: row[\"Company Name\"],\n                year: row[\"Year\"],\n              })\n            );\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Helper function to get column value with flexible naming **/\n  const getColumnValue = (row, possibleNames) => {\n    for (const name of possibleNames) {\n      if (row[name] !== undefined && row[name] !== null && row[name] !== '') {\n        return row[name];\n      }\n    }\n    return '';\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(\n    (row) =>\n      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||\n      row.value.toLowerCase().includes(searchBRSR.toLowerCase())\n  );\n\n  return (\n    <div style={{ padding: '24px', backgroundColor: '#f8f9fa', minHeight: '100vh' }}>\n      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333', marginBottom: '24px', textAlign: 'center' }}>\n        📊 BRSR Report Viewer (Excel + XML)\n      </h1>\n\n      {/* Upload Excel */}\n      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '24px' }}>\n        <input\n          type=\"file\"\n          accept=\".xlsx,.xls,.csv\"\n          onChange={handleExcelUpload}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        {companyData.length > 0 && (\n          <div style={{ marginTop: '8px' }}>\n            <div style={{ color: '#28a745', fontWeight: '500' }}>\n              ✅ {companyData.length} companies loaded\n            </div>\n            <div style={{ fontSize: '12px', color: '#6c757d', marginTop: '4px' }}>\n              Available columns: {Object.keys(companyData[0]).join(', ')}\n            </div>\n          </div>\n        )}\n        <div style={{ marginTop: '8px' }}>\n          <button\n            onClick={() => {\n              // Add sample data for testing\n              const sampleData = [\n                {\n                  \"Company Name\": \"Sample Corp Ltd\",\n                  \"Year\": \"2023\",\n                  \"XML Link\": \"https://httpbin.org/xml\" // This returns sample XML for testing\n                },\n                {\n                  \"Company Name\": \"Test Industries\",\n                  \"Year\": \"2023\",\n                  \"XML Link\": \"https://httpbin.org/xml\"\n                }\n              ];\n              setCompanyData(sampleData);\n              console.log(\"Sample data loaded:\", sampleData);\n            }}\n            style={{\n              padding: '8px 16px',\n              backgroundColor: '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            }}\n          >\n            Load Sample Data (for testing)\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Fetch All */}\n      <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', marginBottom: '16px' }}>\n        <input\n          type=\"text\"\n          placeholder=\"Search company...\"\n          value={searchCompany}\n          onChange={(e) => setSearchCompany(e.target.value)}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        <button\n          onClick={fetchAllCompanies}\n          disabled={loadingAll || companyData.length === 0}\n          style={{\n            padding: '12px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            color: 'white',\n            backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n            cursor: loadingAll ? 'not-allowed' : 'pointer',\n            opacity: loadingAll ? 0.6 : 1\n          }}\n        >\n          {loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"}\n        </button>\n      </div>\n\n      {/* Company Table */}\n      {companyData.length > 0 ? (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '16px',\n          marginBottom: '32px'\n        }}>\n          <DataTable\n            title={`Companies (${companyData.length} loaded)`}\n            columns={[\n              {\n                name: \"Company\",\n                selector: (row) => getColumnValue(row, [\"COMPANY\", \"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]),\n                sortable: true\n              },\n              {\n                name: \"Year\",\n                selector: (row) => getColumnValue(row, [\"FROM YEAR\", \"TO YEAR\", \"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"]),\n                sortable: true\n              },\n              {\n                name: \"XML Link\",\n                selector: (row) => getColumnValue(row, [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]),\n                sortable: true,\n                cell: (row) => {\n                  const link = getColumnValue(row, [\"**XBRL\", \"XBRL\", \"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n                  return link ? (\n                    <a href={link} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#007bff', fontSize: '12px'}}>\n                      {link.length > 30 ? link.substring(0, 30) + '...' : link}\n                    </a>\n                  ) : 'No Link';\n                }\n              },\n              {\n                name: \"Actions\",\n                cell: (row) => (\n                  <button\n                    onClick={() => handleCompanySelect(row)}\n                    style={{\n                      backgroundColor: '#007bff',\n                      color: 'white',\n                      padding: '6px 12px',\n                      border: 'none',\n                      borderRadius: '4px',\n                      cursor: 'pointer',\n                      fontSize: '14px'\n                    }}\n                  >\n                    View BRSR\n                  </button>\n                ),\n              },\n            ]}\n            data={companyData.filter((r) => {\n              const companyName = getColumnValue(r, [\"COMPANY\", \"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]);\n              return companyName.toLowerCase().includes(searchCompany.toLowerCase());\n            })}\n            pagination\n            highlightOnHover\n            dense\n          />\n        </div>\n      ) : (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '32px',\n          marginBottom: '32px',\n          textAlign: 'center',\n          color: '#6c757d'\n        }}>\n          <p>📁 No companies loaded yet. Please upload an Excel file to get started.</p>\n          <p style={{ fontSize: '14px', marginTop: '8px' }}>\n            Expected columns: Company Name, Year, XML Link\n          </p>\n        </div>\n      )}\n\n      {/* Unified Data Table */}\n      {unifiedData.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px',\n          marginBottom: '32px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>Unified BRSR Data</h2>\n          <DataTable\n            columns={[\n              { name: \"Company\", selector: (row) => row.companyName, sortable: true },\n              { name: \"Year\", selector: (row) => row.year, sortable: true },\n              { name: \"Category\", selector: (row) => row.category, sortable: true },\n              { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n              { name: \"Value\", selector: (row) => row.value, sortable: true },\n            ]}\n            data={unifiedData}\n            pagination\n            dense\n            highlightOnHover\n          />\n        </div>\n      )}\n\n      {/* Tabs for single company */}\n      {selectedCompany && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>\n            {getColumnValue(selectedCompany, [\"COMPANY\", \"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"])}\n            ({getColumnValue(selectedCompany, [\"FROM YEAR\", \"TO YEAR\", \"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"])})\n          </h2>\n\n          {/* Tabs */}\n          <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>\n            {[\"Environment\", \"Social\", \"Governance\"].map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                style={{\n                  padding: '8px 16px',\n                  borderRadius: '8px',\n                  border: 'none',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  backgroundColor: activeTab === tab\n                    ? tab === \"Environment\"\n                      ? \"#28a745\"\n                      : tab === \"Social\"\n                      ? \"#6f42c1\"\n                      : \"#fd7e14\"\n                    : \"#e9ecef\",\n                  color: activeTab === tab ? \"white\" : \"#495057\"\n                }}\n              >\n                {tab}\n              </button>\n            ))}\n          </div>\n\n          {/* Search */}\n          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>\n            <input\n              type=\"text\"\n              placeholder={`Search in ${activeTab}...`}\n              value={searchBRSR}\n              onChange={(e) => setSearchBRSR(e.target.value)}\n              style={{\n                width: '320px',\n                padding: '12px',\n                border: '1px solid #ddd',\n                borderRadius: '8px',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }}\n            />\n          </div>\n\n          {/* Table */}\n          {loadingSingle ? (\n            <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>\n              <span style={{ fontSize: '2rem', color: '#007bff' }}>⏳</span>\n            </div>\n          ) : (\n            <DataTable\n              columns={[\n                { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n                { name: \"Value\", selector: (row) => row.value, sortable: true },\n                { name: \"Unit\", selector: (row) => row.unit || \"-\", sortable: true },\n                { name: \"Period\", selector: (row) => row.period, sortable: true },\n              ]}\n              data={filteredTabData}\n              pagination\n              dense\n              highlightOnHover\n            />\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IAAEgB,WAAW,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC;EACzF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC/BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,MAAMC,IAAI,GAAGH,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;MACTF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,IAAI,CAACG,IAAI,EAAEH,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,IAAI,CAAC;IAE9D,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,GAAG,IAAK;MACvB,IAAI;QACFX,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,IAAIW,IAAI;QAER,IAAIV,IAAI,CAACG,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC5C;UACAd,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAClC,MAAMc,OAAO,GAAGJ,GAAG,CAACR,MAAM,CAACa,MAAM;;UAEjC;UACA,MAAMC,YAAY,GAAIC,IAAI,IAAK;YAC7B,MAAMF,MAAM,GAAG,EAAE;YACjB,IAAIG,OAAO,GAAG,EAAE;YAChB,IAAIC,QAAQ,GAAG,KAAK;YAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;cACpC,MAAME,IAAI,GAAGL,IAAI,CAACG,CAAC,CAAC;cAEpB,IAAIE,IAAI,KAAK,GAAG,EAAE;gBAChBH,QAAQ,GAAG,CAACA,QAAQ;cACtB,CAAC,MAAM,IAAIG,IAAI,KAAK,GAAG,IAAI,CAACH,QAAQ,EAAE;gBACpCJ,MAAM,CAACQ,IAAI,CAACL,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC;gBAC3BN,OAAO,GAAG,EAAE;cACd,CAAC,MAAM;gBACLA,OAAO,IAAII,IAAI;cACjB;YACF;YACAP,MAAM,CAACQ,IAAI,CAACL,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC;YAC3B,OAAOT,MAAM;UACf,CAAC;UAED,MAAMU,KAAK,GAAGX,OAAO,CAACY,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACO,IAAI,CAAC,CAAC,CAAC;UAChEzB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyB,KAAK,CAAC,CAAC,CAAC,CAAC;UAExC,MAAMG,OAAO,GAAGZ,YAAY,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;UAC3EzB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4B,OAAO,CAAC;UAClD7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4B,OAAO,CAACP,MAAM,CAAC;UAEjDV,IAAI,GAAG,EAAE;UACT,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,KAAK,CAACJ,MAAM,EAAED,CAAC,EAAE,EAAE;YACrC,MAAMY,MAAM,GAAGhB,YAAY,CAACS,KAAK,CAACL,CAAC,CAAC,CAAC,CAACS,GAAG,CAACI,CAAC,IAAIA,CAAC,CAACF,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;YAC1E,MAAMU,GAAG,GAAG,CAAC,CAAC;YACdN,OAAO,CAACO,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;cACjCH,GAAG,CAACE,MAAM,CAAC,GAAGJ,MAAM,CAACK,KAAK,CAAC,IAAI,EAAE;YACnC,CAAC,CAAC;YACF,IAAIC,MAAM,CAACN,MAAM,CAACE,GAAG,CAAC,CAACK,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC,EAAE;cAAE;cACzC7B,IAAI,CAACY,IAAI,CAACW,GAAG,CAAC;YAChB;UACF;QACF,CAAC,MAAM;UACL;UACAnC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UACpC,MAAMyC,QAAQ,GAAGxE,IAAI,CAACyE,IAAI,CAAChC,GAAG,CAACR,MAAM,CAACa,MAAM,EAAE;YAAEV,IAAI,EAAE;UAAS,CAAC,CAAC;UACjEN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEyC,QAAQ,CAACE,UAAU,CAAC;UAElE,MAAMC,SAAS,GAAGH,QAAQ,CAACE,UAAU,CAAC,CAAC,CAAC;UACxC,MAAME,SAAS,GAAGJ,QAAQ,CAACK,MAAM,CAACF,SAAS,CAAC;UAC5CjC,IAAI,GAAG1C,IAAI,CAAC8E,KAAK,CAACC,aAAa,CAACH,SAAS,CAAC;QAC5C;QAEA9C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEW,IAAI,CAACU,MAAM,EAAE,MAAM,CAAC;QACrDtB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEW,IAAI,CAACsC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE7C;QACA,IAAItC,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UACnBtB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsC,MAAM,CAACY,IAAI,CAACvC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;QAEAlC,cAAc,CAACkC,IAAI,CAAC;MACtB,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdpD,OAAO,CAACoD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CC,KAAK,CAAC,sBAAsB,GAAGD,KAAK,CAACE,OAAO,CAAC;MAC/C;IACF,CAAC;IAED9C,MAAM,CAAC+C,OAAO,GAAIH,KAAK,IAAK;MAC1BpD,OAAO,CAACoD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,KAAK,CAAC,oBAAoB,CAAC;IAC7B,CAAC;;IAED;IACA,IAAInD,IAAI,CAACG,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC5CN,MAAM,CAACgD,UAAU,CAACtD,IAAI,CAAC;IACzB,CAAC,MAAM;MACLM,MAAM,CAACiD,kBAAkB,CAACvD,IAAI,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMwD,eAAe,GAAIrD,IAAI,IAAK;IAChC,MAAMsD,KAAK,GAAGtD,IAAI,CAACQ,WAAW,CAAC,CAAC;IAChC,IAAI8C,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,EAC9G,OAAO,aAAa;IACtB,IAAID,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,KAAK,CAAC,EAChH,OAAO,QAAQ;IACjB,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;IAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,OAAO,EAAE,UAAU,CAAC;IAC1D,MAAMK,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,MAAMC,eAAe,GAAGJ,MAAM,CAACK,oBAAoB,CAAC,eAAe,CAAC;IACpEC,KAAK,CAACC,IAAI,CAACH,eAAe,CAAC,CAACjC,OAAO,CAAEqC,GAAG,IAAK;MAC3C,MAAMC,EAAE,GAAGD,GAAG,CAACE,YAAY,CAAC,IAAI,CAAC;MACjC,MAAMC,aAAa,GAAGH,GAAG,CAACH,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACrE,MAAMO,gBAAgB,GAAGJ,GAAG,CAACH,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMQ,cAAc,GAAGL,GAAG,CAACH,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MAEnEH,QAAQ,CAACO,EAAE,CAAC,GAAG;QACbK,MAAM,EAAEH,aAAa,GAAGA,aAAa,CAACI,WAAW,GAAG,EAAE;QACtDC,SAAS,EAAEJ,gBAAgB,GAAGA,gBAAgB,CAACG,WAAW,GAAG,EAAE;QAC/DE,OAAO,EAAEJ,cAAc,GAAGA,cAAc,CAACE,WAAW,GAAG;MACzD,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMG,YAAY,GAAGlB,MAAM,CAACK,oBAAoB,CAAC,YAAY,CAAC;IAC9DC,KAAK,CAACC,IAAI,CAACW,YAAY,CAAC,CAAC/C,OAAO,CAAEgD,CAAC,IAAK;MACtC,MAAMV,EAAE,GAAGU,CAAC,CAACT,YAAY,CAAC,IAAI,CAAC;MAC/B,MAAMU,cAAc,GAAGD,CAAC,CAACd,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACjEF,KAAK,CAACM,EAAE,CAAC,GAAGW,cAAc,GAAGA,cAAc,CAACL,WAAW,GAAG,EAAE;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,UAAU,GAAG;MAAErG,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;;IAElE;IACA,MAAMoG,WAAW,GAAGtB,MAAM,CAACK,oBAAoB,CAAC,GAAG,CAAC;IACpDC,KAAK,CAACC,IAAI,CAACe,WAAW,CAAC,CAACnD,OAAO,CAAEoD,OAAO,IAAK;MAC3C,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO;MAC/B,IAAIA,OAAO,IAAIA,OAAO,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC/C,MAAMC,UAAU,GAAGH,OAAO,CAACb,YAAY,CAAC,YAAY,CAAC;QACrD,MAAMiB,OAAO,GAAGJ,OAAO,CAACb,YAAY,CAAC,SAAS,CAAC;QAC/C,MAAMkB,OAAO,GAAG1B,QAAQ,CAACwB,UAAU,CAAC;QACpC,MAAMG,IAAI,GAAG1B,KAAK,CAACwB,OAAO,CAAC;QAC3B,MAAMG,QAAQ,GAAGrC,eAAe,CAAC+B,OAAO,CAAC;QAEzCH,UAAU,CAACS,QAAQ,CAAC,CAACvE,IAAI,CAAC;UACxBwE,MAAM,EAAEP,OAAO,CAACzD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;UACzCiE,KAAK,EAAET,OAAO,CAACR,WAAW,IAAI,EAAE;UAChCc,IAAI,EAAEA,IAAI,IAAI,IAAI;UAClBI,MAAM,EAAEL,OAAO,GAAG,GAAGA,OAAO,CAACZ,SAAS,MAAMY,OAAO,CAACX,OAAO,EAAE,GAAG,EAAE;UAClEiB,OAAO,EAAE,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEd,MAAM,KAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAG,MAAOjE,GAAG,IAAK;IACzCnC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkC,GAAG,CAAC;IACrCnC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEsC,MAAM,CAACY,IAAI,CAAChB,GAAG,CAAC,CAAC;IAC9CnC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEsC,MAAM,CAACN,MAAM,CAACE,GAAG,CAAC,CAAC;IAE9CrD,kBAAkB,CAACqD,GAAG,CAAC;IACvBtC,gBAAgB,CAAC,IAAI,CAAC;IACtBb,WAAW,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC,CAAC;IAE5D,MAAMkH,OAAO,GAAGC,cAAc,CAACnE,GAAG,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAChHnC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoG,OAAO,CAAC;IACvCrG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAACmC,OAAO,CAACmE,GAAG,IAAI;MACzFvG,OAAO,CAACC,GAAG,CAAC,KAAKsG,GAAG,GAAG,EAAEpE,GAAG,CAACoE,GAAG,CAAC,CAAC;IACpC,CAAC,CAAC;IAEF,IAAIF,OAAO,IAAIA,OAAO,CAAC5E,IAAI,CAAC,CAAC,IAAI4E,OAAO,KAAK,SAAS,EAAE;MACtD,IAAI;QACFrG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoG,OAAO,CAAC;QAC1C,MAAMG,GAAG,GAAG,MAAMrI,KAAK,CAACsI,GAAG,CAACJ,OAAO,CAAC;QACpCrG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuG,GAAG,CAACE,IAAI,CAACpF,MAAM,CAAC;QAE9D,MAAMgE,UAAU,GAAGzB,YAAY,CAAC2C,GAAG,CAACE,IAAI,CAAC;QACzC1G,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqF,UAAU,CAAC;QAE7CtG,WAAW,CAACsG,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOqB,GAAG,EAAE;QACZ3G,OAAO,CAACoD,KAAK,CAAC,oBAAoB,EAAEuD,GAAG,CAAC;QACxCtD,KAAK,CAAC,qBAAqB,GAAGsD,GAAG,CAACrD,OAAO,CAAC;MAC5C,CAAC,SAAS;QACRzD,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvDD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAIoG,OAAO,GAAG,CAAC;MAC7CxG,gBAAgB,CAAC,KAAK,CAAC;MACvBwD,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMuD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCjH,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMkH,OAAO,GAAG,EAAE;IAClB,KAAK,MAAM1E,GAAG,IAAI1D,WAAW,EAAE;MAC7B,IAAI0D,GAAG,CAAC,UAAU,CAAC,EAAE;QACnB,IAAI;UACF,MAAMqE,GAAG,GAAG,MAAMrI,KAAK,CAACsI,GAAG,CAACtE,GAAG,CAAC,UAAU,CAAC,CAAC;UAC5C,MAAMmD,UAAU,GAAGzB,YAAY,CAAC2C,GAAG,CAACE,IAAI,CAAC;UACzC,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACtE,OAAO,CAAE0E,GAAG,IAAK;YACvDxB,UAAU,CAACwB,GAAG,CAAC,CAAC1E,OAAO,CAAE2E,CAAC,IACxBF,OAAO,CAACrF,IAAI,CAAC;cACX,GAAGuF,CAAC;cACJhB,QAAQ,EAAEe,GAAG;cACbE,WAAW,EAAE7E,GAAG,CAAC,cAAc,CAAC;cAChC8E,IAAI,EAAE9E,GAAG,CAAC,MAAM;YAClB,CAAC,CACH,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOwE,GAAG,EAAE;UACZ3G,OAAO,CAACkH,IAAI,CAAC,kBAAkB,EAAE/E,GAAG,CAAC,cAAc,CAAC,CAAC;QACvD;MACF;IACF;IACA1C,cAAc,CAACoH,OAAO,CAAC;IACvBlH,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAM2G,cAAc,GAAGA,CAACnE,GAAG,EAAEgF,aAAa,KAAK;IAC7C,KAAK,MAAM9G,IAAI,IAAI8G,aAAa,EAAE;MAChC,IAAIhF,GAAG,CAAC9B,IAAI,CAAC,KAAK+G,SAAS,IAAIjF,GAAG,CAAC9B,IAAI,CAAC,KAAK,IAAI,IAAI8B,GAAG,CAAC9B,IAAI,CAAC,KAAK,EAAE,EAAE;QACrE,OAAO8B,GAAG,CAAC9B,IAAI,CAAC;MAClB;IACF;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMgH,eAAe,GAAGtI,QAAQ,CAACO,SAAS,CAAC,CAACsC,MAAM,CAC/CO,GAAG,IACFA,GAAG,CAAC6D,MAAM,CAACnF,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACxE,UAAU,CAACyB,WAAW,CAAC,CAAC,CAAC,IAC3DsB,GAAG,CAAC8D,KAAK,CAACpF,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACxE,UAAU,CAACyB,WAAW,CAAC,CAAC,CAC7D,CAAC;EAED,oBACEvC,OAAA;IAAKgJ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC9EpJ,OAAA;MAAIgJ,KAAK,EAAE;QAAEK,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,YAAY,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAE/G;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGL7J,OAAA;MAAKgJ,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAER,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACnGpJ,OAAA;QACEgC,IAAI,EAAC,MAAM;QACXiI,MAAM,EAAC,iBAAiB;QACxBC,QAAQ,EAAE1I,iBAAkB;QAC5BwH,KAAK,EAAE;UACLmB,KAAK,EAAE,OAAO;UACdlB,OAAO,EAAE,MAAM;UACfmB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD1J,WAAW,CAAC6C,MAAM,GAAG,CAAC,iBACrBhD,OAAA;QAAKgJ,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,gBAC/BpJ,OAAA;UAAKgJ,KAAK,EAAE;YAAEO,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAM,CAAE;UAAAF,QAAA,GAAC,SACjD,EAACjJ,WAAW,CAAC6C,MAAM,EAAC,mBACxB;QAAA;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7J,OAAA;UAAKgJ,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE,SAAS;YAAEgB,SAAS,EAAE;UAAM,CAAE;UAAAnB,QAAA,GAAC,qBACjD,EAACnF,MAAM,CAACY,IAAI,CAAC1E,WAAW,CAAC,CAAC,CAAC,CAAC,CAACqK,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACD7J,OAAA;QAAKgJ,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,eAC/BpJ,OAAA;UACEyK,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,MAAMC,UAAU,GAAG,CACjB;cACE,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,MAAM;cACd,UAAU,EAAE,yBAAyB,CAAC;YACxC,CAAC,EACD;cACE,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,MAAM;cACd,UAAU,EAAE;YACd,CAAC,CACF;YACDtK,cAAc,CAACsK,UAAU,CAAC;YAC1BhJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+I,UAAU,CAAC;UAChD,CAAE;UACF1B,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBC,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACda,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBM,MAAM,EAAE,SAAS;YACjBtB,QAAQ,EAAE;UACZ,CAAE;UAAAD,QAAA,EACH;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7J,OAAA;MAAKgJ,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEc,cAAc,EAAE,QAAQ;QAAEC,GAAG,EAAE,MAAM;QAAErB,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAC3FpJ,OAAA;QACEgC,IAAI,EAAC,MAAM;QACX8I,WAAW,EAAC,mBAAmB;QAC/BnD,KAAK,EAAEtH,aAAc;QACrB6J,QAAQ,EAAGzI,CAAC,IAAKnB,gBAAgB,CAACmB,CAAC,CAACI,MAAM,CAAC8F,KAAK,CAAE;QAClDqB,KAAK,EAAE;UACLmB,KAAK,EAAE,OAAO;UACdlB,OAAO,EAAE,MAAM;UACfmB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF7J,OAAA;QACEyK,OAAO,EAAEnC,iBAAkB;QAC3ByC,QAAQ,EAAE3J,UAAU,IAAIjB,WAAW,CAAC6C,MAAM,KAAK,CAAE;QACjDgG,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBoB,YAAY,EAAE,KAAK;UACnBD,MAAM,EAAE,MAAM;UACdb,KAAK,EAAE,OAAO;UACdL,eAAe,EAAE9H,UAAU,GAAG,SAAS,GAAG,SAAS;UACnDuJ,MAAM,EAAEvJ,UAAU,GAAG,aAAa,GAAG,SAAS;UAC9C4J,OAAO,EAAE5J,UAAU,GAAG,GAAG,GAAG;QAC9B,CAAE;QAAAgI,QAAA,EAEDhI,UAAU,GAAG,iBAAiB,GAAG;MAAqB;QAAAsI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL1J,WAAW,CAAC6C,MAAM,GAAG,CAAC,gBACrBhD,OAAA;MAAKgJ,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,eACApJ,OAAA,CAACF,SAAS;QACRmL,KAAK,EAAE,cAAc9K,WAAW,CAAC6C,MAAM,UAAW;QAClDkI,OAAO,EAAE,CACP;UACEnJ,IAAI,EAAE,SAAS;UACfoJ,QAAQ,EAAGtH,GAAG,IAAKmE,cAAc,CAACnE,GAAG,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;UAChIuH,QAAQ,EAAE;QACZ,CAAC,EACD;UACErJ,IAAI,EAAE,MAAM;UACZoJ,QAAQ,EAAGtH,GAAG,IAAKmE,cAAc,CAACnE,GAAG,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;UACpGuH,QAAQ,EAAE;QACZ,CAAC,EACD;UACErJ,IAAI,EAAE,UAAU;UAChBoJ,QAAQ,EAAGtH,GAAG,IAAKmE,cAAc,CAACnE,GAAG,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;UACnHuH,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAGxH,GAAG,IAAK;YACb,MAAMyH,IAAI,GAAGtD,cAAc,CAACnE,GAAG,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC7G,OAAOyH,IAAI,gBACTtL,OAAA;cAAGuL,IAAI,EAAED,IAAK;cAACzJ,MAAM,EAAC,QAAQ;cAAC2J,GAAG,EAAC,qBAAqB;cAACxC,KAAK,EAAE;gBAACO,KAAK,EAAE,SAAS;gBAAEF,QAAQ,EAAE;cAAM,CAAE;cAAAD,QAAA,EAClGkC,IAAI,CAACtI,MAAM,GAAG,EAAE,GAAGsI,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGH;YAAI;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,GACF,SAAS;UACf;QACF,CAAC,EACD;UACE9H,IAAI,EAAE,SAAS;UACfsJ,IAAI,EAAGxH,GAAG,iBACR7D,OAAA;YACEyK,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAACjE,GAAG,CAAE;YACxCmF,KAAK,EAAE;cACLE,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,OAAO;cACdN,OAAO,EAAE,UAAU;cACnBmB,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBM,MAAM,EAAE,SAAS;cACjBtB,QAAQ,EAAE;YACZ,CAAE;YAAAD,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAEZ,CAAC,CACD;QACFzB,IAAI,EAAEjI,WAAW,CAACmD,MAAM,CAAEoI,CAAC,IAAK;UAC9B,MAAMhD,WAAW,GAAGV,cAAc,CAAC0D,CAAC,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;UAC/H,OAAOhD,WAAW,CAACnG,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACjF,aAAa,CAACkC,WAAW,CAAC,CAAC,CAAC;QACxE,CAAC,CAAE;QACHoJ,UAAU;QACVC,gBAAgB;QAChBC,KAAK;MAAA;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAEN7J,OAAA;MAAKgJ,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE;MACT,CAAE;MAAAH,QAAA,gBACApJ,OAAA;QAAAoJ,QAAA,EAAG;MAAuE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9E7J,OAAA;QAAGgJ,KAAK,EAAE;UAAEK,QAAQ,EAAE,MAAM;UAAEkB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,EAAC;MAElD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGA3I,WAAW,CAAC8B,MAAM,GAAG,CAAC,iBACrBhD,OAAA;MAAKgJ,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,gBACApJ,OAAA;QAAIgJ,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClG7J,OAAA,CAACF,SAAS;QACRoL,OAAO,EAAE,CACP;UAAEnJ,IAAI,EAAE,SAAS;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC6E,WAAW;UAAE0C,QAAQ,EAAE;QAAK,CAAC,EACvE;UAAErJ,IAAI,EAAE,MAAM;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC8E,IAAI;UAAEyC,QAAQ,EAAE;QAAK,CAAC,EAC7D;UAAErJ,IAAI,EAAE,UAAU;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC4D,QAAQ;UAAE2D,QAAQ,EAAE;QAAK,CAAC,EACrE;UAAErJ,IAAI,EAAE,QAAQ;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC6D,MAAM;UAAE0D,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAErJ,IAAI,EAAE,OAAO;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC8D,KAAK;UAAEyD,QAAQ,EAAE;QAAK,CAAC,CAC/D;QACFhD,IAAI,EAAElH,WAAY;QAClByK,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAtJ,eAAe,iBACdP,OAAA;MAAKgJ,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE;MACX,CAAE;MAAAG,QAAA,gBACApJ,OAAA;QAAIgJ,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,GACxEpB,cAAc,CAACzH,eAAe,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,EAAC,GAC1H,EAACyH,cAAc,CAACzH,eAAe,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAC,GAClG;MAAA;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGL7J,OAAA;QAAKgJ,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE,MAAM;UAAErB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAChE,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC5F,GAAG,CAAEsI,GAAG,iBAC/C9L,OAAA;UAEEyK,OAAO,EAAEA,CAAA,KAAMxJ,YAAY,CAAC6K,GAAG,CAAE;UACjC9C,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBoB,YAAY,EAAE,KAAK;YACnBD,MAAM,EAAE,MAAM;YACdd,UAAU,EAAE,KAAK;YACjBqB,MAAM,EAAE,SAAS;YACjBzB,eAAe,EAAElI,SAAS,KAAK8K,GAAG,GAC9BA,GAAG,KAAK,aAAa,GACnB,SAAS,GACTA,GAAG,KAAK,QAAQ,GAChB,SAAS,GACT,SAAS,GACX,SAAS;YACbvC,KAAK,EAAEvI,SAAS,KAAK8K,GAAG,GAAG,OAAO,GAAG;UACvC,CAAE;UAAA1C,QAAA,EAED0C;QAAG,GAlBCA,GAAG;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN7J,OAAA;QAAKgJ,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEc,cAAc,EAAE,QAAQ;UAAEpB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC9EpJ,OAAA;UACEgC,IAAI,EAAC,MAAM;UACX8I,WAAW,EAAE,aAAa9J,SAAS,KAAM;UACzC2G,KAAK,EAAE7G,UAAW;UAClBoJ,QAAQ,EAAGzI,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACI,MAAM,CAAC8F,KAAK,CAAE;UAC/CqB,KAAK,EAAE;YACLmB,KAAK,EAAE,OAAO;YACdlB,OAAO,EAAE,MAAM;YACfmB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACb;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLvI,aAAa,gBACZtB,OAAA;QAAKgJ,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEc,cAAc,EAAE,QAAQ;UAAE3B,OAAO,EAAE;QAAS,CAAE;QAAAG,QAAA,eAC3EpJ,OAAA;UAAMgJ,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAEN7J,OAAA,CAACF,SAAS;QACRoL,OAAO,EAAE,CACP;UAAEnJ,IAAI,EAAE,QAAQ;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC6D,MAAM;UAAE0D,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAErJ,IAAI,EAAE,OAAO;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC8D,KAAK;UAAEyD,QAAQ,EAAE;QAAK,CAAC,EAC/D;UAAErJ,IAAI,EAAE,MAAM;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC2D,IAAI,IAAI,GAAG;UAAE4D,QAAQ,EAAE;QAAK,CAAC,EACpE;UAAErJ,IAAI,EAAE,QAAQ;UAAEoJ,QAAQ,EAAGtH,GAAG,IAAKA,GAAG,CAAC+D,MAAM;UAAEwD,QAAQ,EAAE;QAAK,CAAC,CACjE;QACFhD,IAAI,EAAEW,eAAgB;QACtB4C,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC3J,EAAA,CApiBuBD,UAAU;AAAA8L,EAAA,GAAV9L,UAAU;AAAA,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}