{"ast": null, "code": ";\n(function (sax) {\n  // wrapper for non-node envs\n  sax.parser = function (strict, opt) {\n    return new SAXParser(strict, opt);\n  };\n  sax.SAXParser = SAXParser;\n  sax.SAXStream = SAXStream;\n  sax.createStream = createStream;\n\n  // When we pass the MAX_BUFFER_LENGTH position, start checking for buffer overruns.\n  // When we check, schedule the next check for MAX_BUFFER_LENGTH - (max(buffer lengths)),\n  // since that's the earliest that a buffer overrun could occur.  This way, checks are\n  // as rare as required, but as often as necessary to ensure never crossing this bound.\n  // Furthermore, buffers are only tested at most once per write(), so passing a very\n  // large string into write() might have undesirable effects, but this is manageable by\n  // the caller, so it is assumed to be safe.  Thus, a call to write() may, in the extreme\n  // edge case, result in creating at most one complete copy of the string passed in.\n  // Set to Infinity to have unlimited buffers.\n  sax.MAX_BUFFER_LENGTH = 64 * 1024;\n  var buffers = ['comment', 'sgmlDecl', 'textNode', 'tagName', 'doctype', 'procInstName', 'procInstBody', 'entity', 'attribName', 'attribValue', 'cdata', 'script'];\n  sax.EVENTS = ['text', 'processinginstruction', 'sgmldeclaration', 'doctype', 'comment', 'opentagstart', 'attribute', 'opentag', 'closetag', 'opencdata', 'cdata', 'closecdata', 'error', 'end', 'ready', 'script', 'opennamespace', 'closenamespace'];\n  function SAXParser(strict, opt) {\n    if (!(this instanceof SAXParser)) {\n      return new SAXParser(strict, opt);\n    }\n    var parser = this;\n    clearBuffers(parser);\n    parser.q = parser.c = '';\n    parser.bufferCheckPosition = sax.MAX_BUFFER_LENGTH;\n    parser.opt = opt || {};\n    parser.opt.lowercase = parser.opt.lowercase || parser.opt.lowercasetags;\n    parser.looseCase = parser.opt.lowercase ? 'toLowerCase' : 'toUpperCase';\n    parser.tags = [];\n    parser.closed = parser.closedRoot = parser.sawRoot = false;\n    parser.tag = parser.error = null;\n    parser.strict = !!strict;\n    parser.noscript = !!(strict || parser.opt.noscript);\n    parser.state = S.BEGIN;\n    parser.strictEntities = parser.opt.strictEntities;\n    parser.ENTITIES = parser.strictEntities ? Object.create(sax.XML_ENTITIES) : Object.create(sax.ENTITIES);\n    parser.attribList = [];\n\n    // namespaces form a prototype chain.\n    // it always points at the current tag,\n    // which protos to its parent tag.\n    if (parser.opt.xmlns) {\n      parser.ns = Object.create(rootNS);\n    }\n\n    // mostly just for error reporting\n    parser.trackPosition = parser.opt.position !== false;\n    if (parser.trackPosition) {\n      parser.position = parser.line = parser.column = 0;\n    }\n    emit(parser, 'onready');\n  }\n  if (!Object.create) {\n    Object.create = function (o) {\n      function F() {}\n      F.prototype = o;\n      var newf = new F();\n      return newf;\n    };\n  }\n  if (!Object.keys) {\n    Object.keys = function (o) {\n      var a = [];\n      for (var i in o) if (o.hasOwnProperty(i)) a.push(i);\n      return a;\n    };\n  }\n  function checkBufferLength(parser) {\n    var maxAllowed = Math.max(sax.MAX_BUFFER_LENGTH, 10);\n    var maxActual = 0;\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      var len = parser[buffers[i]].length;\n      if (len > maxAllowed) {\n        // Text/cdata nodes can get big, and since they're buffered,\n        // we can get here under normal conditions.\n        // Avoid issues by emitting the text node now,\n        // so at least it won't get any bigger.\n        switch (buffers[i]) {\n          case 'textNode':\n            closeText(parser);\n            break;\n          case 'cdata':\n            emitNode(parser, 'oncdata', parser.cdata);\n            parser.cdata = '';\n            break;\n          case 'script':\n            emitNode(parser, 'onscript', parser.script);\n            parser.script = '';\n            break;\n          default:\n            error(parser, 'Max buffer length exceeded: ' + buffers[i]);\n        }\n      }\n      maxActual = Math.max(maxActual, len);\n    }\n    // schedule the next check for the earliest possible buffer overrun.\n    var m = sax.MAX_BUFFER_LENGTH - maxActual;\n    parser.bufferCheckPosition = m + parser.position;\n  }\n  function clearBuffers(parser) {\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      parser[buffers[i]] = '';\n    }\n  }\n  function flushBuffers(parser) {\n    closeText(parser);\n    if (parser.cdata !== '') {\n      emitNode(parser, 'oncdata', parser.cdata);\n      parser.cdata = '';\n    }\n    if (parser.script !== '') {\n      emitNode(parser, 'onscript', parser.script);\n      parser.script = '';\n    }\n  }\n  SAXParser.prototype = {\n    end: function () {\n      end(this);\n    },\n    write: write,\n    resume: function () {\n      this.error = null;\n      return this;\n    },\n    close: function () {\n      return this.write(null);\n    },\n    flush: function () {\n      flushBuffers(this);\n    }\n  };\n  var Stream;\n  try {\n    Stream = require('stream').Stream;\n  } catch (ex) {\n    Stream = function () {};\n  }\n  var streamWraps = sax.EVENTS.filter(function (ev) {\n    return ev !== 'error' && ev !== 'end';\n  });\n  function createStream(strict, opt) {\n    return new SAXStream(strict, opt);\n  }\n  function SAXStream(strict, opt) {\n    if (!(this instanceof SAXStream)) {\n      return new SAXStream(strict, opt);\n    }\n    Stream.apply(this);\n    this._parser = new SAXParser(strict, opt);\n    this.writable = true;\n    this.readable = true;\n    var me = this;\n    this._parser.onend = function () {\n      me.emit('end');\n    };\n    this._parser.onerror = function (er) {\n      me.emit('error', er);\n\n      // if didn't throw, then means error was handled.\n      // go ahead and clear error, so we can write again.\n      me._parser.error = null;\n    };\n    this._decoder = null;\n    streamWraps.forEach(function (ev) {\n      Object.defineProperty(me, 'on' + ev, {\n        get: function () {\n          return me._parser['on' + ev];\n        },\n        set: function (h) {\n          if (!h) {\n            me.removeAllListeners(ev);\n            me._parser['on' + ev] = h;\n            return h;\n          }\n          me.on(ev, h);\n        },\n        enumerable: true,\n        configurable: false\n      });\n    });\n  }\n  SAXStream.prototype = Object.create(Stream.prototype, {\n    constructor: {\n      value: SAXStream\n    }\n  });\n  SAXStream.prototype.write = function (data) {\n    if (typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function' && Buffer.isBuffer(data)) {\n      if (!this._decoder) {\n        var SD = require('string_decoder').StringDecoder;\n        this._decoder = new SD('utf8');\n      }\n      data = this._decoder.write(data);\n    }\n    this._parser.write(data.toString());\n    this.emit('data', data);\n    return true;\n  };\n  SAXStream.prototype.end = function (chunk) {\n    if (chunk && chunk.length) {\n      this.write(chunk);\n    }\n    this._parser.end();\n    return true;\n  };\n  SAXStream.prototype.on = function (ev, handler) {\n    var me = this;\n    if (!me._parser['on' + ev] && streamWraps.indexOf(ev) !== -1) {\n      me._parser['on' + ev] = function () {\n        var args = arguments.length === 1 ? [arguments[0]] : Array.apply(null, arguments);\n        args.splice(0, 0, ev);\n        me.emit.apply(me, args);\n      };\n    }\n    return Stream.prototype.on.call(me, ev, handler);\n  };\n\n  // this really needs to be replaced with character classes.\n  // XML allows all manner of ridiculous numbers and digits.\n  var CDATA = '[CDATA[';\n  var DOCTYPE = 'DOCTYPE';\n  var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace';\n  var XMLNS_NAMESPACE = 'http://www.w3.org/2000/xmlns/';\n  var rootNS = {\n    xml: XML_NAMESPACE,\n    xmlns: XMLNS_NAMESPACE\n  };\n\n  // http://www.w3.org/TR/REC-xml/#NT-NameStartChar\n  // This implementation works on strings, a single character at a time\n  // as such, it cannot ever support astral-plane characters (10000-EFFFF)\n  // without a significant breaking change to either this  parser, or the\n  // JavaScript language.  Implementation of an emoji-capable xml parser\n  // is left as an exercise for the reader.\n  var nameStart = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/;\n  var nameBody = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/;\n  var entityStart = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/;\n  var entityBody = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/;\n  function isWhitespace(c) {\n    return c === ' ' || c === '\\n' || c === '\\r' || c === '\\t';\n  }\n  function isQuote(c) {\n    return c === '\"' || c === '\\'';\n  }\n  function isAttribEnd(c) {\n    return c === '>' || isWhitespace(c);\n  }\n  function isMatch(regex, c) {\n    return regex.test(c);\n  }\n  function notMatch(regex, c) {\n    return !isMatch(regex, c);\n  }\n  var S = 0;\n  sax.STATE = {\n    BEGIN: S++,\n    // leading byte order mark or whitespace\n    BEGIN_WHITESPACE: S++,\n    // leading whitespace\n    TEXT: S++,\n    // general stuff\n    TEXT_ENTITY: S++,\n    // &amp and such.\n    OPEN_WAKA: S++,\n    // <\n    SGML_DECL: S++,\n    // <!BLARG\n    SGML_DECL_QUOTED: S++,\n    // <!BLARG foo \"bar\n    DOCTYPE: S++,\n    // <!DOCTYPE\n    DOCTYPE_QUOTED: S++,\n    // <!DOCTYPE \"//blah\n    DOCTYPE_DTD: S++,\n    // <!DOCTYPE \"//blah\" [ ...\n    DOCTYPE_DTD_QUOTED: S++,\n    // <!DOCTYPE \"//blah\" [ \"foo\n    COMMENT_STARTING: S++,\n    // <!-\n    COMMENT: S++,\n    // <!--\n    COMMENT_ENDING: S++,\n    // <!-- blah -\n    COMMENT_ENDED: S++,\n    // <!-- blah --\n    CDATA: S++,\n    // <![CDATA[ something\n    CDATA_ENDING: S++,\n    // ]\n    CDATA_ENDING_2: S++,\n    // ]]\n    PROC_INST: S++,\n    // <?hi\n    PROC_INST_BODY: S++,\n    // <?hi there\n    PROC_INST_ENDING: S++,\n    // <?hi \"there\" ?\n    OPEN_TAG: S++,\n    // <strong\n    OPEN_TAG_SLASH: S++,\n    // <strong /\n    ATTRIB: S++,\n    // <a\n    ATTRIB_NAME: S++,\n    // <a foo\n    ATTRIB_NAME_SAW_WHITE: S++,\n    // <a foo _\n    ATTRIB_VALUE: S++,\n    // <a foo=\n    ATTRIB_VALUE_QUOTED: S++,\n    // <a foo=\"bar\n    ATTRIB_VALUE_CLOSED: S++,\n    // <a foo=\"bar\"\n    ATTRIB_VALUE_UNQUOTED: S++,\n    // <a foo=bar\n    ATTRIB_VALUE_ENTITY_Q: S++,\n    // <foo bar=\"&quot;\"\n    ATTRIB_VALUE_ENTITY_U: S++,\n    // <foo bar=&quot\n    CLOSE_TAG: S++,\n    // </a\n    CLOSE_TAG_SAW_WHITE: S++,\n    // </a   >\n    SCRIPT: S++,\n    // <script> ...\n    SCRIPT_ENDING: S++ // <script> ... <\n  };\n  sax.XML_ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\"\n  };\n  sax.ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\",\n    'AElig': 198,\n    'Aacute': 193,\n    'Acirc': 194,\n    'Agrave': 192,\n    'Aring': 197,\n    'Atilde': 195,\n    'Auml': 196,\n    'Ccedil': 199,\n    'ETH': 208,\n    'Eacute': 201,\n    'Ecirc': 202,\n    'Egrave': 200,\n    'Euml': 203,\n    'Iacute': 205,\n    'Icirc': 206,\n    'Igrave': 204,\n    'Iuml': 207,\n    'Ntilde': 209,\n    'Oacute': 211,\n    'Ocirc': 212,\n    'Ograve': 210,\n    'Oslash': 216,\n    'Otilde': 213,\n    'Ouml': 214,\n    'THORN': 222,\n    'Uacute': 218,\n    'Ucirc': 219,\n    'Ugrave': 217,\n    'Uuml': 220,\n    'Yacute': 221,\n    'aacute': 225,\n    'acirc': 226,\n    'aelig': 230,\n    'agrave': 224,\n    'aring': 229,\n    'atilde': 227,\n    'auml': 228,\n    'ccedil': 231,\n    'eacute': 233,\n    'ecirc': 234,\n    'egrave': 232,\n    'eth': 240,\n    'euml': 235,\n    'iacute': 237,\n    'icirc': 238,\n    'igrave': 236,\n    'iuml': 239,\n    'ntilde': 241,\n    'oacute': 243,\n    'ocirc': 244,\n    'ograve': 242,\n    'oslash': 248,\n    'otilde': 245,\n    'ouml': 246,\n    'szlig': 223,\n    'thorn': 254,\n    'uacute': 250,\n    'ucirc': 251,\n    'ugrave': 249,\n    'uuml': 252,\n    'yacute': 253,\n    'yuml': 255,\n    'copy': 169,\n    'reg': 174,\n    'nbsp': 160,\n    'iexcl': 161,\n    'cent': 162,\n    'pound': 163,\n    'curren': 164,\n    'yen': 165,\n    'brvbar': 166,\n    'sect': 167,\n    'uml': 168,\n    'ordf': 170,\n    'laquo': 171,\n    'not': 172,\n    'shy': 173,\n    'macr': 175,\n    'deg': 176,\n    'plusmn': 177,\n    'sup1': 185,\n    'sup2': 178,\n    'sup3': 179,\n    'acute': 180,\n    'micro': 181,\n    'para': 182,\n    'middot': 183,\n    'cedil': 184,\n    'ordm': 186,\n    'raquo': 187,\n    'frac14': 188,\n    'frac12': 189,\n    'frac34': 190,\n    'iquest': 191,\n    'times': 215,\n    'divide': 247,\n    'OElig': 338,\n    'oelig': 339,\n    'Scaron': 352,\n    'scaron': 353,\n    'Yuml': 376,\n    'fnof': 402,\n    'circ': 710,\n    'tilde': 732,\n    'Alpha': 913,\n    'Beta': 914,\n    'Gamma': 915,\n    'Delta': 916,\n    'Epsilon': 917,\n    'Zeta': 918,\n    'Eta': 919,\n    'Theta': 920,\n    'Iota': 921,\n    'Kappa': 922,\n    'Lambda': 923,\n    'Mu': 924,\n    'Nu': 925,\n    'Xi': 926,\n    'Omicron': 927,\n    'Pi': 928,\n    'Rho': 929,\n    'Sigma': 931,\n    'Tau': 932,\n    'Upsilon': 933,\n    'Phi': 934,\n    'Chi': 935,\n    'Psi': 936,\n    'Omega': 937,\n    'alpha': 945,\n    'beta': 946,\n    'gamma': 947,\n    'delta': 948,\n    'epsilon': 949,\n    'zeta': 950,\n    'eta': 951,\n    'theta': 952,\n    'iota': 953,\n    'kappa': 954,\n    'lambda': 955,\n    'mu': 956,\n    'nu': 957,\n    'xi': 958,\n    'omicron': 959,\n    'pi': 960,\n    'rho': 961,\n    'sigmaf': 962,\n    'sigma': 963,\n    'tau': 964,\n    'upsilon': 965,\n    'phi': 966,\n    'chi': 967,\n    'psi': 968,\n    'omega': 969,\n    'thetasym': 977,\n    'upsih': 978,\n    'piv': 982,\n    'ensp': 8194,\n    'emsp': 8195,\n    'thinsp': 8201,\n    'zwnj': 8204,\n    'zwj': 8205,\n    'lrm': 8206,\n    'rlm': 8207,\n    'ndash': 8211,\n    'mdash': 8212,\n    'lsquo': 8216,\n    'rsquo': 8217,\n    'sbquo': 8218,\n    'ldquo': 8220,\n    'rdquo': 8221,\n    'bdquo': 8222,\n    'dagger': 8224,\n    'Dagger': 8225,\n    'bull': 8226,\n    'hellip': 8230,\n    'permil': 8240,\n    'prime': 8242,\n    'Prime': 8243,\n    'lsaquo': 8249,\n    'rsaquo': 8250,\n    'oline': 8254,\n    'frasl': 8260,\n    'euro': 8364,\n    'image': 8465,\n    'weierp': 8472,\n    'real': 8476,\n    'trade': 8482,\n    'alefsym': 8501,\n    'larr': 8592,\n    'uarr': 8593,\n    'rarr': 8594,\n    'darr': 8595,\n    'harr': 8596,\n    'crarr': 8629,\n    'lArr': 8656,\n    'uArr': 8657,\n    'rArr': 8658,\n    'dArr': 8659,\n    'hArr': 8660,\n    'forall': 8704,\n    'part': 8706,\n    'exist': 8707,\n    'empty': 8709,\n    'nabla': 8711,\n    'isin': 8712,\n    'notin': 8713,\n    'ni': 8715,\n    'prod': 8719,\n    'sum': 8721,\n    'minus': 8722,\n    'lowast': 8727,\n    'radic': 8730,\n    'prop': 8733,\n    'infin': 8734,\n    'ang': 8736,\n    'and': 8743,\n    'or': 8744,\n    'cap': 8745,\n    'cup': 8746,\n    'int': 8747,\n    'there4': 8756,\n    'sim': 8764,\n    'cong': 8773,\n    'asymp': 8776,\n    'ne': 8800,\n    'equiv': 8801,\n    'le': 8804,\n    'ge': 8805,\n    'sub': 8834,\n    'sup': 8835,\n    'nsub': 8836,\n    'sube': 8838,\n    'supe': 8839,\n    'oplus': 8853,\n    'otimes': 8855,\n    'perp': 8869,\n    'sdot': 8901,\n    'lceil': 8968,\n    'rceil': 8969,\n    'lfloor': 8970,\n    'rfloor': 8971,\n    'lang': 9001,\n    'rang': 9002,\n    'loz': 9674,\n    'spades': 9824,\n    'clubs': 9827,\n    'hearts': 9829,\n    'diams': 9830\n  };\n  Object.keys(sax.ENTITIES).forEach(function (key) {\n    var e = sax.ENTITIES[key];\n    var s = typeof e === 'number' ? String.fromCharCode(e) : e;\n    sax.ENTITIES[key] = s;\n  });\n  for (var s in sax.STATE) {\n    sax.STATE[sax.STATE[s]] = s;\n  }\n\n  // shorthand\n  S = sax.STATE;\n  function emit(parser, event, data) {\n    parser[event] && parser[event](data);\n  }\n  function emitNode(parser, nodeType, data) {\n    if (parser.textNode) closeText(parser);\n    emit(parser, nodeType, data);\n  }\n  function closeText(parser) {\n    parser.textNode = textopts(parser.opt, parser.textNode);\n    if (parser.textNode) emit(parser, 'ontext', parser.textNode);\n    parser.textNode = '';\n  }\n  function textopts(opt, text) {\n    if (opt.trim) text = text.trim();\n    if (opt.normalize) text = text.replace(/\\s+/g, ' ');\n    return text;\n  }\n  function error(parser, er) {\n    closeText(parser);\n    if (parser.trackPosition) {\n      er += '\\nLine: ' + parser.line + '\\nColumn: ' + parser.column + '\\nChar: ' + parser.c;\n    }\n    er = new Error(er);\n    parser.error = er;\n    emit(parser, 'onerror', er);\n    return parser;\n  }\n  function end(parser) {\n    if (parser.sawRoot && !parser.closedRoot) strictFail(parser, 'Unclosed root tag');\n    if (parser.state !== S.BEGIN && parser.state !== S.BEGIN_WHITESPACE && parser.state !== S.TEXT) {\n      error(parser, 'Unexpected end');\n    }\n    closeText(parser);\n    parser.c = '';\n    parser.closed = true;\n    emit(parser, 'onend');\n    SAXParser.call(parser, parser.strict, parser.opt);\n    return parser;\n  }\n  function strictFail(parser, message) {\n    if (typeof parser !== 'object' || !(parser instanceof SAXParser)) {\n      throw new Error('bad call to strictFail');\n    }\n    if (parser.strict) {\n      error(parser, message);\n    }\n  }\n  function newTag(parser) {\n    if (!parser.strict) parser.tagName = parser.tagName[parser.looseCase]();\n    var parent = parser.tags[parser.tags.length - 1] || parser;\n    var tag = parser.tag = {\n      name: parser.tagName,\n      attributes: {}\n    };\n\n    // will be overridden if tag contails an xmlns=\"foo\" or xmlns:foo=\"bar\"\n    if (parser.opt.xmlns) {\n      tag.ns = parent.ns;\n    }\n    parser.attribList.length = 0;\n    emitNode(parser, 'onopentagstart', tag);\n  }\n  function qname(name, attribute) {\n    var i = name.indexOf(':');\n    var qualName = i < 0 ? ['', name] : name.split(':');\n    var prefix = qualName[0];\n    var local = qualName[1];\n\n    // <x \"xmlns\"=\"http://foo\">\n    if (attribute && name === 'xmlns') {\n      prefix = 'xmlns';\n      local = '';\n    }\n    return {\n      prefix: prefix,\n      local: local\n    };\n  }\n  function attrib(parser) {\n    if (!parser.strict) {\n      parser.attribName = parser.attribName[parser.looseCase]();\n    }\n    if (parser.attribList.indexOf(parser.attribName) !== -1 || parser.tag.attributes.hasOwnProperty(parser.attribName)) {\n      parser.attribName = parser.attribValue = '';\n      return;\n    }\n    if (parser.opt.xmlns) {\n      var qn = qname(parser.attribName, true);\n      var prefix = qn.prefix;\n      var local = qn.local;\n      if (prefix === 'xmlns') {\n        // namespace binding attribute. push the binding into scope\n        if (local === 'xml' && parser.attribValue !== XML_NAMESPACE) {\n          strictFail(parser, 'xml: prefix must be bound to ' + XML_NAMESPACE + '\\n' + 'Actual: ' + parser.attribValue);\n        } else if (local === 'xmlns' && parser.attribValue !== XMLNS_NAMESPACE) {\n          strictFail(parser, 'xmlns: prefix must be bound to ' + XMLNS_NAMESPACE + '\\n' + 'Actual: ' + parser.attribValue);\n        } else {\n          var tag = parser.tag;\n          var parent = parser.tags[parser.tags.length - 1] || parser;\n          if (tag.ns === parent.ns) {\n            tag.ns = Object.create(parent.ns);\n          }\n          tag.ns[local] = parser.attribValue;\n        }\n      }\n\n      // defer onattribute events until all attributes have been seen\n      // so any new bindings can take effect. preserve attribute order\n      // so deferred events can be emitted in document order\n      parser.attribList.push([parser.attribName, parser.attribValue]);\n    } else {\n      // in non-xmlns mode, we can emit the event right away\n      parser.tag.attributes[parser.attribName] = parser.attribValue;\n      emitNode(parser, 'onattribute', {\n        name: parser.attribName,\n        value: parser.attribValue\n      });\n    }\n    parser.attribName = parser.attribValue = '';\n  }\n  function openTag(parser, selfClosing) {\n    if (parser.opt.xmlns) {\n      // emit namespace binding events\n      var tag = parser.tag;\n\n      // add namespace info to tag\n      var qn = qname(parser.tagName);\n      tag.prefix = qn.prefix;\n      tag.local = qn.local;\n      tag.uri = tag.ns[qn.prefix] || '';\n      if (tag.prefix && !tag.uri) {\n        strictFail(parser, 'Unbound namespace prefix: ' + JSON.stringify(parser.tagName));\n        tag.uri = qn.prefix;\n      }\n      var parent = parser.tags[parser.tags.length - 1] || parser;\n      if (tag.ns && parent.ns !== tag.ns) {\n        Object.keys(tag.ns).forEach(function (p) {\n          emitNode(parser, 'onopennamespace', {\n            prefix: p,\n            uri: tag.ns[p]\n          });\n        });\n      }\n\n      // handle deferred onattribute events\n      // Note: do not apply default ns to attributes:\n      //   http://www.w3.org/TR/REC-xml-names/#defaulting\n      for (var i = 0, l = parser.attribList.length; i < l; i++) {\n        var nv = parser.attribList[i];\n        var name = nv[0];\n        var value = nv[1];\n        var qualName = qname(name, true);\n        var prefix = qualName.prefix;\n        var local = qualName.local;\n        var uri = prefix === '' ? '' : tag.ns[prefix] || '';\n        var a = {\n          name: name,\n          value: value,\n          prefix: prefix,\n          local: local,\n          uri: uri\n        };\n\n        // if there's any attributes with an undefined namespace,\n        // then fail on them now.\n        if (prefix && prefix !== 'xmlns' && !uri) {\n          strictFail(parser, 'Unbound namespace prefix: ' + JSON.stringify(prefix));\n          a.uri = prefix;\n        }\n        parser.tag.attributes[name] = a;\n        emitNode(parser, 'onattribute', a);\n      }\n      parser.attribList.length = 0;\n    }\n    parser.tag.isSelfClosing = !!selfClosing;\n\n    // process the tag\n    parser.sawRoot = true;\n    parser.tags.push(parser.tag);\n    emitNode(parser, 'onopentag', parser.tag);\n    if (!selfClosing) {\n      // special case for <script> in non-strict mode.\n      if (!parser.noscript && parser.tagName.toLowerCase() === 'script') {\n        parser.state = S.SCRIPT;\n      } else {\n        parser.state = S.TEXT;\n      }\n      parser.tag = null;\n      parser.tagName = '';\n    }\n    parser.attribName = parser.attribValue = '';\n    parser.attribList.length = 0;\n  }\n  function closeTag(parser) {\n    if (!parser.tagName) {\n      strictFail(parser, 'Weird empty close tag.');\n      parser.textNode += '</>';\n      parser.state = S.TEXT;\n      return;\n    }\n    if (parser.script) {\n      if (parser.tagName !== 'script') {\n        parser.script += '</' + parser.tagName + '>';\n        parser.tagName = '';\n        parser.state = S.SCRIPT;\n        return;\n      }\n      emitNode(parser, 'onscript', parser.script);\n      parser.script = '';\n    }\n\n    // first make sure that the closing tag actually exists.\n    // <a><b></c></b></a> will close everything, otherwise.\n    var t = parser.tags.length;\n    var tagName = parser.tagName;\n    if (!parser.strict) {\n      tagName = tagName[parser.looseCase]();\n    }\n    var closeTo = tagName;\n    while (t--) {\n      var close = parser.tags[t];\n      if (close.name !== closeTo) {\n        // fail the first time in strict mode\n        strictFail(parser, 'Unexpected close tag');\n      } else {\n        break;\n      }\n    }\n\n    // didn't find it.  we already failed for strict, so just abort.\n    if (t < 0) {\n      strictFail(parser, 'Unmatched closing tag: ' + parser.tagName);\n      parser.textNode += '</' + parser.tagName + '>';\n      parser.state = S.TEXT;\n      return;\n    }\n    parser.tagName = tagName;\n    var s = parser.tags.length;\n    while (s-- > t) {\n      var tag = parser.tag = parser.tags.pop();\n      parser.tagName = parser.tag.name;\n      emitNode(parser, 'onclosetag', parser.tagName);\n      var x = {};\n      for (var i in tag.ns) {\n        x[i] = tag.ns[i];\n      }\n      var parent = parser.tags[parser.tags.length - 1] || parser;\n      if (parser.opt.xmlns && tag.ns !== parent.ns) {\n        // remove namespace bindings introduced by tag\n        Object.keys(tag.ns).forEach(function (p) {\n          var n = tag.ns[p];\n          emitNode(parser, 'onclosenamespace', {\n            prefix: p,\n            uri: n\n          });\n        });\n      }\n    }\n    if (t === 0) parser.closedRoot = true;\n    parser.tagName = parser.attribValue = parser.attribName = '';\n    parser.attribList.length = 0;\n    parser.state = S.TEXT;\n  }\n  function parseEntity(parser) {\n    var entity = parser.entity;\n    var entityLC = entity.toLowerCase();\n    var num;\n    var numStr = '';\n    if (parser.ENTITIES[entity]) {\n      return parser.ENTITIES[entity];\n    }\n    if (parser.ENTITIES[entityLC]) {\n      return parser.ENTITIES[entityLC];\n    }\n    entity = entityLC;\n    if (entity.charAt(0) === '#') {\n      if (entity.charAt(1) === 'x') {\n        entity = entity.slice(2);\n        num = parseInt(entity, 16);\n        numStr = num.toString(16);\n      } else {\n        entity = entity.slice(1);\n        num = parseInt(entity, 10);\n        numStr = num.toString(10);\n      }\n    }\n    entity = entity.replace(/^0+/, '');\n    if (isNaN(num) || numStr.toLowerCase() !== entity) {\n      strictFail(parser, 'Invalid character entity');\n      return '&' + parser.entity + ';';\n    }\n    return String.fromCodePoint(num);\n  }\n  function beginWhiteSpace(parser, c) {\n    if (c === '<') {\n      parser.state = S.OPEN_WAKA;\n      parser.startTagPosition = parser.position;\n    } else if (!isWhitespace(c)) {\n      // have to process this as a text node.\n      // weird, but happens.\n      strictFail(parser, 'Non-whitespace before first tag.');\n      parser.textNode = c;\n      parser.state = S.TEXT;\n    }\n  }\n  function charAt(chunk, i) {\n    var result = '';\n    if (i < chunk.length) {\n      result = chunk.charAt(i);\n    }\n    return result;\n  }\n  function write(chunk) {\n    var parser = this;\n    if (this.error) {\n      throw this.error;\n    }\n    if (parser.closed) {\n      return error(parser, 'Cannot write after close. Assign an onready handler.');\n    }\n    if (chunk === null) {\n      return end(parser);\n    }\n    if (typeof chunk === 'object') {\n      chunk = chunk.toString();\n    }\n    var i = 0;\n    var c = '';\n    while (true) {\n      c = charAt(chunk, i++);\n      parser.c = c;\n      if (!c) {\n        break;\n      }\n      if (parser.trackPosition) {\n        parser.position++;\n        if (c === '\\n') {\n          parser.line++;\n          parser.column = 0;\n        } else {\n          parser.column++;\n        }\n      }\n      switch (parser.state) {\n        case S.BEGIN:\n          parser.state = S.BEGIN_WHITESPACE;\n          if (c === '\\uFEFF') {\n            continue;\n          }\n          beginWhiteSpace(parser, c);\n          continue;\n        case S.BEGIN_WHITESPACE:\n          beginWhiteSpace(parser, c);\n          continue;\n        case S.TEXT:\n          if (parser.sawRoot && !parser.closedRoot) {\n            var starti = i - 1;\n            while (c && c !== '<' && c !== '&') {\n              c = charAt(chunk, i++);\n              if (c && parser.trackPosition) {\n                parser.position++;\n                if (c === '\\n') {\n                  parser.line++;\n                  parser.column = 0;\n                } else {\n                  parser.column++;\n                }\n              }\n            }\n            parser.textNode += chunk.substring(starti, i - 1);\n          }\n          if (c === '<' && !(parser.sawRoot && parser.closedRoot && !parser.strict)) {\n            parser.state = S.OPEN_WAKA;\n            parser.startTagPosition = parser.position;\n          } else {\n            if (!isWhitespace(c) && (!parser.sawRoot || parser.closedRoot)) {\n              strictFail(parser, 'Text data outside of root node.');\n            }\n            if (c === '&') {\n              parser.state = S.TEXT_ENTITY;\n            } else {\n              parser.textNode += c;\n            }\n          }\n          continue;\n        case S.SCRIPT:\n          // only non-strict\n          if (c === '<') {\n            parser.state = S.SCRIPT_ENDING;\n          } else {\n            parser.script += c;\n          }\n          continue;\n        case S.SCRIPT_ENDING:\n          if (c === '/') {\n            parser.state = S.CLOSE_TAG;\n          } else {\n            parser.script += '<' + c;\n            parser.state = S.SCRIPT;\n          }\n          continue;\n        case S.OPEN_WAKA:\n          // either a /, ?, !, or text is coming next.\n          if (c === '!') {\n            parser.state = S.SGML_DECL;\n            parser.sgmlDecl = '';\n          } else if (isWhitespace(c)) {\n            // wait for it...\n          } else if (isMatch(nameStart, c)) {\n            parser.state = S.OPEN_TAG;\n            parser.tagName = c;\n          } else if (c === '/') {\n            parser.state = S.CLOSE_TAG;\n            parser.tagName = '';\n          } else if (c === '?') {\n            parser.state = S.PROC_INST;\n            parser.procInstName = parser.procInstBody = '';\n          } else {\n            strictFail(parser, 'Unencoded <');\n            // if there was some whitespace, then add that in.\n            if (parser.startTagPosition + 1 < parser.position) {\n              var pad = parser.position - parser.startTagPosition;\n              c = new Array(pad).join(' ') + c;\n            }\n            parser.textNode += '<' + c;\n            parser.state = S.TEXT;\n          }\n          continue;\n        case S.SGML_DECL:\n          if ((parser.sgmlDecl + c).toUpperCase() === CDATA) {\n            emitNode(parser, 'onopencdata');\n            parser.state = S.CDATA;\n            parser.sgmlDecl = '';\n            parser.cdata = '';\n          } else if (parser.sgmlDecl + c === '--') {\n            parser.state = S.COMMENT;\n            parser.comment = '';\n            parser.sgmlDecl = '';\n          } else if ((parser.sgmlDecl + c).toUpperCase() === DOCTYPE) {\n            parser.state = S.DOCTYPE;\n            if (parser.doctype || parser.sawRoot) {\n              strictFail(parser, 'Inappropriately located doctype declaration');\n            }\n            parser.doctype = '';\n            parser.sgmlDecl = '';\n          } else if (c === '>') {\n            emitNode(parser, 'onsgmldeclaration', parser.sgmlDecl);\n            parser.sgmlDecl = '';\n            parser.state = S.TEXT;\n          } else if (isQuote(c)) {\n            parser.state = S.SGML_DECL_QUOTED;\n            parser.sgmlDecl += c;\n          } else {\n            parser.sgmlDecl += c;\n          }\n          continue;\n        case S.SGML_DECL_QUOTED:\n          if (c === parser.q) {\n            parser.state = S.SGML_DECL;\n            parser.q = '';\n          }\n          parser.sgmlDecl += c;\n          continue;\n        case S.DOCTYPE:\n          if (c === '>') {\n            parser.state = S.TEXT;\n            emitNode(parser, 'ondoctype', parser.doctype);\n            parser.doctype = true; // just remember that we saw it.\n          } else {\n            parser.doctype += c;\n            if (c === '[') {\n              parser.state = S.DOCTYPE_DTD;\n            } else if (isQuote(c)) {\n              parser.state = S.DOCTYPE_QUOTED;\n              parser.q = c;\n            }\n          }\n          continue;\n        case S.DOCTYPE_QUOTED:\n          parser.doctype += c;\n          if (c === parser.q) {\n            parser.q = '';\n            parser.state = S.DOCTYPE;\n          }\n          continue;\n        case S.DOCTYPE_DTD:\n          parser.doctype += c;\n          if (c === ']') {\n            parser.state = S.DOCTYPE;\n          } else if (isQuote(c)) {\n            parser.state = S.DOCTYPE_DTD_QUOTED;\n            parser.q = c;\n          }\n          continue;\n        case S.DOCTYPE_DTD_QUOTED:\n          parser.doctype += c;\n          if (c === parser.q) {\n            parser.state = S.DOCTYPE_DTD;\n            parser.q = '';\n          }\n          continue;\n        case S.COMMENT:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDING;\n          } else {\n            parser.comment += c;\n          }\n          continue;\n        case S.COMMENT_ENDING:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDED;\n            parser.comment = textopts(parser.opt, parser.comment);\n            if (parser.comment) {\n              emitNode(parser, 'oncomment', parser.comment);\n            }\n            parser.comment = '';\n          } else {\n            parser.comment += '-' + c;\n            parser.state = S.COMMENT;\n          }\n          continue;\n        case S.COMMENT_ENDED:\n          if (c !== '>') {\n            strictFail(parser, 'Malformed comment');\n            // allow <!-- blah -- bloo --> in non-strict mode,\n            // which is a comment of \" blah -- bloo \"\n            parser.comment += '--' + c;\n            parser.state = S.COMMENT;\n          } else {\n            parser.state = S.TEXT;\n          }\n          continue;\n        case S.CDATA:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING;\n          } else {\n            parser.cdata += c;\n          }\n          continue;\n        case S.CDATA_ENDING:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING_2;\n          } else {\n            parser.cdata += ']' + c;\n            parser.state = S.CDATA;\n          }\n          continue;\n        case S.CDATA_ENDING_2:\n          if (c === '>') {\n            if (parser.cdata) {\n              emitNode(parser, 'oncdata', parser.cdata);\n            }\n            emitNode(parser, 'onclosecdata');\n            parser.cdata = '';\n            parser.state = S.TEXT;\n          } else if (c === ']') {\n            parser.cdata += ']';\n          } else {\n            parser.cdata += ']]' + c;\n            parser.state = S.CDATA;\n          }\n          continue;\n        case S.PROC_INST:\n          if (c === '?') {\n            parser.state = S.PROC_INST_ENDING;\n          } else if (isWhitespace(c)) {\n            parser.state = S.PROC_INST_BODY;\n          } else {\n            parser.procInstName += c;\n          }\n          continue;\n        case S.PROC_INST_BODY:\n          if (!parser.procInstBody && isWhitespace(c)) {\n            continue;\n          } else if (c === '?') {\n            parser.state = S.PROC_INST_ENDING;\n          } else {\n            parser.procInstBody += c;\n          }\n          continue;\n        case S.PROC_INST_ENDING:\n          if (c === '>') {\n            emitNode(parser, 'onprocessinginstruction', {\n              name: parser.procInstName,\n              body: parser.procInstBody\n            });\n            parser.procInstName = parser.procInstBody = '';\n            parser.state = S.TEXT;\n          } else {\n            parser.procInstBody += '?' + c;\n            parser.state = S.PROC_INST_BODY;\n          }\n          continue;\n        case S.OPEN_TAG:\n          if (isMatch(nameBody, c)) {\n            parser.tagName += c;\n          } else {\n            newTag(parser);\n            if (c === '>') {\n              openTag(parser);\n            } else if (c === '/') {\n              parser.state = S.OPEN_TAG_SLASH;\n            } else {\n              if (!isWhitespace(c)) {\n                strictFail(parser, 'Invalid character in tag name');\n              }\n              parser.state = S.ATTRIB;\n            }\n          }\n          continue;\n        case S.OPEN_TAG_SLASH:\n          if (c === '>') {\n            openTag(parser, true);\n            closeTag(parser);\n          } else {\n            strictFail(parser, 'Forward-slash in opening tag not followed by >');\n            parser.state = S.ATTRIB;\n          }\n          continue;\n        case S.ATTRIB:\n          // haven't read the attribute name yet.\n          if (isWhitespace(c)) {\n            continue;\n          } else if (c === '>') {\n            openTag(parser);\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH;\n          } else if (isMatch(nameStart, c)) {\n            parser.attribName = c;\n            parser.attribValue = '';\n            parser.state = S.ATTRIB_NAME;\n          } else {\n            strictFail(parser, 'Invalid attribute name');\n          }\n          continue;\n        case S.ATTRIB_NAME:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE;\n          } else if (c === '>') {\n            strictFail(parser, 'Attribute without value');\n            parser.attribValue = parser.attribName;\n            attrib(parser);\n            openTag(parser);\n          } else if (isWhitespace(c)) {\n            parser.state = S.ATTRIB_NAME_SAW_WHITE;\n          } else if (isMatch(nameBody, c)) {\n            parser.attribName += c;\n          } else {\n            strictFail(parser, 'Invalid attribute name');\n          }\n          continue;\n        case S.ATTRIB_NAME_SAW_WHITE:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE;\n          } else if (isWhitespace(c)) {\n            continue;\n          } else {\n            strictFail(parser, 'Attribute without value');\n            parser.tag.attributes[parser.attribName] = '';\n            parser.attribValue = '';\n            emitNode(parser, 'onattribute', {\n              name: parser.attribName,\n              value: ''\n            });\n            parser.attribName = '';\n            if (c === '>') {\n              openTag(parser);\n            } else if (isMatch(nameStart, c)) {\n              parser.attribName = c;\n              parser.state = S.ATTRIB_NAME;\n            } else {\n              strictFail(parser, 'Invalid attribute name');\n              parser.state = S.ATTRIB;\n            }\n          }\n          continue;\n        case S.ATTRIB_VALUE:\n          if (isWhitespace(c)) {\n            continue;\n          } else if (isQuote(c)) {\n            parser.q = c;\n            parser.state = S.ATTRIB_VALUE_QUOTED;\n          } else {\n            strictFail(parser, 'Unquoted attribute value');\n            parser.state = S.ATTRIB_VALUE_UNQUOTED;\n            parser.attribValue = c;\n          }\n          continue;\n        case S.ATTRIB_VALUE_QUOTED:\n          if (c !== parser.q) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_Q;\n            } else {\n              parser.attribValue += c;\n            }\n            continue;\n          }\n          attrib(parser);\n          parser.q = '';\n          parser.state = S.ATTRIB_VALUE_CLOSED;\n          continue;\n        case S.ATTRIB_VALUE_CLOSED:\n          if (isWhitespace(c)) {\n            parser.state = S.ATTRIB;\n          } else if (c === '>') {\n            openTag(parser);\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH;\n          } else if (isMatch(nameStart, c)) {\n            strictFail(parser, 'No whitespace between attributes');\n            parser.attribName = c;\n            parser.attribValue = '';\n            parser.state = S.ATTRIB_NAME;\n          } else {\n            strictFail(parser, 'Invalid attribute name');\n          }\n          continue;\n        case S.ATTRIB_VALUE_UNQUOTED:\n          if (!isAttribEnd(c)) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_U;\n            } else {\n              parser.attribValue += c;\n            }\n            continue;\n          }\n          attrib(parser);\n          if (c === '>') {\n            openTag(parser);\n          } else {\n            parser.state = S.ATTRIB;\n          }\n          continue;\n        case S.CLOSE_TAG:\n          if (!parser.tagName) {\n            if (isWhitespace(c)) {\n              continue;\n            } else if (notMatch(nameStart, c)) {\n              if (parser.script) {\n                parser.script += '</' + c;\n                parser.state = S.SCRIPT;\n              } else {\n                strictFail(parser, 'Invalid tagname in closing tag.');\n              }\n            } else {\n              parser.tagName = c;\n            }\n          } else if (c === '>') {\n            closeTag(parser);\n          } else if (isMatch(nameBody, c)) {\n            parser.tagName += c;\n          } else if (parser.script) {\n            parser.script += '</' + parser.tagName;\n            parser.tagName = '';\n            parser.state = S.SCRIPT;\n          } else {\n            if (!isWhitespace(c)) {\n              strictFail(parser, 'Invalid tagname in closing tag');\n            }\n            parser.state = S.CLOSE_TAG_SAW_WHITE;\n          }\n          continue;\n        case S.CLOSE_TAG_SAW_WHITE:\n          if (isWhitespace(c)) {\n            continue;\n          }\n          if (c === '>') {\n            closeTag(parser);\n          } else {\n            strictFail(parser, 'Invalid characters in closing tag');\n          }\n          continue;\n        case S.TEXT_ENTITY:\n        case S.ATTRIB_VALUE_ENTITY_Q:\n        case S.ATTRIB_VALUE_ENTITY_U:\n          var returnState;\n          var buffer;\n          switch (parser.state) {\n            case S.TEXT_ENTITY:\n              returnState = S.TEXT;\n              buffer = 'textNode';\n              break;\n            case S.ATTRIB_VALUE_ENTITY_Q:\n              returnState = S.ATTRIB_VALUE_QUOTED;\n              buffer = 'attribValue';\n              break;\n            case S.ATTRIB_VALUE_ENTITY_U:\n              returnState = S.ATTRIB_VALUE_UNQUOTED;\n              buffer = 'attribValue';\n              break;\n          }\n          if (c === ';') {\n            parser[buffer] += parseEntity(parser);\n            parser.entity = '';\n            parser.state = returnState;\n          } else if (isMatch(parser.entity.length ? entityBody : entityStart, c)) {\n            parser.entity += c;\n          } else {\n            strictFail(parser, 'Invalid character in entity name');\n            parser[buffer] += '&' + parser.entity + c;\n            parser.entity = '';\n            parser.state = returnState;\n          }\n          continue;\n        default:\n          throw new Error(parser, 'Unknown state: ' + parser.state);\n      }\n    } // while\n\n    if (parser.position >= parser.bufferCheckPosition) {\n      checkBufferLength(parser);\n    }\n    return parser;\n  }\n\n  /*! http://mths.be/fromcodepoint v0.1.0 by @mathias */\n  /* istanbul ignore next */\n  if (!String.fromCodePoint) {\n    (function () {\n      var stringFromCharCode = String.fromCharCode;\n      var floor = Math.floor;\n      var fromCodePoint = function () {\n        var MAX_SIZE = 0x4000;\n        var codeUnits = [];\n        var highSurrogate;\n        var lowSurrogate;\n        var index = -1;\n        var length = arguments.length;\n        if (!length) {\n          return '';\n        }\n        var result = '';\n        while (++index < length) {\n          var codePoint = Number(arguments[index]);\n          if (!isFinite(codePoint) ||\n          // `NaN`, `+Infinity`, or `-Infinity`\n          codePoint < 0 ||\n          // not a valid Unicode code point\n          codePoint > 0x10FFFF ||\n          // not a valid Unicode code point\n          floor(codePoint) !== codePoint // not an integer\n          ) {\n            throw RangeError('Invalid code point: ' + codePoint);\n          }\n          if (codePoint <= 0xFFFF) {\n            // BMP code point\n            codeUnits.push(codePoint);\n          } else {\n            // Astral code point; split in surrogate halves\n            // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n            codePoint -= 0x10000;\n            highSurrogate = (codePoint >> 10) + 0xD800;\n            lowSurrogate = codePoint % 0x400 + 0xDC00;\n            codeUnits.push(highSurrogate, lowSurrogate);\n          }\n          if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n            result += stringFromCharCode.apply(null, codeUnits);\n            codeUnits.length = 0;\n          }\n        }\n        return result;\n      };\n      /* istanbul ignore next */\n      if (Object.defineProperty) {\n        Object.defineProperty(String, 'fromCodePoint', {\n          value: fromCodePoint,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        String.fromCodePoint = fromCodePoint;\n      }\n    })();\n  }\n})(typeof exports === 'undefined' ? this.sax = {} : exports);", "map": {"version": 3, "names": ["sax", "parser", "strict", "opt", "SAXParser", "SAXStream", "createStream", "MAX_BUFFER_LENGTH", "buffers", "EVENTS", "clearBuffers", "q", "c", "bufferCheckPosition", "lowercase", "lowercasetags", "looseCase", "tags", "closed", "closedRoot", "sawRoot", "tag", "error", "noscript", "state", "S", "BEGIN", "strictEntities", "ENTITIES", "Object", "create", "XML_ENTITIES", "attribList", "xmlns", "ns", "rootNS", "trackPosition", "position", "line", "column", "emit", "o", "F", "prototype", "newf", "keys", "a", "i", "hasOwnProperty", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxAllowed", "Math", "max", "maxActual", "l", "length", "len", "closeText", "emitNode", "cdata", "script", "m", "flushBuffers", "end", "write", "resume", "close", "flush", "Stream", "require", "ex", "streamWraps", "filter", "ev", "apply", "_parser", "writable", "readable", "me", "onend", "onerror", "er", "_decoder", "for<PERSON>ach", "defineProperty", "get", "set", "h", "removeAllListeners", "on", "enumerable", "configurable", "constructor", "value", "data", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SD", "StringDecoder", "toString", "chunk", "handler", "indexOf", "args", "arguments", "Array", "splice", "call", "CDATA", "DOCTYPE", "XML_NAMESPACE", "XMLNS_NAMESPACE", "xml", "nameStart", "nameBody", "entityStart", "entityBody", "isWhitespace", "isQuote", "isAttribEnd", "isMatch", "regex", "test", "notMatch", "STATE", "BEGIN_WHITESPACE", "TEXT", "TEXT_ENTITY", "OPEN_WAKA", "SGML_DECL", "SGML_DECL_QUOTED", "DOCTYPE_QUOTED", "DOCTYPE_DTD", "DOCTYPE_DTD_QUOTED", "COMMENT_STARTING", "COMMENT", "COMMENT_ENDING", "COMMENT_ENDED", "CDATA_ENDING", "CDATA_ENDING_2", "PROC_INST", "PROC_INST_BODY", "PROC_INST_ENDING", "OPEN_TAG", "OPEN_TAG_SLASH", "ATTRIB", "ATTRIB_NAME", "ATTRIB_NAME_SAW_WHITE", "ATTRIB_VALUE", "ATTRIB_VALUE_QUOTED", "ATTRIB_VALUE_CLOSED", "ATTRIB_VALUE_UNQUOTED", "ATTRIB_VALUE_ENTITY_Q", "ATTRIB_VALUE_ENTITY_U", "CLOSE_TAG", "CLOSE_TAG_SAW_WHITE", "SCRIPT", "SCRIPT_ENDING", "key", "e", "s", "String", "fromCharCode", "event", "nodeType", "textNode", "textopts", "text", "trim", "normalize", "replace", "Error", "strictFail", "message", "newTag", "tagName", "parent", "name", "attributes", "qname", "attribute", "qualName", "split", "prefix", "local", "attrib", "attribName", "attribValue", "qn", "openTag", "selfClosing", "uri", "JSON", "stringify", "p", "nv", "isSelfClosing", "toLowerCase", "closeTag", "t", "closeTo", "pop", "x", "n", "parseEntity", "entity", "entityLC", "num", "numStr", "char<PERSON>t", "slice", "parseInt", "isNaN", "fromCodePoint", "beginWhiteSpace", "startTagPosition", "result", "starti", "substring", "sgmlDecl", "procInstName", "procInstBody", "pad", "join", "toUpperCase", "comment", "doctype", "body", "returnState", "buffer", "stringFromCharCode", "floor", "MAX_SIZE", "codeUnits", "highSurrogate", "lowSurrogate", "index", "codePoint", "Number", "isFinite", "RangeError", "exports"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/sax/lib/sax.js"], "sourcesContent": [";(function (sax) { // wrapper for non-node envs\n  sax.parser = function (strict, opt) { return new SAXParser(strict, opt) }\n  sax.SAXParser = SAXParser\n  sax.SAXStream = SAXStream\n  sax.createStream = createStream\n\n  // When we pass the MAX_BUFFER_LENGTH position, start checking for buffer overruns.\n  // When we check, schedule the next check for MAX_BUFFER_LENGTH - (max(buffer lengths)),\n  // since that's the earliest that a buffer overrun could occur.  This way, checks are\n  // as rare as required, but as often as necessary to ensure never crossing this bound.\n  // Furthermore, buffers are only tested at most once per write(), so passing a very\n  // large string into write() might have undesirable effects, but this is manageable by\n  // the caller, so it is assumed to be safe.  Thus, a call to write() may, in the extreme\n  // edge case, result in creating at most one complete copy of the string passed in.\n  // Set to Infinity to have unlimited buffers.\n  sax.MAX_BUFFER_LENGTH = 64 * 1024\n\n  var buffers = [\n    'comment', 'sgmlDecl', 'textNode', 'tagName', 'doctype',\n    'procInstName', 'procInstBody', 'entity', 'attribName',\n    'attribValue', 'cdata', 'script'\n  ]\n\n  sax.EVENTS = [\n    'text',\n    'processinginstruction',\n    'sgmldeclaration',\n    'doctype',\n    'comment',\n    'opentagstart',\n    'attribute',\n    'opentag',\n    'closetag',\n    'opencdata',\n    'cdata',\n    'closecdata',\n    'error',\n    'end',\n    'ready',\n    'script',\n    'opennamespace',\n    'closenamespace'\n  ]\n\n  function SAXParser (strict, opt) {\n    if (!(this instanceof SAXParser)) {\n      return new SAXParser(strict, opt)\n    }\n\n    var parser = this\n    clearBuffers(parser)\n    parser.q = parser.c = ''\n    parser.bufferCheckPosition = sax.MAX_BUFFER_LENGTH\n    parser.opt = opt || {}\n    parser.opt.lowercase = parser.opt.lowercase || parser.opt.lowercasetags\n    parser.looseCase = parser.opt.lowercase ? 'toLowerCase' : 'toUpperCase'\n    parser.tags = []\n    parser.closed = parser.closedRoot = parser.sawRoot = false\n    parser.tag = parser.error = null\n    parser.strict = !!strict\n    parser.noscript = !!(strict || parser.opt.noscript)\n    parser.state = S.BEGIN\n    parser.strictEntities = parser.opt.strictEntities\n    parser.ENTITIES = parser.strictEntities ? Object.create(sax.XML_ENTITIES) : Object.create(sax.ENTITIES)\n    parser.attribList = []\n\n    // namespaces form a prototype chain.\n    // it always points at the current tag,\n    // which protos to its parent tag.\n    if (parser.opt.xmlns) {\n      parser.ns = Object.create(rootNS)\n    }\n\n    // mostly just for error reporting\n    parser.trackPosition = parser.opt.position !== false\n    if (parser.trackPosition) {\n      parser.position = parser.line = parser.column = 0\n    }\n    emit(parser, 'onready')\n  }\n\n  if (!Object.create) {\n    Object.create = function (o) {\n      function F () {}\n      F.prototype = o\n      var newf = new F()\n      return newf\n    }\n  }\n\n  if (!Object.keys) {\n    Object.keys = function (o) {\n      var a = []\n      for (var i in o) if (o.hasOwnProperty(i)) a.push(i)\n      return a\n    }\n  }\n\n  function checkBufferLength (parser) {\n    var maxAllowed = Math.max(sax.MAX_BUFFER_LENGTH, 10)\n    var maxActual = 0\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      var len = parser[buffers[i]].length\n      if (len > maxAllowed) {\n        // Text/cdata nodes can get big, and since they're buffered,\n        // we can get here under normal conditions.\n        // Avoid issues by emitting the text node now,\n        // so at least it won't get any bigger.\n        switch (buffers[i]) {\n          case 'textNode':\n            closeText(parser)\n            break\n\n          case 'cdata':\n            emitNode(parser, 'oncdata', parser.cdata)\n            parser.cdata = ''\n            break\n\n          case 'script':\n            emitNode(parser, 'onscript', parser.script)\n            parser.script = ''\n            break\n\n          default:\n            error(parser, 'Max buffer length exceeded: ' + buffers[i])\n        }\n      }\n      maxActual = Math.max(maxActual, len)\n    }\n    // schedule the next check for the earliest possible buffer overrun.\n    var m = sax.MAX_BUFFER_LENGTH - maxActual\n    parser.bufferCheckPosition = m + parser.position\n  }\n\n  function clearBuffers (parser) {\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      parser[buffers[i]] = ''\n    }\n  }\n\n  function flushBuffers (parser) {\n    closeText(parser)\n    if (parser.cdata !== '') {\n      emitNode(parser, 'oncdata', parser.cdata)\n      parser.cdata = ''\n    }\n    if (parser.script !== '') {\n      emitNode(parser, 'onscript', parser.script)\n      parser.script = ''\n    }\n  }\n\n  SAXParser.prototype = {\n    end: function () { end(this) },\n    write: write,\n    resume: function () { this.error = null; return this },\n    close: function () { return this.write(null) },\n    flush: function () { flushBuffers(this) }\n  }\n\n  var Stream\n  try {\n    Stream = require('stream').Stream\n  } catch (ex) {\n    Stream = function () {}\n  }\n\n  var streamWraps = sax.EVENTS.filter(function (ev) {\n    return ev !== 'error' && ev !== 'end'\n  })\n\n  function createStream (strict, opt) {\n    return new SAXStream(strict, opt)\n  }\n\n  function SAXStream (strict, opt) {\n    if (!(this instanceof SAXStream)) {\n      return new SAXStream(strict, opt)\n    }\n\n    Stream.apply(this)\n\n    this._parser = new SAXParser(strict, opt)\n    this.writable = true\n    this.readable = true\n\n    var me = this\n\n    this._parser.onend = function () {\n      me.emit('end')\n    }\n\n    this._parser.onerror = function (er) {\n      me.emit('error', er)\n\n      // if didn't throw, then means error was handled.\n      // go ahead and clear error, so we can write again.\n      me._parser.error = null\n    }\n\n    this._decoder = null\n\n    streamWraps.forEach(function (ev) {\n      Object.defineProperty(me, 'on' + ev, {\n        get: function () {\n          return me._parser['on' + ev]\n        },\n        set: function (h) {\n          if (!h) {\n            me.removeAllListeners(ev)\n            me._parser['on' + ev] = h\n            return h\n          }\n          me.on(ev, h)\n        },\n        enumerable: true,\n        configurable: false\n      })\n    })\n  }\n\n  SAXStream.prototype = Object.create(Stream.prototype, {\n    constructor: {\n      value: SAXStream\n    }\n  })\n\n  SAXStream.prototype.write = function (data) {\n    if (typeof Buffer === 'function' &&\n      typeof Buffer.isBuffer === 'function' &&\n      Buffer.isBuffer(data)) {\n      if (!this._decoder) {\n        var SD = require('string_decoder').StringDecoder\n        this._decoder = new SD('utf8')\n      }\n      data = this._decoder.write(data)\n    }\n\n    this._parser.write(data.toString())\n    this.emit('data', data)\n    return true\n  }\n\n  SAXStream.prototype.end = function (chunk) {\n    if (chunk && chunk.length) {\n      this.write(chunk)\n    }\n    this._parser.end()\n    return true\n  }\n\n  SAXStream.prototype.on = function (ev, handler) {\n    var me = this\n    if (!me._parser['on' + ev] && streamWraps.indexOf(ev) !== -1) {\n      me._parser['on' + ev] = function () {\n        var args = arguments.length === 1 ? [arguments[0]] : Array.apply(null, arguments)\n        args.splice(0, 0, ev)\n        me.emit.apply(me, args)\n      }\n    }\n\n    return Stream.prototype.on.call(me, ev, handler)\n  }\n\n  // this really needs to be replaced with character classes.\n  // XML allows all manner of ridiculous numbers and digits.\n  var CDATA = '[CDATA['\n  var DOCTYPE = 'DOCTYPE'\n  var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace'\n  var XMLNS_NAMESPACE = 'http://www.w3.org/2000/xmlns/'\n  var rootNS = { xml: XML_NAMESPACE, xmlns: XMLNS_NAMESPACE }\n\n  // http://www.w3.org/TR/REC-xml/#NT-NameStartChar\n  // This implementation works on strings, a single character at a time\n  // as such, it cannot ever support astral-plane characters (10000-EFFFF)\n  // without a significant breaking change to either this  parser, or the\n  // JavaScript language.  Implementation of an emoji-capable xml parser\n  // is left as an exercise for the reader.\n  var nameStart = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/\n\n  var nameBody = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/\n\n  var entityStart = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/\n  var entityBody = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/\n\n  function isWhitespace (c) {\n    return c === ' ' || c === '\\n' || c === '\\r' || c === '\\t'\n  }\n\n  function isQuote (c) {\n    return c === '\"' || c === '\\''\n  }\n\n  function isAttribEnd (c) {\n    return c === '>' || isWhitespace(c)\n  }\n\n  function isMatch (regex, c) {\n    return regex.test(c)\n  }\n\n  function notMatch (regex, c) {\n    return !isMatch(regex, c)\n  }\n\n  var S = 0\n  sax.STATE = {\n    BEGIN: S++, // leading byte order mark or whitespace\n    BEGIN_WHITESPACE: S++, // leading whitespace\n    TEXT: S++, // general stuff\n    TEXT_ENTITY: S++, // &amp and such.\n    OPEN_WAKA: S++, // <\n    SGML_DECL: S++, // <!BLARG\n    SGML_DECL_QUOTED: S++, // <!BLARG foo \"bar\n    DOCTYPE: S++, // <!DOCTYPE\n    DOCTYPE_QUOTED: S++, // <!DOCTYPE \"//blah\n    DOCTYPE_DTD: S++, // <!DOCTYPE \"//blah\" [ ...\n    DOCTYPE_DTD_QUOTED: S++, // <!DOCTYPE \"//blah\" [ \"foo\n    COMMENT_STARTING: S++, // <!-\n    COMMENT: S++, // <!--\n    COMMENT_ENDING: S++, // <!-- blah -\n    COMMENT_ENDED: S++, // <!-- blah --\n    CDATA: S++, // <![CDATA[ something\n    CDATA_ENDING: S++, // ]\n    CDATA_ENDING_2: S++, // ]]\n    PROC_INST: S++, // <?hi\n    PROC_INST_BODY: S++, // <?hi there\n    PROC_INST_ENDING: S++, // <?hi \"there\" ?\n    OPEN_TAG: S++, // <strong\n    OPEN_TAG_SLASH: S++, // <strong /\n    ATTRIB: S++, // <a\n    ATTRIB_NAME: S++, // <a foo\n    ATTRIB_NAME_SAW_WHITE: S++, // <a foo _\n    ATTRIB_VALUE: S++, // <a foo=\n    ATTRIB_VALUE_QUOTED: S++, // <a foo=\"bar\n    ATTRIB_VALUE_CLOSED: S++, // <a foo=\"bar\"\n    ATTRIB_VALUE_UNQUOTED: S++, // <a foo=bar\n    ATTRIB_VALUE_ENTITY_Q: S++, // <foo bar=\"&quot;\"\n    ATTRIB_VALUE_ENTITY_U: S++, // <foo bar=&quot\n    CLOSE_TAG: S++, // </a\n    CLOSE_TAG_SAW_WHITE: S++, // </a   >\n    SCRIPT: S++, // <script> ...\n    SCRIPT_ENDING: S++ // <script> ... <\n  }\n\n  sax.XML_ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\"\n  }\n\n  sax.ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\",\n    'AElig': 198,\n    'Aacute': 193,\n    'Acirc': 194,\n    'Agrave': 192,\n    'Aring': 197,\n    'Atilde': 195,\n    'Auml': 196,\n    'Ccedil': 199,\n    'ETH': 208,\n    'Eacute': 201,\n    'Ecirc': 202,\n    'Egrave': 200,\n    'Euml': 203,\n    'Iacute': 205,\n    'Icirc': 206,\n    'Igrave': 204,\n    'Iuml': 207,\n    'Ntilde': 209,\n    'Oacute': 211,\n    'Ocirc': 212,\n    'Ograve': 210,\n    'Oslash': 216,\n    'Otilde': 213,\n    'Ouml': 214,\n    'THORN': 222,\n    'Uacute': 218,\n    'Ucirc': 219,\n    'Ugrave': 217,\n    'Uuml': 220,\n    'Yacute': 221,\n    'aacute': 225,\n    'acirc': 226,\n    'aelig': 230,\n    'agrave': 224,\n    'aring': 229,\n    'atilde': 227,\n    'auml': 228,\n    'ccedil': 231,\n    'eacute': 233,\n    'ecirc': 234,\n    'egrave': 232,\n    'eth': 240,\n    'euml': 235,\n    'iacute': 237,\n    'icirc': 238,\n    'igrave': 236,\n    'iuml': 239,\n    'ntilde': 241,\n    'oacute': 243,\n    'ocirc': 244,\n    'ograve': 242,\n    'oslash': 248,\n    'otilde': 245,\n    'ouml': 246,\n    'szlig': 223,\n    'thorn': 254,\n    'uacute': 250,\n    'ucirc': 251,\n    'ugrave': 249,\n    'uuml': 252,\n    'yacute': 253,\n    'yuml': 255,\n    'copy': 169,\n    'reg': 174,\n    'nbsp': 160,\n    'iexcl': 161,\n    'cent': 162,\n    'pound': 163,\n    'curren': 164,\n    'yen': 165,\n    'brvbar': 166,\n    'sect': 167,\n    'uml': 168,\n    'ordf': 170,\n    'laquo': 171,\n    'not': 172,\n    'shy': 173,\n    'macr': 175,\n    'deg': 176,\n    'plusmn': 177,\n    'sup1': 185,\n    'sup2': 178,\n    'sup3': 179,\n    'acute': 180,\n    'micro': 181,\n    'para': 182,\n    'middot': 183,\n    'cedil': 184,\n    'ordm': 186,\n    'raquo': 187,\n    'frac14': 188,\n    'frac12': 189,\n    'frac34': 190,\n    'iquest': 191,\n    'times': 215,\n    'divide': 247,\n    'OElig': 338,\n    'oelig': 339,\n    'Scaron': 352,\n    'scaron': 353,\n    'Yuml': 376,\n    'fnof': 402,\n    'circ': 710,\n    'tilde': 732,\n    'Alpha': 913,\n    'Beta': 914,\n    'Gamma': 915,\n    'Delta': 916,\n    'Epsilon': 917,\n    'Zeta': 918,\n    'Eta': 919,\n    'Theta': 920,\n    'Iota': 921,\n    'Kappa': 922,\n    'Lambda': 923,\n    'Mu': 924,\n    'Nu': 925,\n    'Xi': 926,\n    'Omicron': 927,\n    'Pi': 928,\n    'Rho': 929,\n    'Sigma': 931,\n    'Tau': 932,\n    'Upsilon': 933,\n    'Phi': 934,\n    'Chi': 935,\n    'Psi': 936,\n    'Omega': 937,\n    'alpha': 945,\n    'beta': 946,\n    'gamma': 947,\n    'delta': 948,\n    'epsilon': 949,\n    'zeta': 950,\n    'eta': 951,\n    'theta': 952,\n    'iota': 953,\n    'kappa': 954,\n    'lambda': 955,\n    'mu': 956,\n    'nu': 957,\n    'xi': 958,\n    'omicron': 959,\n    'pi': 960,\n    'rho': 961,\n    'sigmaf': 962,\n    'sigma': 963,\n    'tau': 964,\n    'upsilon': 965,\n    'phi': 966,\n    'chi': 967,\n    'psi': 968,\n    'omega': 969,\n    'thetasym': 977,\n    'upsih': 978,\n    'piv': 982,\n    'ensp': 8194,\n    'emsp': 8195,\n    'thinsp': 8201,\n    'zwnj': 8204,\n    'zwj': 8205,\n    'lrm': 8206,\n    'rlm': 8207,\n    'ndash': 8211,\n    'mdash': 8212,\n    'lsquo': 8216,\n    'rsquo': 8217,\n    'sbquo': 8218,\n    'ldquo': 8220,\n    'rdquo': 8221,\n    'bdquo': 8222,\n    'dagger': 8224,\n    'Dagger': 8225,\n    'bull': 8226,\n    'hellip': 8230,\n    'permil': 8240,\n    'prime': 8242,\n    'Prime': 8243,\n    'lsaquo': 8249,\n    'rsaquo': 8250,\n    'oline': 8254,\n    'frasl': 8260,\n    'euro': 8364,\n    'image': 8465,\n    'weierp': 8472,\n    'real': 8476,\n    'trade': 8482,\n    'alefsym': 8501,\n    'larr': 8592,\n    'uarr': 8593,\n    'rarr': 8594,\n    'darr': 8595,\n    'harr': 8596,\n    'crarr': 8629,\n    'lArr': 8656,\n    'uArr': 8657,\n    'rArr': 8658,\n    'dArr': 8659,\n    'hArr': 8660,\n    'forall': 8704,\n    'part': 8706,\n    'exist': 8707,\n    'empty': 8709,\n    'nabla': 8711,\n    'isin': 8712,\n    'notin': 8713,\n    'ni': 8715,\n    'prod': 8719,\n    'sum': 8721,\n    'minus': 8722,\n    'lowast': 8727,\n    'radic': 8730,\n    'prop': 8733,\n    'infin': 8734,\n    'ang': 8736,\n    'and': 8743,\n    'or': 8744,\n    'cap': 8745,\n    'cup': 8746,\n    'int': 8747,\n    'there4': 8756,\n    'sim': 8764,\n    'cong': 8773,\n    'asymp': 8776,\n    'ne': 8800,\n    'equiv': 8801,\n    'le': 8804,\n    'ge': 8805,\n    'sub': 8834,\n    'sup': 8835,\n    'nsub': 8836,\n    'sube': 8838,\n    'supe': 8839,\n    'oplus': 8853,\n    'otimes': 8855,\n    'perp': 8869,\n    'sdot': 8901,\n    'lceil': 8968,\n    'rceil': 8969,\n    'lfloor': 8970,\n    'rfloor': 8971,\n    'lang': 9001,\n    'rang': 9002,\n    'loz': 9674,\n    'spades': 9824,\n    'clubs': 9827,\n    'hearts': 9829,\n    'diams': 9830\n  }\n\n  Object.keys(sax.ENTITIES).forEach(function (key) {\n    var e = sax.ENTITIES[key]\n    var s = typeof e === 'number' ? String.fromCharCode(e) : e\n    sax.ENTITIES[key] = s\n  })\n\n  for (var s in sax.STATE) {\n    sax.STATE[sax.STATE[s]] = s\n  }\n\n  // shorthand\n  S = sax.STATE\n\n  function emit (parser, event, data) {\n    parser[event] && parser[event](data)\n  }\n\n  function emitNode (parser, nodeType, data) {\n    if (parser.textNode) closeText(parser)\n    emit(parser, nodeType, data)\n  }\n\n  function closeText (parser) {\n    parser.textNode = textopts(parser.opt, parser.textNode)\n    if (parser.textNode) emit(parser, 'ontext', parser.textNode)\n    parser.textNode = ''\n  }\n\n  function textopts (opt, text) {\n    if (opt.trim) text = text.trim()\n    if (opt.normalize) text = text.replace(/\\s+/g, ' ')\n    return text\n  }\n\n  function error (parser, er) {\n    closeText(parser)\n    if (parser.trackPosition) {\n      er += '\\nLine: ' + parser.line +\n        '\\nColumn: ' + parser.column +\n        '\\nChar: ' + parser.c\n    }\n    er = new Error(er)\n    parser.error = er\n    emit(parser, 'onerror', er)\n    return parser\n  }\n\n  function end (parser) {\n    if (parser.sawRoot && !parser.closedRoot) strictFail(parser, 'Unclosed root tag')\n    if ((parser.state !== S.BEGIN) &&\n      (parser.state !== S.BEGIN_WHITESPACE) &&\n      (parser.state !== S.TEXT)) {\n      error(parser, 'Unexpected end')\n    }\n    closeText(parser)\n    parser.c = ''\n    parser.closed = true\n    emit(parser, 'onend')\n    SAXParser.call(parser, parser.strict, parser.opt)\n    return parser\n  }\n\n  function strictFail (parser, message) {\n    if (typeof parser !== 'object' || !(parser instanceof SAXParser)) {\n      throw new Error('bad call to strictFail')\n    }\n    if (parser.strict) {\n      error(parser, message)\n    }\n  }\n\n  function newTag (parser) {\n    if (!parser.strict) parser.tagName = parser.tagName[parser.looseCase]()\n    var parent = parser.tags[parser.tags.length - 1] || parser\n    var tag = parser.tag = { name: parser.tagName, attributes: {} }\n\n    // will be overridden if tag contails an xmlns=\"foo\" or xmlns:foo=\"bar\"\n    if (parser.opt.xmlns) {\n      tag.ns = parent.ns\n    }\n    parser.attribList.length = 0\n    emitNode(parser, 'onopentagstart', tag)\n  }\n\n  function qname (name, attribute) {\n    var i = name.indexOf(':')\n    var qualName = i < 0 ? [ '', name ] : name.split(':')\n    var prefix = qualName[0]\n    var local = qualName[1]\n\n    // <x \"xmlns\"=\"http://foo\">\n    if (attribute && name === 'xmlns') {\n      prefix = 'xmlns'\n      local = ''\n    }\n\n    return { prefix: prefix, local: local }\n  }\n\n  function attrib (parser) {\n    if (!parser.strict) {\n      parser.attribName = parser.attribName[parser.looseCase]()\n    }\n\n    if (parser.attribList.indexOf(parser.attribName) !== -1 ||\n      parser.tag.attributes.hasOwnProperty(parser.attribName)) {\n      parser.attribName = parser.attribValue = ''\n      return\n    }\n\n    if (parser.opt.xmlns) {\n      var qn = qname(parser.attribName, true)\n      var prefix = qn.prefix\n      var local = qn.local\n\n      if (prefix === 'xmlns') {\n        // namespace binding attribute. push the binding into scope\n        if (local === 'xml' && parser.attribValue !== XML_NAMESPACE) {\n          strictFail(parser,\n            'xml: prefix must be bound to ' + XML_NAMESPACE + '\\n' +\n            'Actual: ' + parser.attribValue)\n        } else if (local === 'xmlns' && parser.attribValue !== XMLNS_NAMESPACE) {\n          strictFail(parser,\n            'xmlns: prefix must be bound to ' + XMLNS_NAMESPACE + '\\n' +\n            'Actual: ' + parser.attribValue)\n        } else {\n          var tag = parser.tag\n          var parent = parser.tags[parser.tags.length - 1] || parser\n          if (tag.ns === parent.ns) {\n            tag.ns = Object.create(parent.ns)\n          }\n          tag.ns[local] = parser.attribValue\n        }\n      }\n\n      // defer onattribute events until all attributes have been seen\n      // so any new bindings can take effect. preserve attribute order\n      // so deferred events can be emitted in document order\n      parser.attribList.push([parser.attribName, parser.attribValue])\n    } else {\n      // in non-xmlns mode, we can emit the event right away\n      parser.tag.attributes[parser.attribName] = parser.attribValue\n      emitNode(parser, 'onattribute', {\n        name: parser.attribName,\n        value: parser.attribValue\n      })\n    }\n\n    parser.attribName = parser.attribValue = ''\n  }\n\n  function openTag (parser, selfClosing) {\n    if (parser.opt.xmlns) {\n      // emit namespace binding events\n      var tag = parser.tag\n\n      // add namespace info to tag\n      var qn = qname(parser.tagName)\n      tag.prefix = qn.prefix\n      tag.local = qn.local\n      tag.uri = tag.ns[qn.prefix] || ''\n\n      if (tag.prefix && !tag.uri) {\n        strictFail(parser, 'Unbound namespace prefix: ' +\n          JSON.stringify(parser.tagName))\n        tag.uri = qn.prefix\n      }\n\n      var parent = parser.tags[parser.tags.length - 1] || parser\n      if (tag.ns && parent.ns !== tag.ns) {\n        Object.keys(tag.ns).forEach(function (p) {\n          emitNode(parser, 'onopennamespace', {\n            prefix: p,\n            uri: tag.ns[p]\n          })\n        })\n      }\n\n      // handle deferred onattribute events\n      // Note: do not apply default ns to attributes:\n      //   http://www.w3.org/TR/REC-xml-names/#defaulting\n      for (var i = 0, l = parser.attribList.length; i < l; i++) {\n        var nv = parser.attribList[i]\n        var name = nv[0]\n        var value = nv[1]\n        var qualName = qname(name, true)\n        var prefix = qualName.prefix\n        var local = qualName.local\n        var uri = prefix === '' ? '' : (tag.ns[prefix] || '')\n        var a = {\n          name: name,\n          value: value,\n          prefix: prefix,\n          local: local,\n          uri: uri\n        }\n\n        // if there's any attributes with an undefined namespace,\n        // then fail on them now.\n        if (prefix && prefix !== 'xmlns' && !uri) {\n          strictFail(parser, 'Unbound namespace prefix: ' +\n            JSON.stringify(prefix))\n          a.uri = prefix\n        }\n        parser.tag.attributes[name] = a\n        emitNode(parser, 'onattribute', a)\n      }\n      parser.attribList.length = 0\n    }\n\n    parser.tag.isSelfClosing = !!selfClosing\n\n    // process the tag\n    parser.sawRoot = true\n    parser.tags.push(parser.tag)\n    emitNode(parser, 'onopentag', parser.tag)\n    if (!selfClosing) {\n      // special case for <script> in non-strict mode.\n      if (!parser.noscript && parser.tagName.toLowerCase() === 'script') {\n        parser.state = S.SCRIPT\n      } else {\n        parser.state = S.TEXT\n      }\n      parser.tag = null\n      parser.tagName = ''\n    }\n    parser.attribName = parser.attribValue = ''\n    parser.attribList.length = 0\n  }\n\n  function closeTag (parser) {\n    if (!parser.tagName) {\n      strictFail(parser, 'Weird empty close tag.')\n      parser.textNode += '</>'\n      parser.state = S.TEXT\n      return\n    }\n\n    if (parser.script) {\n      if (parser.tagName !== 'script') {\n        parser.script += '</' + parser.tagName + '>'\n        parser.tagName = ''\n        parser.state = S.SCRIPT\n        return\n      }\n      emitNode(parser, 'onscript', parser.script)\n      parser.script = ''\n    }\n\n    // first make sure that the closing tag actually exists.\n    // <a><b></c></b></a> will close everything, otherwise.\n    var t = parser.tags.length\n    var tagName = parser.tagName\n    if (!parser.strict) {\n      tagName = tagName[parser.looseCase]()\n    }\n    var closeTo = tagName\n    while (t--) {\n      var close = parser.tags[t]\n      if (close.name !== closeTo) {\n        // fail the first time in strict mode\n        strictFail(parser, 'Unexpected close tag')\n      } else {\n        break\n      }\n    }\n\n    // didn't find it.  we already failed for strict, so just abort.\n    if (t < 0) {\n      strictFail(parser, 'Unmatched closing tag: ' + parser.tagName)\n      parser.textNode += '</' + parser.tagName + '>'\n      parser.state = S.TEXT\n      return\n    }\n    parser.tagName = tagName\n    var s = parser.tags.length\n    while (s-- > t) {\n      var tag = parser.tag = parser.tags.pop()\n      parser.tagName = parser.tag.name\n      emitNode(parser, 'onclosetag', parser.tagName)\n\n      var x = {}\n      for (var i in tag.ns) {\n        x[i] = tag.ns[i]\n      }\n\n      var parent = parser.tags[parser.tags.length - 1] || parser\n      if (parser.opt.xmlns && tag.ns !== parent.ns) {\n        // remove namespace bindings introduced by tag\n        Object.keys(tag.ns).forEach(function (p) {\n          var n = tag.ns[p]\n          emitNode(parser, 'onclosenamespace', { prefix: p, uri: n })\n        })\n      }\n    }\n    if (t === 0) parser.closedRoot = true\n    parser.tagName = parser.attribValue = parser.attribName = ''\n    parser.attribList.length = 0\n    parser.state = S.TEXT\n  }\n\n  function parseEntity (parser) {\n    var entity = parser.entity\n    var entityLC = entity.toLowerCase()\n    var num\n    var numStr = ''\n\n    if (parser.ENTITIES[entity]) {\n      return parser.ENTITIES[entity]\n    }\n    if (parser.ENTITIES[entityLC]) {\n      return parser.ENTITIES[entityLC]\n    }\n    entity = entityLC\n    if (entity.charAt(0) === '#') {\n      if (entity.charAt(1) === 'x') {\n        entity = entity.slice(2)\n        num = parseInt(entity, 16)\n        numStr = num.toString(16)\n      } else {\n        entity = entity.slice(1)\n        num = parseInt(entity, 10)\n        numStr = num.toString(10)\n      }\n    }\n    entity = entity.replace(/^0+/, '')\n    if (isNaN(num) || numStr.toLowerCase() !== entity) {\n      strictFail(parser, 'Invalid character entity')\n      return '&' + parser.entity + ';'\n    }\n\n    return String.fromCodePoint(num)\n  }\n\n  function beginWhiteSpace (parser, c) {\n    if (c === '<') {\n      parser.state = S.OPEN_WAKA\n      parser.startTagPosition = parser.position\n    } else if (!isWhitespace(c)) {\n      // have to process this as a text node.\n      // weird, but happens.\n      strictFail(parser, 'Non-whitespace before first tag.')\n      parser.textNode = c\n      parser.state = S.TEXT\n    }\n  }\n\n  function charAt (chunk, i) {\n    var result = ''\n    if (i < chunk.length) {\n      result = chunk.charAt(i)\n    }\n    return result\n  }\n\n  function write (chunk) {\n    var parser = this\n    if (this.error) {\n      throw this.error\n    }\n    if (parser.closed) {\n      return error(parser,\n        'Cannot write after close. Assign an onready handler.')\n    }\n    if (chunk === null) {\n      return end(parser)\n    }\n    if (typeof chunk === 'object') {\n      chunk = chunk.toString()\n    }\n    var i = 0\n    var c = ''\n    while (true) {\n      c = charAt(chunk, i++)\n      parser.c = c\n\n      if (!c) {\n        break\n      }\n\n      if (parser.trackPosition) {\n        parser.position++\n        if (c === '\\n') {\n          parser.line++\n          parser.column = 0\n        } else {\n          parser.column++\n        }\n      }\n\n      switch (parser.state) {\n        case S.BEGIN:\n          parser.state = S.BEGIN_WHITESPACE\n          if (c === '\\uFEFF') {\n            continue\n          }\n          beginWhiteSpace(parser, c)\n          continue\n\n        case S.BEGIN_WHITESPACE:\n          beginWhiteSpace(parser, c)\n          continue\n\n        case S.TEXT:\n          if (parser.sawRoot && !parser.closedRoot) {\n            var starti = i - 1\n            while (c && c !== '<' && c !== '&') {\n              c = charAt(chunk, i++)\n              if (c && parser.trackPosition) {\n                parser.position++\n                if (c === '\\n') {\n                  parser.line++\n                  parser.column = 0\n                } else {\n                  parser.column++\n                }\n              }\n            }\n            parser.textNode += chunk.substring(starti, i - 1)\n          }\n          if (c === '<' && !(parser.sawRoot && parser.closedRoot && !parser.strict)) {\n            parser.state = S.OPEN_WAKA\n            parser.startTagPosition = parser.position\n          } else {\n            if (!isWhitespace(c) && (!parser.sawRoot || parser.closedRoot)) {\n              strictFail(parser, 'Text data outside of root node.')\n            }\n            if (c === '&') {\n              parser.state = S.TEXT_ENTITY\n            } else {\n              parser.textNode += c\n            }\n          }\n          continue\n\n        case S.SCRIPT:\n          // only non-strict\n          if (c === '<') {\n            parser.state = S.SCRIPT_ENDING\n          } else {\n            parser.script += c\n          }\n          continue\n\n        case S.SCRIPT_ENDING:\n          if (c === '/') {\n            parser.state = S.CLOSE_TAG\n          } else {\n            parser.script += '<' + c\n            parser.state = S.SCRIPT\n          }\n          continue\n\n        case S.OPEN_WAKA:\n          // either a /, ?, !, or text is coming next.\n          if (c === '!') {\n            parser.state = S.SGML_DECL\n            parser.sgmlDecl = ''\n          } else if (isWhitespace(c)) {\n            // wait for it...\n          } else if (isMatch(nameStart, c)) {\n            parser.state = S.OPEN_TAG\n            parser.tagName = c\n          } else if (c === '/') {\n            parser.state = S.CLOSE_TAG\n            parser.tagName = ''\n          } else if (c === '?') {\n            parser.state = S.PROC_INST\n            parser.procInstName = parser.procInstBody = ''\n          } else {\n            strictFail(parser, 'Unencoded <')\n            // if there was some whitespace, then add that in.\n            if (parser.startTagPosition + 1 < parser.position) {\n              var pad = parser.position - parser.startTagPosition\n              c = new Array(pad).join(' ') + c\n            }\n            parser.textNode += '<' + c\n            parser.state = S.TEXT\n          }\n          continue\n\n        case S.SGML_DECL:\n          if ((parser.sgmlDecl + c).toUpperCase() === CDATA) {\n            emitNode(parser, 'onopencdata')\n            parser.state = S.CDATA\n            parser.sgmlDecl = ''\n            parser.cdata = ''\n          } else if (parser.sgmlDecl + c === '--') {\n            parser.state = S.COMMENT\n            parser.comment = ''\n            parser.sgmlDecl = ''\n          } else if ((parser.sgmlDecl + c).toUpperCase() === DOCTYPE) {\n            parser.state = S.DOCTYPE\n            if (parser.doctype || parser.sawRoot) {\n              strictFail(parser,\n                'Inappropriately located doctype declaration')\n            }\n            parser.doctype = ''\n            parser.sgmlDecl = ''\n          } else if (c === '>') {\n            emitNode(parser, 'onsgmldeclaration', parser.sgmlDecl)\n            parser.sgmlDecl = ''\n            parser.state = S.TEXT\n          } else if (isQuote(c)) {\n            parser.state = S.SGML_DECL_QUOTED\n            parser.sgmlDecl += c\n          } else {\n            parser.sgmlDecl += c\n          }\n          continue\n\n        case S.SGML_DECL_QUOTED:\n          if (c === parser.q) {\n            parser.state = S.SGML_DECL\n            parser.q = ''\n          }\n          parser.sgmlDecl += c\n          continue\n\n        case S.DOCTYPE:\n          if (c === '>') {\n            parser.state = S.TEXT\n            emitNode(parser, 'ondoctype', parser.doctype)\n            parser.doctype = true // just remember that we saw it.\n          } else {\n            parser.doctype += c\n            if (c === '[') {\n              parser.state = S.DOCTYPE_DTD\n            } else if (isQuote(c)) {\n              parser.state = S.DOCTYPE_QUOTED\n              parser.q = c\n            }\n          }\n          continue\n\n        case S.DOCTYPE_QUOTED:\n          parser.doctype += c\n          if (c === parser.q) {\n            parser.q = ''\n            parser.state = S.DOCTYPE\n          }\n          continue\n\n        case S.DOCTYPE_DTD:\n          parser.doctype += c\n          if (c === ']') {\n            parser.state = S.DOCTYPE\n          } else if (isQuote(c)) {\n            parser.state = S.DOCTYPE_DTD_QUOTED\n            parser.q = c\n          }\n          continue\n\n        case S.DOCTYPE_DTD_QUOTED:\n          parser.doctype += c\n          if (c === parser.q) {\n            parser.state = S.DOCTYPE_DTD\n            parser.q = ''\n          }\n          continue\n\n        case S.COMMENT:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDING\n          } else {\n            parser.comment += c\n          }\n          continue\n\n        case S.COMMENT_ENDING:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDED\n            parser.comment = textopts(parser.opt, parser.comment)\n            if (parser.comment) {\n              emitNode(parser, 'oncomment', parser.comment)\n            }\n            parser.comment = ''\n          } else {\n            parser.comment += '-' + c\n            parser.state = S.COMMENT\n          }\n          continue\n\n        case S.COMMENT_ENDED:\n          if (c !== '>') {\n            strictFail(parser, 'Malformed comment')\n            // allow <!-- blah -- bloo --> in non-strict mode,\n            // which is a comment of \" blah -- bloo \"\n            parser.comment += '--' + c\n            parser.state = S.COMMENT\n          } else {\n            parser.state = S.TEXT\n          }\n          continue\n\n        case S.CDATA:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING\n          } else {\n            parser.cdata += c\n          }\n          continue\n\n        case S.CDATA_ENDING:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING_2\n          } else {\n            parser.cdata += ']' + c\n            parser.state = S.CDATA\n          }\n          continue\n\n        case S.CDATA_ENDING_2:\n          if (c === '>') {\n            if (parser.cdata) {\n              emitNode(parser, 'oncdata', parser.cdata)\n            }\n            emitNode(parser, 'onclosecdata')\n            parser.cdata = ''\n            parser.state = S.TEXT\n          } else if (c === ']') {\n            parser.cdata += ']'\n          } else {\n            parser.cdata += ']]' + c\n            parser.state = S.CDATA\n          }\n          continue\n\n        case S.PROC_INST:\n          if (c === '?') {\n            parser.state = S.PROC_INST_ENDING\n          } else if (isWhitespace(c)) {\n            parser.state = S.PROC_INST_BODY\n          } else {\n            parser.procInstName += c\n          }\n          continue\n\n        case S.PROC_INST_BODY:\n          if (!parser.procInstBody && isWhitespace(c)) {\n            continue\n          } else if (c === '?') {\n            parser.state = S.PROC_INST_ENDING\n          } else {\n            parser.procInstBody += c\n          }\n          continue\n\n        case S.PROC_INST_ENDING:\n          if (c === '>') {\n            emitNode(parser, 'onprocessinginstruction', {\n              name: parser.procInstName,\n              body: parser.procInstBody\n            })\n            parser.procInstName = parser.procInstBody = ''\n            parser.state = S.TEXT\n          } else {\n            parser.procInstBody += '?' + c\n            parser.state = S.PROC_INST_BODY\n          }\n          continue\n\n        case S.OPEN_TAG:\n          if (isMatch(nameBody, c)) {\n            parser.tagName += c\n          } else {\n            newTag(parser)\n            if (c === '>') {\n              openTag(parser)\n            } else if (c === '/') {\n              parser.state = S.OPEN_TAG_SLASH\n            } else {\n              if (!isWhitespace(c)) {\n                strictFail(parser, 'Invalid character in tag name')\n              }\n              parser.state = S.ATTRIB\n            }\n          }\n          continue\n\n        case S.OPEN_TAG_SLASH:\n          if (c === '>') {\n            openTag(parser, true)\n            closeTag(parser)\n          } else {\n            strictFail(parser, 'Forward-slash in opening tag not followed by >')\n            parser.state = S.ATTRIB\n          }\n          continue\n\n        case S.ATTRIB:\n          // haven't read the attribute name yet.\n          if (isWhitespace(c)) {\n            continue\n          } else if (c === '>') {\n            openTag(parser)\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH\n          } else if (isMatch(nameStart, c)) {\n            parser.attribName = c\n            parser.attribValue = ''\n            parser.state = S.ATTRIB_NAME\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_NAME:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE\n          } else if (c === '>') {\n            strictFail(parser, 'Attribute without value')\n            parser.attribValue = parser.attribName\n            attrib(parser)\n            openTag(parser)\n          } else if (isWhitespace(c)) {\n            parser.state = S.ATTRIB_NAME_SAW_WHITE\n          } else if (isMatch(nameBody, c)) {\n            parser.attribName += c\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_NAME_SAW_WHITE:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE\n          } else if (isWhitespace(c)) {\n            continue\n          } else {\n            strictFail(parser, 'Attribute without value')\n            parser.tag.attributes[parser.attribName] = ''\n            parser.attribValue = ''\n            emitNode(parser, 'onattribute', {\n              name: parser.attribName,\n              value: ''\n            })\n            parser.attribName = ''\n            if (c === '>') {\n              openTag(parser)\n            } else if (isMatch(nameStart, c)) {\n              parser.attribName = c\n              parser.state = S.ATTRIB_NAME\n            } else {\n              strictFail(parser, 'Invalid attribute name')\n              parser.state = S.ATTRIB\n            }\n          }\n          continue\n\n        case S.ATTRIB_VALUE:\n          if (isWhitespace(c)) {\n            continue\n          } else if (isQuote(c)) {\n            parser.q = c\n            parser.state = S.ATTRIB_VALUE_QUOTED\n          } else {\n            strictFail(parser, 'Unquoted attribute value')\n            parser.state = S.ATTRIB_VALUE_UNQUOTED\n            parser.attribValue = c\n          }\n          continue\n\n        case S.ATTRIB_VALUE_QUOTED:\n          if (c !== parser.q) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_Q\n            } else {\n              parser.attribValue += c\n            }\n            continue\n          }\n          attrib(parser)\n          parser.q = ''\n          parser.state = S.ATTRIB_VALUE_CLOSED\n          continue\n\n        case S.ATTRIB_VALUE_CLOSED:\n          if (isWhitespace(c)) {\n            parser.state = S.ATTRIB\n          } else if (c === '>') {\n            openTag(parser)\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH\n          } else if (isMatch(nameStart, c)) {\n            strictFail(parser, 'No whitespace between attributes')\n            parser.attribName = c\n            parser.attribValue = ''\n            parser.state = S.ATTRIB_NAME\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_VALUE_UNQUOTED:\n          if (!isAttribEnd(c)) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_U\n            } else {\n              parser.attribValue += c\n            }\n            continue\n          }\n          attrib(parser)\n          if (c === '>') {\n            openTag(parser)\n          } else {\n            parser.state = S.ATTRIB\n          }\n          continue\n\n        case S.CLOSE_TAG:\n          if (!parser.tagName) {\n            if (isWhitespace(c)) {\n              continue\n            } else if (notMatch(nameStart, c)) {\n              if (parser.script) {\n                parser.script += '</' + c\n                parser.state = S.SCRIPT\n              } else {\n                strictFail(parser, 'Invalid tagname in closing tag.')\n              }\n            } else {\n              parser.tagName = c\n            }\n          } else if (c === '>') {\n            closeTag(parser)\n          } else if (isMatch(nameBody, c)) {\n            parser.tagName += c\n          } else if (parser.script) {\n            parser.script += '</' + parser.tagName\n            parser.tagName = ''\n            parser.state = S.SCRIPT\n          } else {\n            if (!isWhitespace(c)) {\n              strictFail(parser, 'Invalid tagname in closing tag')\n            }\n            parser.state = S.CLOSE_TAG_SAW_WHITE\n          }\n          continue\n\n        case S.CLOSE_TAG_SAW_WHITE:\n          if (isWhitespace(c)) {\n            continue\n          }\n          if (c === '>') {\n            closeTag(parser)\n          } else {\n            strictFail(parser, 'Invalid characters in closing tag')\n          }\n          continue\n\n        case S.TEXT_ENTITY:\n        case S.ATTRIB_VALUE_ENTITY_Q:\n        case S.ATTRIB_VALUE_ENTITY_U:\n          var returnState\n          var buffer\n          switch (parser.state) {\n            case S.TEXT_ENTITY:\n              returnState = S.TEXT\n              buffer = 'textNode'\n              break\n\n            case S.ATTRIB_VALUE_ENTITY_Q:\n              returnState = S.ATTRIB_VALUE_QUOTED\n              buffer = 'attribValue'\n              break\n\n            case S.ATTRIB_VALUE_ENTITY_U:\n              returnState = S.ATTRIB_VALUE_UNQUOTED\n              buffer = 'attribValue'\n              break\n          }\n\n          if (c === ';') {\n            parser[buffer] += parseEntity(parser)\n            parser.entity = ''\n            parser.state = returnState\n          } else if (isMatch(parser.entity.length ? entityBody : entityStart, c)) {\n            parser.entity += c\n          } else {\n            strictFail(parser, 'Invalid character in entity name')\n            parser[buffer] += '&' + parser.entity + c\n            parser.entity = ''\n            parser.state = returnState\n          }\n\n          continue\n\n        default:\n          throw new Error(parser, 'Unknown state: ' + parser.state)\n      }\n    } // while\n\n    if (parser.position >= parser.bufferCheckPosition) {\n      checkBufferLength(parser)\n    }\n    return parser\n  }\n\n  /*! http://mths.be/fromcodepoint v0.1.0 by @mathias */\n  /* istanbul ignore next */\n  if (!String.fromCodePoint) {\n    (function () {\n      var stringFromCharCode = String.fromCharCode\n      var floor = Math.floor\n      var fromCodePoint = function () {\n        var MAX_SIZE = 0x4000\n        var codeUnits = []\n        var highSurrogate\n        var lowSurrogate\n        var index = -1\n        var length = arguments.length\n        if (!length) {\n          return ''\n        }\n        var result = ''\n        while (++index < length) {\n          var codePoint = Number(arguments[index])\n          if (\n            !isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`\n            codePoint < 0 || // not a valid Unicode code point\n            codePoint > 0x10FFFF || // not a valid Unicode code point\n            floor(codePoint) !== codePoint // not an integer\n          ) {\n            throw RangeError('Invalid code point: ' + codePoint)\n          }\n          if (codePoint <= 0xFFFF) { // BMP code point\n            codeUnits.push(codePoint)\n          } else { // Astral code point; split in surrogate halves\n            // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n            codePoint -= 0x10000\n            highSurrogate = (codePoint >> 10) + 0xD800\n            lowSurrogate = (codePoint % 0x400) + 0xDC00\n            codeUnits.push(highSurrogate, lowSurrogate)\n          }\n          if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n            result += stringFromCharCode.apply(null, codeUnits)\n            codeUnits.length = 0\n          }\n        }\n        return result\n      }\n      /* istanbul ignore next */\n      if (Object.defineProperty) {\n        Object.defineProperty(String, 'fromCodePoint', {\n          value: fromCodePoint,\n          configurable: true,\n          writable: true\n        })\n      } else {\n        String.fromCodePoint = fromCodePoint\n      }\n    }())\n  }\n})(typeof exports === 'undefined' ? this.sax = {} : exports)\n"], "mappings": "AAAA;AAAC,CAAC,UAAUA,GAAG,EAAE;EAAE;EACjBA,GAAG,CAACC,MAAM,GAAG,UAAUC,MAAM,EAAEC,GAAG,EAAE;IAAE,OAAO,IAAIC,SAAS,CAACF,MAAM,EAAEC,GAAG,CAAC;EAAC,CAAC;EACzEH,GAAG,CAACI,SAAS,GAAGA,SAAS;EACzBJ,GAAG,CAACK,SAAS,GAAGA,SAAS;EACzBL,GAAG,CAACM,YAAY,GAAGA,YAAY;;EAE/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAN,GAAG,CAACO,iBAAiB,GAAG,EAAE,GAAG,IAAI;EAEjC,IAAIC,OAAO,GAAG,CACZ,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EACvD,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,YAAY,EACtD,aAAa,EAAE,OAAO,EAAE,QAAQ,CACjC;EAEDR,GAAG,CAACS,MAAM,GAAG,CACX,MAAM,EACN,uBAAuB,EACvB,iBAAiB,EACjB,SAAS,EACT,SAAS,EACT,cAAc,EACd,WAAW,EACX,SAAS,EACT,UAAU,EACV,WAAW,EACX,OAAO,EACP,YAAY,EACZ,OAAO,EACP,KAAK,EACL,OAAO,EACP,QAAQ,EACR,eAAe,EACf,gBAAgB,CACjB;EAED,SAASL,SAASA,CAAEF,MAAM,EAAEC,GAAG,EAAE;IAC/B,IAAI,EAAE,IAAI,YAAYC,SAAS,CAAC,EAAE;MAChC,OAAO,IAAIA,SAAS,CAACF,MAAM,EAAEC,GAAG,CAAC;IACnC;IAEA,IAAIF,MAAM,GAAG,IAAI;IACjBS,YAAY,CAACT,MAAM,CAAC;IACpBA,MAAM,CAACU,CAAC,GAAGV,MAAM,CAACW,CAAC,GAAG,EAAE;IACxBX,MAAM,CAACY,mBAAmB,GAAGb,GAAG,CAACO,iBAAiB;IAClDN,MAAM,CAACE,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACtBF,MAAM,CAACE,GAAG,CAACW,SAAS,GAAGb,MAAM,CAACE,GAAG,CAACW,SAAS,IAAIb,MAAM,CAACE,GAAG,CAACY,aAAa;IACvEd,MAAM,CAACe,SAAS,GAAGf,MAAM,CAACE,GAAG,CAACW,SAAS,GAAG,aAAa,GAAG,aAAa;IACvEb,MAAM,CAACgB,IAAI,GAAG,EAAE;IAChBhB,MAAM,CAACiB,MAAM,GAAGjB,MAAM,CAACkB,UAAU,GAAGlB,MAAM,CAACmB,OAAO,GAAG,KAAK;IAC1DnB,MAAM,CAACoB,GAAG,GAAGpB,MAAM,CAACqB,KAAK,GAAG,IAAI;IAChCrB,MAAM,CAACC,MAAM,GAAG,CAAC,CAACA,MAAM;IACxBD,MAAM,CAACsB,QAAQ,GAAG,CAAC,EAAErB,MAAM,IAAID,MAAM,CAACE,GAAG,CAACoB,QAAQ,CAAC;IACnDtB,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACC,KAAK;IACtBzB,MAAM,CAAC0B,cAAc,GAAG1B,MAAM,CAACE,GAAG,CAACwB,cAAc;IACjD1B,MAAM,CAAC2B,QAAQ,GAAG3B,MAAM,CAAC0B,cAAc,GAAGE,MAAM,CAACC,MAAM,CAAC9B,GAAG,CAAC+B,YAAY,CAAC,GAAGF,MAAM,CAACC,MAAM,CAAC9B,GAAG,CAAC4B,QAAQ,CAAC;IACvG3B,MAAM,CAAC+B,UAAU,GAAG,EAAE;;IAEtB;IACA;IACA;IACA,IAAI/B,MAAM,CAACE,GAAG,CAAC8B,KAAK,EAAE;MACpBhC,MAAM,CAACiC,EAAE,GAAGL,MAAM,CAACC,MAAM,CAACK,MAAM,CAAC;IACnC;;IAEA;IACAlC,MAAM,CAACmC,aAAa,GAAGnC,MAAM,CAACE,GAAG,CAACkC,QAAQ,KAAK,KAAK;IACpD,IAAIpC,MAAM,CAACmC,aAAa,EAAE;MACxBnC,MAAM,CAACoC,QAAQ,GAAGpC,MAAM,CAACqC,IAAI,GAAGrC,MAAM,CAACsC,MAAM,GAAG,CAAC;IACnD;IACAC,IAAI,CAACvC,MAAM,EAAE,SAAS,CAAC;EACzB;EAEA,IAAI,CAAC4B,MAAM,CAACC,MAAM,EAAE;IAClBD,MAAM,CAACC,MAAM,GAAG,UAAUW,CAAC,EAAE;MAC3B,SAASC,CAACA,CAAA,EAAI,CAAC;MACfA,CAAC,CAACC,SAAS,GAAGF,CAAC;MACf,IAAIG,IAAI,GAAG,IAAIF,CAAC,CAAC,CAAC;MAClB,OAAOE,IAAI;IACb,CAAC;EACH;EAEA,IAAI,CAACf,MAAM,CAACgB,IAAI,EAAE;IAChBhB,MAAM,CAACgB,IAAI,GAAG,UAAUJ,CAAC,EAAE;MACzB,IAAIK,CAAC,GAAG,EAAE;MACV,KAAK,IAAIC,CAAC,IAAIN,CAAC,EAAE,IAAIA,CAAC,CAACO,cAAc,CAACD,CAAC,CAAC,EAAED,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC;MACnD,OAAOD,CAAC;IACV,CAAC;EACH;EAEA,SAASI,iBAAiBA,CAAEjD,MAAM,EAAE;IAClC,IAAIkD,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACrD,GAAG,CAACO,iBAAiB,EAAE,EAAE,CAAC;IACpD,IAAI+C,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEQ,CAAC,GAAG/C,OAAO,CAACgD,MAAM,EAAET,CAAC,GAAGQ,CAAC,EAAER,CAAC,EAAE,EAAE;MAC9C,IAAIU,GAAG,GAAGxD,MAAM,CAACO,OAAO,CAACuC,CAAC,CAAC,CAAC,CAACS,MAAM;MACnC,IAAIC,GAAG,GAAGN,UAAU,EAAE;QACpB;QACA;QACA;QACA;QACA,QAAQ3C,OAAO,CAACuC,CAAC,CAAC;UAChB,KAAK,UAAU;YACbW,SAAS,CAACzD,MAAM,CAAC;YACjB;UAEF,KAAK,OAAO;YACV0D,QAAQ,CAAC1D,MAAM,EAAE,SAAS,EAAEA,MAAM,CAAC2D,KAAK,CAAC;YACzC3D,MAAM,CAAC2D,KAAK,GAAG,EAAE;YACjB;UAEF,KAAK,QAAQ;YACXD,QAAQ,CAAC1D,MAAM,EAAE,UAAU,EAAEA,MAAM,CAAC4D,MAAM,CAAC;YAC3C5D,MAAM,CAAC4D,MAAM,GAAG,EAAE;YAClB;UAEF;YACEvC,KAAK,CAACrB,MAAM,EAAE,8BAA8B,GAAGO,OAAO,CAACuC,CAAC,CAAC,CAAC;QAC9D;MACF;MACAO,SAAS,GAAGF,IAAI,CAACC,GAAG,CAACC,SAAS,EAAEG,GAAG,CAAC;IACtC;IACA;IACA,IAAIK,CAAC,GAAG9D,GAAG,CAACO,iBAAiB,GAAG+C,SAAS;IACzCrD,MAAM,CAACY,mBAAmB,GAAGiD,CAAC,GAAG7D,MAAM,CAACoC,QAAQ;EAClD;EAEA,SAAS3B,YAAYA,CAAET,MAAM,EAAE;IAC7B,KAAK,IAAI8C,CAAC,GAAG,CAAC,EAAEQ,CAAC,GAAG/C,OAAO,CAACgD,MAAM,EAAET,CAAC,GAAGQ,CAAC,EAAER,CAAC,EAAE,EAAE;MAC9C9C,MAAM,CAACO,OAAO,CAACuC,CAAC,CAAC,CAAC,GAAG,EAAE;IACzB;EACF;EAEA,SAASgB,YAAYA,CAAE9D,MAAM,EAAE;IAC7ByD,SAAS,CAACzD,MAAM,CAAC;IACjB,IAAIA,MAAM,CAAC2D,KAAK,KAAK,EAAE,EAAE;MACvBD,QAAQ,CAAC1D,MAAM,EAAE,SAAS,EAAEA,MAAM,CAAC2D,KAAK,CAAC;MACzC3D,MAAM,CAAC2D,KAAK,GAAG,EAAE;IACnB;IACA,IAAI3D,MAAM,CAAC4D,MAAM,KAAK,EAAE,EAAE;MACxBF,QAAQ,CAAC1D,MAAM,EAAE,UAAU,EAAEA,MAAM,CAAC4D,MAAM,CAAC;MAC3C5D,MAAM,CAAC4D,MAAM,GAAG,EAAE;IACpB;EACF;EAEAzD,SAAS,CAACuC,SAAS,GAAG;IACpBqB,GAAG,EAAE,SAAAA,CAAA,EAAY;MAAEA,GAAG,CAAC,IAAI,CAAC;IAAC,CAAC;IAC9BC,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAE,SAAAA,CAAA,EAAY;MAAE,IAAI,CAAC5C,KAAK,GAAG,IAAI;MAAE,OAAO,IAAI;IAAC,CAAC;IACtD6C,KAAK,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,IAAI,CAACF,KAAK,CAAC,IAAI,CAAC;IAAC,CAAC;IAC9CG,KAAK,EAAE,SAAAA,CAAA,EAAY;MAAEL,YAAY,CAAC,IAAI,CAAC;IAAC;EAC1C,CAAC;EAED,IAAIM,MAAM;EACV,IAAI;IACFA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,MAAM;EACnC,CAAC,CAAC,OAAOE,EAAE,EAAE;IACXF,MAAM,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;EACzB;EAEA,IAAIG,WAAW,GAAGxE,GAAG,CAACS,MAAM,CAACgE,MAAM,CAAC,UAAUC,EAAE,EAAE;IAChD,OAAOA,EAAE,KAAK,OAAO,IAAIA,EAAE,KAAK,KAAK;EACvC,CAAC,CAAC;EAEF,SAASpE,YAAYA,CAAEJ,MAAM,EAAEC,GAAG,EAAE;IAClC,OAAO,IAAIE,SAAS,CAACH,MAAM,EAAEC,GAAG,CAAC;EACnC;EAEA,SAASE,SAASA,CAAEH,MAAM,EAAEC,GAAG,EAAE;IAC/B,IAAI,EAAE,IAAI,YAAYE,SAAS,CAAC,EAAE;MAChC,OAAO,IAAIA,SAAS,CAACH,MAAM,EAAEC,GAAG,CAAC;IACnC;IAEAkE,MAAM,CAACM,KAAK,CAAC,IAAI,CAAC;IAElB,IAAI,CAACC,OAAO,GAAG,IAAIxE,SAAS,CAACF,MAAM,EAAEC,GAAG,CAAC;IACzC,IAAI,CAAC0E,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;IAEpB,IAAIC,EAAE,GAAG,IAAI;IAEb,IAAI,CAACH,OAAO,CAACI,KAAK,GAAG,YAAY;MAC/BD,EAAE,CAACvC,IAAI,CAAC,KAAK,CAAC;IAChB,CAAC;IAED,IAAI,CAACoC,OAAO,CAACK,OAAO,GAAG,UAAUC,EAAE,EAAE;MACnCH,EAAE,CAACvC,IAAI,CAAC,OAAO,EAAE0C,EAAE,CAAC;;MAEpB;MACA;MACAH,EAAE,CAACH,OAAO,CAACtD,KAAK,GAAG,IAAI;IACzB,CAAC;IAED,IAAI,CAAC6D,QAAQ,GAAG,IAAI;IAEpBX,WAAW,CAACY,OAAO,CAAC,UAAUV,EAAE,EAAE;MAChC7C,MAAM,CAACwD,cAAc,CAACN,EAAE,EAAE,IAAI,GAAGL,EAAE,EAAE;QACnCY,GAAG,EAAE,SAAAA,CAAA,EAAY;UACf,OAAOP,EAAE,CAACH,OAAO,CAAC,IAAI,GAAGF,EAAE,CAAC;QAC9B,CAAC;QACDa,GAAG,EAAE,SAAAA,CAAUC,CAAC,EAAE;UAChB,IAAI,CAACA,CAAC,EAAE;YACNT,EAAE,CAACU,kBAAkB,CAACf,EAAE,CAAC;YACzBK,EAAE,CAACH,OAAO,CAAC,IAAI,GAAGF,EAAE,CAAC,GAAGc,CAAC;YACzB,OAAOA,CAAC;UACV;UACAT,EAAE,CAACW,EAAE,CAAChB,EAAE,EAAEc,CAAC,CAAC;QACd,CAAC;QACDG,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAvF,SAAS,CAACsC,SAAS,GAAGd,MAAM,CAACC,MAAM,CAACuC,MAAM,CAAC1B,SAAS,EAAE;IACpDkD,WAAW,EAAE;MACXC,KAAK,EAAEzF;IACT;EACF,CAAC,CAAC;EAEFA,SAAS,CAACsC,SAAS,CAACsB,KAAK,GAAG,UAAU8B,IAAI,EAAE;IAC1C,IAAI,OAAOC,MAAM,KAAK,UAAU,IAC9B,OAAOA,MAAM,CAACC,QAAQ,KAAK,UAAU,IACrCD,MAAM,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;MACvB,IAAI,CAAC,IAAI,CAACZ,QAAQ,EAAE;QAClB,IAAIe,EAAE,GAAG5B,OAAO,CAAC,gBAAgB,CAAC,CAAC6B,aAAa;QAChD,IAAI,CAAChB,QAAQ,GAAG,IAAIe,EAAE,CAAC,MAAM,CAAC;MAChC;MACAH,IAAI,GAAG,IAAI,CAACZ,QAAQ,CAAClB,KAAK,CAAC8B,IAAI,CAAC;IAClC;IAEA,IAAI,CAACnB,OAAO,CAACX,KAAK,CAAC8B,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC;IACnC,IAAI,CAAC5D,IAAI,CAAC,MAAM,EAAEuD,IAAI,CAAC;IACvB,OAAO,IAAI;EACb,CAAC;EAED1F,SAAS,CAACsC,SAAS,CAACqB,GAAG,GAAG,UAAUqC,KAAK,EAAE;IACzC,IAAIA,KAAK,IAAIA,KAAK,CAAC7C,MAAM,EAAE;MACzB,IAAI,CAACS,KAAK,CAACoC,KAAK,CAAC;IACnB;IACA,IAAI,CAACzB,OAAO,CAACZ,GAAG,CAAC,CAAC;IAClB,OAAO,IAAI;EACb,CAAC;EAED3D,SAAS,CAACsC,SAAS,CAAC+C,EAAE,GAAG,UAAUhB,EAAE,EAAE4B,OAAO,EAAE;IAC9C,IAAIvB,EAAE,GAAG,IAAI;IACb,IAAI,CAACA,EAAE,CAACH,OAAO,CAAC,IAAI,GAAGF,EAAE,CAAC,IAAIF,WAAW,CAAC+B,OAAO,CAAC7B,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5DK,EAAE,CAACH,OAAO,CAAC,IAAI,GAAGF,EAAE,CAAC,GAAG,YAAY;QAClC,IAAI8B,IAAI,GAAGC,SAAS,CAACjD,MAAM,KAAK,CAAC,GAAG,CAACiD,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC/B,KAAK,CAAC,IAAI,EAAE8B,SAAS,CAAC;QACjFD,IAAI,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEjC,EAAE,CAAC;QACrBK,EAAE,CAACvC,IAAI,CAACmC,KAAK,CAACI,EAAE,EAAEyB,IAAI,CAAC;MACzB,CAAC;IACH;IAEA,OAAOnC,MAAM,CAAC1B,SAAS,CAAC+C,EAAE,CAACkB,IAAI,CAAC7B,EAAE,EAAEL,EAAE,EAAE4B,OAAO,CAAC;EAClD,CAAC;;EAED;EACA;EACA,IAAIO,KAAK,GAAG,SAAS;EACrB,IAAIC,OAAO,GAAG,SAAS;EACvB,IAAIC,aAAa,GAAG,sCAAsC;EAC1D,IAAIC,eAAe,GAAG,+BAA+B;EACrD,IAAI7E,MAAM,GAAG;IAAE8E,GAAG,EAAEF,aAAa;IAAE9E,KAAK,EAAE+E;EAAgB,CAAC;;EAE3D;EACA;EACA;EACA;EACA;EACA;EACA,IAAIE,SAAS,GAAG,2JAA2J;EAE3K,IAAIC,QAAQ,GAAG,+LAA+L;EAE9M,IAAIC,WAAW,GAAG,4JAA4J;EAC9K,IAAIC,UAAU,GAAG,gMAAgM;EAEjN,SAASC,YAAYA,CAAE1G,CAAC,EAAE;IACxB,OAAOA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,IAAI;EAC5D;EAEA,SAAS2G,OAAOA,CAAE3G,CAAC,EAAE;IACnB,OAAOA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,IAAI;EAChC;EAEA,SAAS4G,WAAWA,CAAE5G,CAAC,EAAE;IACvB,OAAOA,CAAC,KAAK,GAAG,IAAI0G,YAAY,CAAC1G,CAAC,CAAC;EACrC;EAEA,SAAS6G,OAAOA,CAAEC,KAAK,EAAE9G,CAAC,EAAE;IAC1B,OAAO8G,KAAK,CAACC,IAAI,CAAC/G,CAAC,CAAC;EACtB;EAEA,SAASgH,QAAQA,CAAEF,KAAK,EAAE9G,CAAC,EAAE;IAC3B,OAAO,CAAC6G,OAAO,CAACC,KAAK,EAAE9G,CAAC,CAAC;EAC3B;EAEA,IAAIa,CAAC,GAAG,CAAC;EACTzB,GAAG,CAAC6H,KAAK,GAAG;IACVnG,KAAK,EAAED,CAAC,EAAE;IAAE;IACZqG,gBAAgB,EAAErG,CAAC,EAAE;IAAE;IACvBsG,IAAI,EAAEtG,CAAC,EAAE;IAAE;IACXuG,WAAW,EAAEvG,CAAC,EAAE;IAAE;IAClBwG,SAAS,EAAExG,CAAC,EAAE;IAAE;IAChByG,SAAS,EAAEzG,CAAC,EAAE;IAAE;IAChB0G,gBAAgB,EAAE1G,CAAC,EAAE;IAAE;IACvBqF,OAAO,EAAErF,CAAC,EAAE;IAAE;IACd2G,cAAc,EAAE3G,CAAC,EAAE;IAAE;IACrB4G,WAAW,EAAE5G,CAAC,EAAE;IAAE;IAClB6G,kBAAkB,EAAE7G,CAAC,EAAE;IAAE;IACzB8G,gBAAgB,EAAE9G,CAAC,EAAE;IAAE;IACvB+G,OAAO,EAAE/G,CAAC,EAAE;IAAE;IACdgH,cAAc,EAAEhH,CAAC,EAAE;IAAE;IACrBiH,aAAa,EAAEjH,CAAC,EAAE;IAAE;IACpBoF,KAAK,EAAEpF,CAAC,EAAE;IAAE;IACZkH,YAAY,EAAElH,CAAC,EAAE;IAAE;IACnBmH,cAAc,EAAEnH,CAAC,EAAE;IAAE;IACrBoH,SAAS,EAAEpH,CAAC,EAAE;IAAE;IAChBqH,cAAc,EAAErH,CAAC,EAAE;IAAE;IACrBsH,gBAAgB,EAAEtH,CAAC,EAAE;IAAE;IACvBuH,QAAQ,EAAEvH,CAAC,EAAE;IAAE;IACfwH,cAAc,EAAExH,CAAC,EAAE;IAAE;IACrByH,MAAM,EAAEzH,CAAC,EAAE;IAAE;IACb0H,WAAW,EAAE1H,CAAC,EAAE;IAAE;IAClB2H,qBAAqB,EAAE3H,CAAC,EAAE;IAAE;IAC5B4H,YAAY,EAAE5H,CAAC,EAAE;IAAE;IACnB6H,mBAAmB,EAAE7H,CAAC,EAAE;IAAE;IAC1B8H,mBAAmB,EAAE9H,CAAC,EAAE;IAAE;IAC1B+H,qBAAqB,EAAE/H,CAAC,EAAE;IAAE;IAC5BgI,qBAAqB,EAAEhI,CAAC,EAAE;IAAE;IAC5BiI,qBAAqB,EAAEjI,CAAC,EAAE;IAAE;IAC5BkI,SAAS,EAAElI,CAAC,EAAE;IAAE;IAChBmI,mBAAmB,EAAEnI,CAAC,EAAE;IAAE;IAC1BoI,MAAM,EAAEpI,CAAC,EAAE;IAAE;IACbqI,aAAa,EAAErI,CAAC,EAAE,CAAC;EACrB,CAAC;EAEDzB,GAAG,CAAC+B,YAAY,GAAG;IACjB,KAAK,EAAE,GAAG;IACV,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;EACV,CAAC;EAED/B,GAAG,CAAC4B,QAAQ,GAAG;IACb,KAAK,EAAE,GAAG;IACV,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,KAAK,EAAE,GAAG;IACV,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,KAAK,EAAE,GAAG;IACV,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;IACV,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,GAAG;IACd,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;IACV,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,SAAS,EAAE,GAAG;IACd,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,GAAG;IACV,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;IACV,SAAS,EAAE,GAAG;IACd,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,GAAG;IACd,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;IACV,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,SAAS,EAAE,GAAG;IACd,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,GAAG;IACV,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;IACV,SAAS,EAAE,GAAG;IACd,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,OAAO,EAAE,GAAG;IACZ,UAAU,EAAE,GAAG;IACf,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,SAAS,EAAE,IAAI;IACf,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,IAAI;IACV,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,IAAI;IACd,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,IAAI;IACV,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE;EACX,CAAC;EAEDC,MAAM,CAACgB,IAAI,CAAC7C,GAAG,CAAC4B,QAAQ,CAAC,CAACwD,OAAO,CAAC,UAAU2E,GAAG,EAAE;IAC/C,IAAIC,CAAC,GAAGhK,GAAG,CAAC4B,QAAQ,CAACmI,GAAG,CAAC;IACzB,IAAIE,CAAC,GAAG,OAAOD,CAAC,KAAK,QAAQ,GAAGE,MAAM,CAACC,YAAY,CAACH,CAAC,CAAC,GAAGA,CAAC;IAC1DhK,GAAG,CAAC4B,QAAQ,CAACmI,GAAG,CAAC,GAAGE,CAAC;EACvB,CAAC,CAAC;EAEF,KAAK,IAAIA,CAAC,IAAIjK,GAAG,CAAC6H,KAAK,EAAE;IACvB7H,GAAG,CAAC6H,KAAK,CAAC7H,GAAG,CAAC6H,KAAK,CAACoC,CAAC,CAAC,CAAC,GAAGA,CAAC;EAC7B;;EAEA;EACAxI,CAAC,GAAGzB,GAAG,CAAC6H,KAAK;EAEb,SAASrF,IAAIA,CAAEvC,MAAM,EAAEmK,KAAK,EAAErE,IAAI,EAAE;IAClC9F,MAAM,CAACmK,KAAK,CAAC,IAAInK,MAAM,CAACmK,KAAK,CAAC,CAACrE,IAAI,CAAC;EACtC;EAEA,SAASpC,QAAQA,CAAE1D,MAAM,EAAEoK,QAAQ,EAAEtE,IAAI,EAAE;IACzC,IAAI9F,MAAM,CAACqK,QAAQ,EAAE5G,SAAS,CAACzD,MAAM,CAAC;IACtCuC,IAAI,CAACvC,MAAM,EAAEoK,QAAQ,EAAEtE,IAAI,CAAC;EAC9B;EAEA,SAASrC,SAASA,CAAEzD,MAAM,EAAE;IAC1BA,MAAM,CAACqK,QAAQ,GAAGC,QAAQ,CAACtK,MAAM,CAACE,GAAG,EAAEF,MAAM,CAACqK,QAAQ,CAAC;IACvD,IAAIrK,MAAM,CAACqK,QAAQ,EAAE9H,IAAI,CAACvC,MAAM,EAAE,QAAQ,EAAEA,MAAM,CAACqK,QAAQ,CAAC;IAC5DrK,MAAM,CAACqK,QAAQ,GAAG,EAAE;EACtB;EAEA,SAASC,QAAQA,CAAEpK,GAAG,EAAEqK,IAAI,EAAE;IAC5B,IAAIrK,GAAG,CAACsK,IAAI,EAAED,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;IAChC,IAAItK,GAAG,CAACuK,SAAS,EAAEF,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IACnD,OAAOH,IAAI;EACb;EAEA,SAASlJ,KAAKA,CAAErB,MAAM,EAAEiF,EAAE,EAAE;IAC1BxB,SAAS,CAACzD,MAAM,CAAC;IACjB,IAAIA,MAAM,CAACmC,aAAa,EAAE;MACxB8C,EAAE,IAAI,UAAU,GAAGjF,MAAM,CAACqC,IAAI,GAC5B,YAAY,GAAGrC,MAAM,CAACsC,MAAM,GAC5B,UAAU,GAAGtC,MAAM,CAACW,CAAC;IACzB;IACAsE,EAAE,GAAG,IAAI0F,KAAK,CAAC1F,EAAE,CAAC;IAClBjF,MAAM,CAACqB,KAAK,GAAG4D,EAAE;IACjB1C,IAAI,CAACvC,MAAM,EAAE,SAAS,EAAEiF,EAAE,CAAC;IAC3B,OAAOjF,MAAM;EACf;EAEA,SAAS+D,GAAGA,CAAE/D,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACmB,OAAO,IAAI,CAACnB,MAAM,CAACkB,UAAU,EAAE0J,UAAU,CAAC5K,MAAM,EAAE,mBAAmB,CAAC;IACjF,IAAKA,MAAM,CAACuB,KAAK,KAAKC,CAAC,CAACC,KAAK,IAC1BzB,MAAM,CAACuB,KAAK,KAAKC,CAAC,CAACqG,gBAAiB,IACpC7H,MAAM,CAACuB,KAAK,KAAKC,CAAC,CAACsG,IAAK,EAAE;MAC3BzG,KAAK,CAACrB,MAAM,EAAE,gBAAgB,CAAC;IACjC;IACAyD,SAAS,CAACzD,MAAM,CAAC;IACjBA,MAAM,CAACW,CAAC,GAAG,EAAE;IACbX,MAAM,CAACiB,MAAM,GAAG,IAAI;IACpBsB,IAAI,CAACvC,MAAM,EAAE,OAAO,CAAC;IACrBG,SAAS,CAACwG,IAAI,CAAC3G,MAAM,EAAEA,MAAM,CAACC,MAAM,EAAED,MAAM,CAACE,GAAG,CAAC;IACjD,OAAOF,MAAM;EACf;EAEA,SAAS4K,UAAUA,CAAE5K,MAAM,EAAE6K,OAAO,EAAE;IACpC,IAAI,OAAO7K,MAAM,KAAK,QAAQ,IAAI,EAAEA,MAAM,YAAYG,SAAS,CAAC,EAAE;MAChE,MAAM,IAAIwK,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IACA,IAAI3K,MAAM,CAACC,MAAM,EAAE;MACjBoB,KAAK,CAACrB,MAAM,EAAE6K,OAAO,CAAC;IACxB;EACF;EAEA,SAASC,MAAMA,CAAE9K,MAAM,EAAE;IACvB,IAAI,CAACA,MAAM,CAACC,MAAM,EAAED,MAAM,CAAC+K,OAAO,GAAG/K,MAAM,CAAC+K,OAAO,CAAC/K,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC;IACvE,IAAIiK,MAAM,GAAGhL,MAAM,CAACgB,IAAI,CAAChB,MAAM,CAACgB,IAAI,CAACuC,MAAM,GAAG,CAAC,CAAC,IAAIvD,MAAM;IAC1D,IAAIoB,GAAG,GAAGpB,MAAM,CAACoB,GAAG,GAAG;MAAE6J,IAAI,EAAEjL,MAAM,CAAC+K,OAAO;MAAEG,UAAU,EAAE,CAAC;IAAE,CAAC;;IAE/D;IACA,IAAIlL,MAAM,CAACE,GAAG,CAAC8B,KAAK,EAAE;MACpBZ,GAAG,CAACa,EAAE,GAAG+I,MAAM,CAAC/I,EAAE;IACpB;IACAjC,MAAM,CAAC+B,UAAU,CAACwB,MAAM,GAAG,CAAC;IAC5BG,QAAQ,CAAC1D,MAAM,EAAE,gBAAgB,EAAEoB,GAAG,CAAC;EACzC;EAEA,SAAS+J,KAAKA,CAAEF,IAAI,EAAEG,SAAS,EAAE;IAC/B,IAAItI,CAAC,GAAGmI,IAAI,CAAC3E,OAAO,CAAC,GAAG,CAAC;IACzB,IAAI+E,QAAQ,GAAGvI,CAAC,GAAG,CAAC,GAAG,CAAE,EAAE,EAAEmI,IAAI,CAAE,GAAGA,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC;IACrD,IAAIC,MAAM,GAAGF,QAAQ,CAAC,CAAC,CAAC;IACxB,IAAIG,KAAK,GAAGH,QAAQ,CAAC,CAAC,CAAC;;IAEvB;IACA,IAAID,SAAS,IAAIH,IAAI,KAAK,OAAO,EAAE;MACjCM,MAAM,GAAG,OAAO;MAChBC,KAAK,GAAG,EAAE;IACZ;IAEA,OAAO;MAAED,MAAM,EAAEA,MAAM;MAAEC,KAAK,EAAEA;IAAM,CAAC;EACzC;EAEA,SAASC,MAAMA,CAAEzL,MAAM,EAAE;IACvB,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;MAClBD,MAAM,CAAC0L,UAAU,GAAG1L,MAAM,CAAC0L,UAAU,CAAC1L,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC;IAC3D;IAEA,IAAIf,MAAM,CAAC+B,UAAU,CAACuE,OAAO,CAACtG,MAAM,CAAC0L,UAAU,CAAC,KAAK,CAAC,CAAC,IACrD1L,MAAM,CAACoB,GAAG,CAAC8J,UAAU,CAACnI,cAAc,CAAC/C,MAAM,CAAC0L,UAAU,CAAC,EAAE;MACzD1L,MAAM,CAAC0L,UAAU,GAAG1L,MAAM,CAAC2L,WAAW,GAAG,EAAE;MAC3C;IACF;IAEA,IAAI3L,MAAM,CAACE,GAAG,CAAC8B,KAAK,EAAE;MACpB,IAAI4J,EAAE,GAAGT,KAAK,CAACnL,MAAM,CAAC0L,UAAU,EAAE,IAAI,CAAC;MACvC,IAAIH,MAAM,GAAGK,EAAE,CAACL,MAAM;MACtB,IAAIC,KAAK,GAAGI,EAAE,CAACJ,KAAK;MAEpB,IAAID,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAIC,KAAK,KAAK,KAAK,IAAIxL,MAAM,CAAC2L,WAAW,KAAK7E,aAAa,EAAE;UAC3D8D,UAAU,CAAC5K,MAAM,EACf,+BAA+B,GAAG8G,aAAa,GAAG,IAAI,GACtD,UAAU,GAAG9G,MAAM,CAAC2L,WAAW,CAAC;QACpC,CAAC,MAAM,IAAIH,KAAK,KAAK,OAAO,IAAIxL,MAAM,CAAC2L,WAAW,KAAK5E,eAAe,EAAE;UACtE6D,UAAU,CAAC5K,MAAM,EACf,iCAAiC,GAAG+G,eAAe,GAAG,IAAI,GAC1D,UAAU,GAAG/G,MAAM,CAAC2L,WAAW,CAAC;QACpC,CAAC,MAAM;UACL,IAAIvK,GAAG,GAAGpB,MAAM,CAACoB,GAAG;UACpB,IAAI4J,MAAM,GAAGhL,MAAM,CAACgB,IAAI,CAAChB,MAAM,CAACgB,IAAI,CAACuC,MAAM,GAAG,CAAC,CAAC,IAAIvD,MAAM;UAC1D,IAAIoB,GAAG,CAACa,EAAE,KAAK+I,MAAM,CAAC/I,EAAE,EAAE;YACxBb,GAAG,CAACa,EAAE,GAAGL,MAAM,CAACC,MAAM,CAACmJ,MAAM,CAAC/I,EAAE,CAAC;UACnC;UACAb,GAAG,CAACa,EAAE,CAACuJ,KAAK,CAAC,GAAGxL,MAAM,CAAC2L,WAAW;QACpC;MACF;;MAEA;MACA;MACA;MACA3L,MAAM,CAAC+B,UAAU,CAACiB,IAAI,CAAC,CAAChD,MAAM,CAAC0L,UAAU,EAAE1L,MAAM,CAAC2L,WAAW,CAAC,CAAC;IACjE,CAAC,MAAM;MACL;MACA3L,MAAM,CAACoB,GAAG,CAAC8J,UAAU,CAAClL,MAAM,CAAC0L,UAAU,CAAC,GAAG1L,MAAM,CAAC2L,WAAW;MAC7DjI,QAAQ,CAAC1D,MAAM,EAAE,aAAa,EAAE;QAC9BiL,IAAI,EAAEjL,MAAM,CAAC0L,UAAU;QACvB7F,KAAK,EAAE7F,MAAM,CAAC2L;MAChB,CAAC,CAAC;IACJ;IAEA3L,MAAM,CAAC0L,UAAU,GAAG1L,MAAM,CAAC2L,WAAW,GAAG,EAAE;EAC7C;EAEA,SAASE,OAAOA,CAAE7L,MAAM,EAAE8L,WAAW,EAAE;IACrC,IAAI9L,MAAM,CAACE,GAAG,CAAC8B,KAAK,EAAE;MACpB;MACA,IAAIZ,GAAG,GAAGpB,MAAM,CAACoB,GAAG;;MAEpB;MACA,IAAIwK,EAAE,GAAGT,KAAK,CAACnL,MAAM,CAAC+K,OAAO,CAAC;MAC9B3J,GAAG,CAACmK,MAAM,GAAGK,EAAE,CAACL,MAAM;MACtBnK,GAAG,CAACoK,KAAK,GAAGI,EAAE,CAACJ,KAAK;MACpBpK,GAAG,CAAC2K,GAAG,GAAG3K,GAAG,CAACa,EAAE,CAAC2J,EAAE,CAACL,MAAM,CAAC,IAAI,EAAE;MAEjC,IAAInK,GAAG,CAACmK,MAAM,IAAI,CAACnK,GAAG,CAAC2K,GAAG,EAAE;QAC1BnB,UAAU,CAAC5K,MAAM,EAAE,4BAA4B,GAC7CgM,IAAI,CAACC,SAAS,CAACjM,MAAM,CAAC+K,OAAO,CAAC,CAAC;QACjC3J,GAAG,CAAC2K,GAAG,GAAGH,EAAE,CAACL,MAAM;MACrB;MAEA,IAAIP,MAAM,GAAGhL,MAAM,CAACgB,IAAI,CAAChB,MAAM,CAACgB,IAAI,CAACuC,MAAM,GAAG,CAAC,CAAC,IAAIvD,MAAM;MAC1D,IAAIoB,GAAG,CAACa,EAAE,IAAI+I,MAAM,CAAC/I,EAAE,KAAKb,GAAG,CAACa,EAAE,EAAE;QAClCL,MAAM,CAACgB,IAAI,CAACxB,GAAG,CAACa,EAAE,CAAC,CAACkD,OAAO,CAAC,UAAU+G,CAAC,EAAE;UACvCxI,QAAQ,CAAC1D,MAAM,EAAE,iBAAiB,EAAE;YAClCuL,MAAM,EAAEW,CAAC;YACTH,GAAG,EAAE3K,GAAG,CAACa,EAAE,CAACiK,CAAC;UACf,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;;MAEA;MACA;MACA;MACA,KAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEQ,CAAC,GAAGtD,MAAM,CAAC+B,UAAU,CAACwB,MAAM,EAAET,CAAC,GAAGQ,CAAC,EAAER,CAAC,EAAE,EAAE;QACxD,IAAIqJ,EAAE,GAAGnM,MAAM,CAAC+B,UAAU,CAACe,CAAC,CAAC;QAC7B,IAAImI,IAAI,GAAGkB,EAAE,CAAC,CAAC,CAAC;QAChB,IAAItG,KAAK,GAAGsG,EAAE,CAAC,CAAC,CAAC;QACjB,IAAId,QAAQ,GAAGF,KAAK,CAACF,IAAI,EAAE,IAAI,CAAC;QAChC,IAAIM,MAAM,GAAGF,QAAQ,CAACE,MAAM;QAC5B,IAAIC,KAAK,GAAGH,QAAQ,CAACG,KAAK;QAC1B,IAAIO,GAAG,GAAGR,MAAM,KAAK,EAAE,GAAG,EAAE,GAAInK,GAAG,CAACa,EAAE,CAACsJ,MAAM,CAAC,IAAI,EAAG;QACrD,IAAI1I,CAAC,GAAG;UACNoI,IAAI,EAAEA,IAAI;UACVpF,KAAK,EAAEA,KAAK;UACZ0F,MAAM,EAAEA,MAAM;UACdC,KAAK,EAAEA,KAAK;UACZO,GAAG,EAAEA;QACP,CAAC;;QAED;QACA;QACA,IAAIR,MAAM,IAAIA,MAAM,KAAK,OAAO,IAAI,CAACQ,GAAG,EAAE;UACxCnB,UAAU,CAAC5K,MAAM,EAAE,4BAA4B,GAC7CgM,IAAI,CAACC,SAAS,CAACV,MAAM,CAAC,CAAC;UACzB1I,CAAC,CAACkJ,GAAG,GAAGR,MAAM;QAChB;QACAvL,MAAM,CAACoB,GAAG,CAAC8J,UAAU,CAACD,IAAI,CAAC,GAAGpI,CAAC;QAC/Ba,QAAQ,CAAC1D,MAAM,EAAE,aAAa,EAAE6C,CAAC,CAAC;MACpC;MACA7C,MAAM,CAAC+B,UAAU,CAACwB,MAAM,GAAG,CAAC;IAC9B;IAEAvD,MAAM,CAACoB,GAAG,CAACgL,aAAa,GAAG,CAAC,CAACN,WAAW;;IAExC;IACA9L,MAAM,CAACmB,OAAO,GAAG,IAAI;IACrBnB,MAAM,CAACgB,IAAI,CAACgC,IAAI,CAAChD,MAAM,CAACoB,GAAG,CAAC;IAC5BsC,QAAQ,CAAC1D,MAAM,EAAE,WAAW,EAAEA,MAAM,CAACoB,GAAG,CAAC;IACzC,IAAI,CAAC0K,WAAW,EAAE;MAChB;MACA,IAAI,CAAC9L,MAAM,CAACsB,QAAQ,IAAItB,MAAM,CAAC+K,OAAO,CAACsB,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;QACjErM,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoI,MAAM;MACzB,CAAC,MAAM;QACL5J,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;MACvB;MACA9H,MAAM,CAACoB,GAAG,GAAG,IAAI;MACjBpB,MAAM,CAAC+K,OAAO,GAAG,EAAE;IACrB;IACA/K,MAAM,CAAC0L,UAAU,GAAG1L,MAAM,CAAC2L,WAAW,GAAG,EAAE;IAC3C3L,MAAM,CAAC+B,UAAU,CAACwB,MAAM,GAAG,CAAC;EAC9B;EAEA,SAAS+I,QAAQA,CAAEtM,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,CAAC+K,OAAO,EAAE;MACnBH,UAAU,CAAC5K,MAAM,EAAE,wBAAwB,CAAC;MAC5CA,MAAM,CAACqK,QAAQ,IAAI,KAAK;MACxBrK,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;MACrB;IACF;IAEA,IAAI9H,MAAM,CAAC4D,MAAM,EAAE;MACjB,IAAI5D,MAAM,CAAC+K,OAAO,KAAK,QAAQ,EAAE;QAC/B/K,MAAM,CAAC4D,MAAM,IAAI,IAAI,GAAG5D,MAAM,CAAC+K,OAAO,GAAG,GAAG;QAC5C/K,MAAM,CAAC+K,OAAO,GAAG,EAAE;QACnB/K,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoI,MAAM;QACvB;MACF;MACAlG,QAAQ,CAAC1D,MAAM,EAAE,UAAU,EAAEA,MAAM,CAAC4D,MAAM,CAAC;MAC3C5D,MAAM,CAAC4D,MAAM,GAAG,EAAE;IACpB;;IAEA;IACA;IACA,IAAI2I,CAAC,GAAGvM,MAAM,CAACgB,IAAI,CAACuC,MAAM;IAC1B,IAAIwH,OAAO,GAAG/K,MAAM,CAAC+K,OAAO;IAC5B,IAAI,CAAC/K,MAAM,CAACC,MAAM,EAAE;MAClB8K,OAAO,GAAGA,OAAO,CAAC/K,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC;IACvC;IACA,IAAIyL,OAAO,GAAGzB,OAAO;IACrB,OAAOwB,CAAC,EAAE,EAAE;MACV,IAAIrI,KAAK,GAAGlE,MAAM,CAACgB,IAAI,CAACuL,CAAC,CAAC;MAC1B,IAAIrI,KAAK,CAAC+G,IAAI,KAAKuB,OAAO,EAAE;QAC1B;QACA5B,UAAU,CAAC5K,MAAM,EAAE,sBAAsB,CAAC;MAC5C,CAAC,MAAM;QACL;MACF;IACF;;IAEA;IACA,IAAIuM,CAAC,GAAG,CAAC,EAAE;MACT3B,UAAU,CAAC5K,MAAM,EAAE,yBAAyB,GAAGA,MAAM,CAAC+K,OAAO,CAAC;MAC9D/K,MAAM,CAACqK,QAAQ,IAAI,IAAI,GAAGrK,MAAM,CAAC+K,OAAO,GAAG,GAAG;MAC9C/K,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;MACrB;IACF;IACA9H,MAAM,CAAC+K,OAAO,GAAGA,OAAO;IACxB,IAAIf,CAAC,GAAGhK,MAAM,CAACgB,IAAI,CAACuC,MAAM;IAC1B,OAAOyG,CAAC,EAAE,GAAGuC,CAAC,EAAE;MACd,IAAInL,GAAG,GAAGpB,MAAM,CAACoB,GAAG,GAAGpB,MAAM,CAACgB,IAAI,CAACyL,GAAG,CAAC,CAAC;MACxCzM,MAAM,CAAC+K,OAAO,GAAG/K,MAAM,CAACoB,GAAG,CAAC6J,IAAI;MAChCvH,QAAQ,CAAC1D,MAAM,EAAE,YAAY,EAAEA,MAAM,CAAC+K,OAAO,CAAC;MAE9C,IAAI2B,CAAC,GAAG,CAAC,CAAC;MACV,KAAK,IAAI5J,CAAC,IAAI1B,GAAG,CAACa,EAAE,EAAE;QACpByK,CAAC,CAAC5J,CAAC,CAAC,GAAG1B,GAAG,CAACa,EAAE,CAACa,CAAC,CAAC;MAClB;MAEA,IAAIkI,MAAM,GAAGhL,MAAM,CAACgB,IAAI,CAAChB,MAAM,CAACgB,IAAI,CAACuC,MAAM,GAAG,CAAC,CAAC,IAAIvD,MAAM;MAC1D,IAAIA,MAAM,CAACE,GAAG,CAAC8B,KAAK,IAAIZ,GAAG,CAACa,EAAE,KAAK+I,MAAM,CAAC/I,EAAE,EAAE;QAC5C;QACAL,MAAM,CAACgB,IAAI,CAACxB,GAAG,CAACa,EAAE,CAAC,CAACkD,OAAO,CAAC,UAAU+G,CAAC,EAAE;UACvC,IAAIS,CAAC,GAAGvL,GAAG,CAACa,EAAE,CAACiK,CAAC,CAAC;UACjBxI,QAAQ,CAAC1D,MAAM,EAAE,kBAAkB,EAAE;YAAEuL,MAAM,EAAEW,CAAC;YAAEH,GAAG,EAAEY;UAAE,CAAC,CAAC;QAC7D,CAAC,CAAC;MACJ;IACF;IACA,IAAIJ,CAAC,KAAK,CAAC,EAAEvM,MAAM,CAACkB,UAAU,GAAG,IAAI;IACrClB,MAAM,CAAC+K,OAAO,GAAG/K,MAAM,CAAC2L,WAAW,GAAG3L,MAAM,CAAC0L,UAAU,GAAG,EAAE;IAC5D1L,MAAM,CAAC+B,UAAU,CAACwB,MAAM,GAAG,CAAC;IAC5BvD,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;EACvB;EAEA,SAAS8E,WAAWA,CAAE5M,MAAM,EAAE;IAC5B,IAAI6M,MAAM,GAAG7M,MAAM,CAAC6M,MAAM;IAC1B,IAAIC,QAAQ,GAAGD,MAAM,CAACR,WAAW,CAAC,CAAC;IACnC,IAAIU,GAAG;IACP,IAAIC,MAAM,GAAG,EAAE;IAEf,IAAIhN,MAAM,CAAC2B,QAAQ,CAACkL,MAAM,CAAC,EAAE;MAC3B,OAAO7M,MAAM,CAAC2B,QAAQ,CAACkL,MAAM,CAAC;IAChC;IACA,IAAI7M,MAAM,CAAC2B,QAAQ,CAACmL,QAAQ,CAAC,EAAE;MAC7B,OAAO9M,MAAM,CAAC2B,QAAQ,CAACmL,QAAQ,CAAC;IAClC;IACAD,MAAM,GAAGC,QAAQ;IACjB,IAAID,MAAM,CAACI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5B,IAAIJ,MAAM,CAACI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5BJ,MAAM,GAAGA,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC;QACxBH,GAAG,GAAGI,QAAQ,CAACN,MAAM,EAAE,EAAE,CAAC;QAC1BG,MAAM,GAAGD,GAAG,CAAC5G,QAAQ,CAAC,EAAE,CAAC;MAC3B,CAAC,MAAM;QACL0G,MAAM,GAAGA,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC;QACxBH,GAAG,GAAGI,QAAQ,CAACN,MAAM,EAAE,EAAE,CAAC;QAC1BG,MAAM,GAAGD,GAAG,CAAC5G,QAAQ,CAAC,EAAE,CAAC;MAC3B;IACF;IACA0G,MAAM,GAAGA,MAAM,CAACnC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAClC,IAAI0C,KAAK,CAACL,GAAG,CAAC,IAAIC,MAAM,CAACX,WAAW,CAAC,CAAC,KAAKQ,MAAM,EAAE;MACjDjC,UAAU,CAAC5K,MAAM,EAAE,0BAA0B,CAAC;MAC9C,OAAO,GAAG,GAAGA,MAAM,CAAC6M,MAAM,GAAG,GAAG;IAClC;IAEA,OAAO5C,MAAM,CAACoD,aAAa,CAACN,GAAG,CAAC;EAClC;EAEA,SAASO,eAAeA,CAAEtN,MAAM,EAAEW,CAAC,EAAE;IACnC,IAAIA,CAAC,KAAK,GAAG,EAAE;MACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACwG,SAAS;MAC1BhI,MAAM,CAACuN,gBAAgB,GAAGvN,MAAM,CAACoC,QAAQ;IAC3C,CAAC,MAAM,IAAI,CAACiF,YAAY,CAAC1G,CAAC,CAAC,EAAE;MAC3B;MACA;MACAiK,UAAU,CAAC5K,MAAM,EAAE,kCAAkC,CAAC;MACtDA,MAAM,CAACqK,QAAQ,GAAG1J,CAAC;MACnBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;IACvB;EACF;EAEA,SAASmF,MAAMA,CAAE7G,KAAK,EAAEtD,CAAC,EAAE;IACzB,IAAI0K,MAAM,GAAG,EAAE;IACf,IAAI1K,CAAC,GAAGsD,KAAK,CAAC7C,MAAM,EAAE;MACpBiK,MAAM,GAAGpH,KAAK,CAAC6G,MAAM,CAACnK,CAAC,CAAC;IAC1B;IACA,OAAO0K,MAAM;EACf;EAEA,SAASxJ,KAAKA,CAAEoC,KAAK,EAAE;IACrB,IAAIpG,MAAM,GAAG,IAAI;IACjB,IAAI,IAAI,CAACqB,KAAK,EAAE;MACd,MAAM,IAAI,CAACA,KAAK;IAClB;IACA,IAAIrB,MAAM,CAACiB,MAAM,EAAE;MACjB,OAAOI,KAAK,CAACrB,MAAM,EACjB,sDAAsD,CAAC;IAC3D;IACA,IAAIoG,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOrC,GAAG,CAAC/D,MAAM,CAAC;IACpB;IACA,IAAI,OAAOoG,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGA,KAAK,CAACD,QAAQ,CAAC,CAAC;IAC1B;IACA,IAAIrD,CAAC,GAAG,CAAC;IACT,IAAInC,CAAC,GAAG,EAAE;IACV,OAAO,IAAI,EAAE;MACXA,CAAC,GAAGsM,MAAM,CAAC7G,KAAK,EAAEtD,CAAC,EAAE,CAAC;MACtB9C,MAAM,CAACW,CAAC,GAAGA,CAAC;MAEZ,IAAI,CAACA,CAAC,EAAE;QACN;MACF;MAEA,IAAIX,MAAM,CAACmC,aAAa,EAAE;QACxBnC,MAAM,CAACoC,QAAQ,EAAE;QACjB,IAAIzB,CAAC,KAAK,IAAI,EAAE;UACdX,MAAM,CAACqC,IAAI,EAAE;UACbrC,MAAM,CAACsC,MAAM,GAAG,CAAC;QACnB,CAAC,MAAM;UACLtC,MAAM,CAACsC,MAAM,EAAE;QACjB;MACF;MAEA,QAAQtC,MAAM,CAACuB,KAAK;QAClB,KAAKC,CAAC,CAACC,KAAK;UACVzB,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqG,gBAAgB;UACjC,IAAIlH,CAAC,KAAK,QAAQ,EAAE;YAClB;UACF;UACA2M,eAAe,CAACtN,MAAM,EAAEW,CAAC,CAAC;UAC1B;QAEF,KAAKa,CAAC,CAACqG,gBAAgB;UACrByF,eAAe,CAACtN,MAAM,EAAEW,CAAC,CAAC;UAC1B;QAEF,KAAKa,CAAC,CAACsG,IAAI;UACT,IAAI9H,MAAM,CAACmB,OAAO,IAAI,CAACnB,MAAM,CAACkB,UAAU,EAAE;YACxC,IAAIuM,MAAM,GAAG3K,CAAC,GAAG,CAAC;YAClB,OAAOnC,CAAC,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;cAClCA,CAAC,GAAGsM,MAAM,CAAC7G,KAAK,EAAEtD,CAAC,EAAE,CAAC;cACtB,IAAInC,CAAC,IAAIX,MAAM,CAACmC,aAAa,EAAE;gBAC7BnC,MAAM,CAACoC,QAAQ,EAAE;gBACjB,IAAIzB,CAAC,KAAK,IAAI,EAAE;kBACdX,MAAM,CAACqC,IAAI,EAAE;kBACbrC,MAAM,CAACsC,MAAM,GAAG,CAAC;gBACnB,CAAC,MAAM;kBACLtC,MAAM,CAACsC,MAAM,EAAE;gBACjB;cACF;YACF;YACAtC,MAAM,CAACqK,QAAQ,IAAIjE,KAAK,CAACsH,SAAS,CAACD,MAAM,EAAE3K,CAAC,GAAG,CAAC,CAAC;UACnD;UACA,IAAInC,CAAC,KAAK,GAAG,IAAI,EAAEX,MAAM,CAACmB,OAAO,IAAInB,MAAM,CAACkB,UAAU,IAAI,CAAClB,MAAM,CAACC,MAAM,CAAC,EAAE;YACzED,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACwG,SAAS;YAC1BhI,MAAM,CAACuN,gBAAgB,GAAGvN,MAAM,CAACoC,QAAQ;UAC3C,CAAC,MAAM;YACL,IAAI,CAACiF,YAAY,CAAC1G,CAAC,CAAC,KAAK,CAACX,MAAM,CAACmB,OAAO,IAAInB,MAAM,CAACkB,UAAU,CAAC,EAAE;cAC9D0J,UAAU,CAAC5K,MAAM,EAAE,iCAAiC,CAAC;YACvD;YACA,IAAIW,CAAC,KAAK,GAAG,EAAE;cACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACuG,WAAW;YAC9B,CAAC,MAAM;cACL/H,MAAM,CAACqK,QAAQ,IAAI1J,CAAC;YACtB;UACF;UACA;QAEF,KAAKa,CAAC,CAACoI,MAAM;UACX;UACA,IAAIjJ,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqI,aAAa;UAChC,CAAC,MAAM;YACL7J,MAAM,CAAC4D,MAAM,IAAIjD,CAAC;UACpB;UACA;QAEF,KAAKa,CAAC,CAACqI,aAAa;UAClB,IAAIlJ,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACkI,SAAS;UAC5B,CAAC,MAAM;YACL1J,MAAM,CAAC4D,MAAM,IAAI,GAAG,GAAGjD,CAAC;YACxBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoI,MAAM;UACzB;UACA;QAEF,KAAKpI,CAAC,CAACwG,SAAS;UACd;UACA,IAAIrH,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyG,SAAS;YAC1BjI,MAAM,CAAC2N,QAAQ,GAAG,EAAE;UACtB,CAAC,MAAM,IAAItG,YAAY,CAAC1G,CAAC,CAAC,EAAE;YAC1B;UAAA,CACD,MAAM,IAAI6G,OAAO,CAACP,SAAS,EAAEtG,CAAC,CAAC,EAAE;YAChCX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACuH,QAAQ;YACzB/I,MAAM,CAAC+K,OAAO,GAAGpK,CAAC;UACpB,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;YACpBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACkI,SAAS;YAC1B1J,MAAM,CAAC+K,OAAO,GAAG,EAAE;UACrB,CAAC,MAAM,IAAIpK,CAAC,KAAK,GAAG,EAAE;YACpBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoH,SAAS;YAC1B5I,MAAM,CAAC4N,YAAY,GAAG5N,MAAM,CAAC6N,YAAY,GAAG,EAAE;UAChD,CAAC,MAAM;YACLjD,UAAU,CAAC5K,MAAM,EAAE,aAAa,CAAC;YACjC;YACA,IAAIA,MAAM,CAACuN,gBAAgB,GAAG,CAAC,GAAGvN,MAAM,CAACoC,QAAQ,EAAE;cACjD,IAAI0L,GAAG,GAAG9N,MAAM,CAACoC,QAAQ,GAAGpC,MAAM,CAACuN,gBAAgB;cACnD5M,CAAC,GAAG,IAAI8F,KAAK,CAACqH,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGpN,CAAC;YAClC;YACAX,MAAM,CAACqK,QAAQ,IAAI,GAAG,GAAG1J,CAAC;YAC1BX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;UACvB;UACA;QAEF,KAAKtG,CAAC,CAACyG,SAAS;UACd,IAAI,CAACjI,MAAM,CAAC2N,QAAQ,GAAGhN,CAAC,EAAEqN,WAAW,CAAC,CAAC,KAAKpH,KAAK,EAAE;YACjDlD,QAAQ,CAAC1D,MAAM,EAAE,aAAa,CAAC;YAC/BA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoF,KAAK;YACtB5G,MAAM,CAAC2N,QAAQ,GAAG,EAAE;YACpB3N,MAAM,CAAC2D,KAAK,GAAG,EAAE;UACnB,CAAC,MAAM,IAAI3D,MAAM,CAAC2N,QAAQ,GAAGhN,CAAC,KAAK,IAAI,EAAE;YACvCX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC+G,OAAO;YACxBvI,MAAM,CAACiO,OAAO,GAAG,EAAE;YACnBjO,MAAM,CAAC2N,QAAQ,GAAG,EAAE;UACtB,CAAC,MAAM,IAAI,CAAC3N,MAAM,CAAC2N,QAAQ,GAAGhN,CAAC,EAAEqN,WAAW,CAAC,CAAC,KAAKnH,OAAO,EAAE;YAC1D7G,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqF,OAAO;YACxB,IAAI7G,MAAM,CAACkO,OAAO,IAAIlO,MAAM,CAACmB,OAAO,EAAE;cACpCyJ,UAAU,CAAC5K,MAAM,EACf,6CAA6C,CAAC;YAClD;YACAA,MAAM,CAACkO,OAAO,GAAG,EAAE;YACnBlO,MAAM,CAAC2N,QAAQ,GAAG,EAAE;UACtB,CAAC,MAAM,IAAIhN,CAAC,KAAK,GAAG,EAAE;YACpB+C,QAAQ,CAAC1D,MAAM,EAAE,mBAAmB,EAAEA,MAAM,CAAC2N,QAAQ,CAAC;YACtD3N,MAAM,CAAC2N,QAAQ,GAAG,EAAE;YACpB3N,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;UACvB,CAAC,MAAM,IAAIR,OAAO,CAAC3G,CAAC,CAAC,EAAE;YACrBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC0G,gBAAgB;YACjClI,MAAM,CAAC2N,QAAQ,IAAIhN,CAAC;UACtB,CAAC,MAAM;YACLX,MAAM,CAAC2N,QAAQ,IAAIhN,CAAC;UACtB;UACA;QAEF,KAAKa,CAAC,CAAC0G,gBAAgB;UACrB,IAAIvH,CAAC,KAAKX,MAAM,CAACU,CAAC,EAAE;YAClBV,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyG,SAAS;YAC1BjI,MAAM,CAACU,CAAC,GAAG,EAAE;UACf;UACAV,MAAM,CAAC2N,QAAQ,IAAIhN,CAAC;UACpB;QAEF,KAAKa,CAAC,CAACqF,OAAO;UACZ,IAAIlG,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;YACrBpE,QAAQ,CAAC1D,MAAM,EAAE,WAAW,EAAEA,MAAM,CAACkO,OAAO,CAAC;YAC7ClO,MAAM,CAACkO,OAAO,GAAG,IAAI,EAAC;UACxB,CAAC,MAAM;YACLlO,MAAM,CAACkO,OAAO,IAAIvN,CAAC;YACnB,IAAIA,CAAC,KAAK,GAAG,EAAE;cACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC4G,WAAW;YAC9B,CAAC,MAAM,IAAId,OAAO,CAAC3G,CAAC,CAAC,EAAE;cACrBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC2G,cAAc;cAC/BnI,MAAM,CAACU,CAAC,GAAGC,CAAC;YACd;UACF;UACA;QAEF,KAAKa,CAAC,CAAC2G,cAAc;UACnBnI,MAAM,CAACkO,OAAO,IAAIvN,CAAC;UACnB,IAAIA,CAAC,KAAKX,MAAM,CAACU,CAAC,EAAE;YAClBV,MAAM,CAACU,CAAC,GAAG,EAAE;YACbV,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqF,OAAO;UAC1B;UACA;QAEF,KAAKrF,CAAC,CAAC4G,WAAW;UAChBpI,MAAM,CAACkO,OAAO,IAAIvN,CAAC;UACnB,IAAIA,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqF,OAAO;UAC1B,CAAC,MAAM,IAAIS,OAAO,CAAC3G,CAAC,CAAC,EAAE;YACrBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC6G,kBAAkB;YACnCrI,MAAM,CAACU,CAAC,GAAGC,CAAC;UACd;UACA;QAEF,KAAKa,CAAC,CAAC6G,kBAAkB;UACvBrI,MAAM,CAACkO,OAAO,IAAIvN,CAAC;UACnB,IAAIA,CAAC,KAAKX,MAAM,CAACU,CAAC,EAAE;YAClBV,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC4G,WAAW;YAC5BpI,MAAM,CAACU,CAAC,GAAG,EAAE;UACf;UACA;QAEF,KAAKc,CAAC,CAAC+G,OAAO;UACZ,IAAI5H,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACgH,cAAc;UACjC,CAAC,MAAM;YACLxI,MAAM,CAACiO,OAAO,IAAItN,CAAC;UACrB;UACA;QAEF,KAAKa,CAAC,CAACgH,cAAc;UACnB,IAAI7H,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACiH,aAAa;YAC9BzI,MAAM,CAACiO,OAAO,GAAG3D,QAAQ,CAACtK,MAAM,CAACE,GAAG,EAAEF,MAAM,CAACiO,OAAO,CAAC;YACrD,IAAIjO,MAAM,CAACiO,OAAO,EAAE;cAClBvK,QAAQ,CAAC1D,MAAM,EAAE,WAAW,EAAEA,MAAM,CAACiO,OAAO,CAAC;YAC/C;YACAjO,MAAM,CAACiO,OAAO,GAAG,EAAE;UACrB,CAAC,MAAM;YACLjO,MAAM,CAACiO,OAAO,IAAI,GAAG,GAAGtN,CAAC;YACzBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC+G,OAAO;UAC1B;UACA;QAEF,KAAK/G,CAAC,CAACiH,aAAa;UAClB,IAAI9H,CAAC,KAAK,GAAG,EAAE;YACbiK,UAAU,CAAC5K,MAAM,EAAE,mBAAmB,CAAC;YACvC;YACA;YACAA,MAAM,CAACiO,OAAO,IAAI,IAAI,GAAGtN,CAAC;YAC1BX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC+G,OAAO;UAC1B,CAAC,MAAM;YACLvI,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;UACvB;UACA;QAEF,KAAKtG,CAAC,CAACoF,KAAK;UACV,IAAIjG,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACkH,YAAY;UAC/B,CAAC,MAAM;YACL1I,MAAM,CAAC2D,KAAK,IAAIhD,CAAC;UACnB;UACA;QAEF,KAAKa,CAAC,CAACkH,YAAY;UACjB,IAAI/H,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACmH,cAAc;UACjC,CAAC,MAAM;YACL3I,MAAM,CAAC2D,KAAK,IAAI,GAAG,GAAGhD,CAAC;YACvBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoF,KAAK;UACxB;UACA;QAEF,KAAKpF,CAAC,CAACmH,cAAc;UACnB,IAAIhI,CAAC,KAAK,GAAG,EAAE;YACb,IAAIX,MAAM,CAAC2D,KAAK,EAAE;cAChBD,QAAQ,CAAC1D,MAAM,EAAE,SAAS,EAAEA,MAAM,CAAC2D,KAAK,CAAC;YAC3C;YACAD,QAAQ,CAAC1D,MAAM,EAAE,cAAc,CAAC;YAChCA,MAAM,CAAC2D,KAAK,GAAG,EAAE;YACjB3D,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;UACvB,CAAC,MAAM,IAAInH,CAAC,KAAK,GAAG,EAAE;YACpBX,MAAM,CAAC2D,KAAK,IAAI,GAAG;UACrB,CAAC,MAAM;YACL3D,MAAM,CAAC2D,KAAK,IAAI,IAAI,GAAGhD,CAAC;YACxBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoF,KAAK;UACxB;UACA;QAEF,KAAKpF,CAAC,CAACoH,SAAS;UACd,IAAIjI,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsH,gBAAgB;UACnC,CAAC,MAAM,IAAIzB,YAAY,CAAC1G,CAAC,CAAC,EAAE;YAC1BX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqH,cAAc;UACjC,CAAC,MAAM;YACL7I,MAAM,CAAC4N,YAAY,IAAIjN,CAAC;UAC1B;UACA;QAEF,KAAKa,CAAC,CAACqH,cAAc;UACnB,IAAI,CAAC7I,MAAM,CAAC6N,YAAY,IAAIxG,YAAY,CAAC1G,CAAC,CAAC,EAAE;YAC3C;UACF,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;YACpBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsH,gBAAgB;UACnC,CAAC,MAAM;YACL9I,MAAM,CAAC6N,YAAY,IAAIlN,CAAC;UAC1B;UACA;QAEF,KAAKa,CAAC,CAACsH,gBAAgB;UACrB,IAAInI,CAAC,KAAK,GAAG,EAAE;YACb+C,QAAQ,CAAC1D,MAAM,EAAE,yBAAyB,EAAE;cAC1CiL,IAAI,EAAEjL,MAAM,CAAC4N,YAAY;cACzBO,IAAI,EAAEnO,MAAM,CAAC6N;YACf,CAAC,CAAC;YACF7N,MAAM,CAAC4N,YAAY,GAAG5N,MAAM,CAAC6N,YAAY,GAAG,EAAE;YAC9C7N,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACsG,IAAI;UACvB,CAAC,MAAM;YACL9H,MAAM,CAAC6N,YAAY,IAAI,GAAG,GAAGlN,CAAC;YAC9BX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACqH,cAAc;UACjC;UACA;QAEF,KAAKrH,CAAC,CAACuH,QAAQ;UACb,IAAIvB,OAAO,CAACN,QAAQ,EAAEvG,CAAC,CAAC,EAAE;YACxBX,MAAM,CAAC+K,OAAO,IAAIpK,CAAC;UACrB,CAAC,MAAM;YACLmK,MAAM,CAAC9K,MAAM,CAAC;YACd,IAAIW,CAAC,KAAK,GAAG,EAAE;cACbkL,OAAO,CAAC7L,MAAM,CAAC;YACjB,CAAC,MAAM,IAAIW,CAAC,KAAK,GAAG,EAAE;cACpBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACwH,cAAc;YACjC,CAAC,MAAM;cACL,IAAI,CAAC3B,YAAY,CAAC1G,CAAC,CAAC,EAAE;gBACpBiK,UAAU,CAAC5K,MAAM,EAAE,+BAA+B,CAAC;cACrD;cACAA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyH,MAAM;YACzB;UACF;UACA;QAEF,KAAKzH,CAAC,CAACwH,cAAc;UACnB,IAAIrI,CAAC,KAAK,GAAG,EAAE;YACbkL,OAAO,CAAC7L,MAAM,EAAE,IAAI,CAAC;YACrBsM,QAAQ,CAACtM,MAAM,CAAC;UAClB,CAAC,MAAM;YACL4K,UAAU,CAAC5K,MAAM,EAAE,gDAAgD,CAAC;YACpEA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyH,MAAM;UACzB;UACA;QAEF,KAAKzH,CAAC,CAACyH,MAAM;UACX;UACA,IAAI5B,YAAY,CAAC1G,CAAC,CAAC,EAAE;YACnB;UACF,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;YACpBkL,OAAO,CAAC7L,MAAM,CAAC;UACjB,CAAC,MAAM,IAAIW,CAAC,KAAK,GAAG,EAAE;YACpBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACwH,cAAc;UACjC,CAAC,MAAM,IAAIxB,OAAO,CAACP,SAAS,EAAEtG,CAAC,CAAC,EAAE;YAChCX,MAAM,CAAC0L,UAAU,GAAG/K,CAAC;YACrBX,MAAM,CAAC2L,WAAW,GAAG,EAAE;YACvB3L,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC0H,WAAW;UAC9B,CAAC,MAAM;YACL0B,UAAU,CAAC5K,MAAM,EAAE,wBAAwB,CAAC;UAC9C;UACA;QAEF,KAAKwB,CAAC,CAAC0H,WAAW;UAChB,IAAIvI,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC4H,YAAY;UAC/B,CAAC,MAAM,IAAIzI,CAAC,KAAK,GAAG,EAAE;YACpBiK,UAAU,CAAC5K,MAAM,EAAE,yBAAyB,CAAC;YAC7CA,MAAM,CAAC2L,WAAW,GAAG3L,MAAM,CAAC0L,UAAU;YACtCD,MAAM,CAACzL,MAAM,CAAC;YACd6L,OAAO,CAAC7L,MAAM,CAAC;UACjB,CAAC,MAAM,IAAIqH,YAAY,CAAC1G,CAAC,CAAC,EAAE;YAC1BX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC2H,qBAAqB;UACxC,CAAC,MAAM,IAAI3B,OAAO,CAACN,QAAQ,EAAEvG,CAAC,CAAC,EAAE;YAC/BX,MAAM,CAAC0L,UAAU,IAAI/K,CAAC;UACxB,CAAC,MAAM;YACLiK,UAAU,CAAC5K,MAAM,EAAE,wBAAwB,CAAC;UAC9C;UACA;QAEF,KAAKwB,CAAC,CAAC2H,qBAAqB;UAC1B,IAAIxI,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC4H,YAAY;UAC/B,CAAC,MAAM,IAAI/B,YAAY,CAAC1G,CAAC,CAAC,EAAE;YAC1B;UACF,CAAC,MAAM;YACLiK,UAAU,CAAC5K,MAAM,EAAE,yBAAyB,CAAC;YAC7CA,MAAM,CAACoB,GAAG,CAAC8J,UAAU,CAAClL,MAAM,CAAC0L,UAAU,CAAC,GAAG,EAAE;YAC7C1L,MAAM,CAAC2L,WAAW,GAAG,EAAE;YACvBjI,QAAQ,CAAC1D,MAAM,EAAE,aAAa,EAAE;cAC9BiL,IAAI,EAAEjL,MAAM,CAAC0L,UAAU;cACvB7F,KAAK,EAAE;YACT,CAAC,CAAC;YACF7F,MAAM,CAAC0L,UAAU,GAAG,EAAE;YACtB,IAAI/K,CAAC,KAAK,GAAG,EAAE;cACbkL,OAAO,CAAC7L,MAAM,CAAC;YACjB,CAAC,MAAM,IAAIwH,OAAO,CAACP,SAAS,EAAEtG,CAAC,CAAC,EAAE;cAChCX,MAAM,CAAC0L,UAAU,GAAG/K,CAAC;cACrBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC0H,WAAW;YAC9B,CAAC,MAAM;cACL0B,UAAU,CAAC5K,MAAM,EAAE,wBAAwB,CAAC;cAC5CA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyH,MAAM;YACzB;UACF;UACA;QAEF,KAAKzH,CAAC,CAAC4H,YAAY;UACjB,IAAI/B,YAAY,CAAC1G,CAAC,CAAC,EAAE;YACnB;UACF,CAAC,MAAM,IAAI2G,OAAO,CAAC3G,CAAC,CAAC,EAAE;YACrBX,MAAM,CAACU,CAAC,GAAGC,CAAC;YACZX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC6H,mBAAmB;UACtC,CAAC,MAAM;YACLuB,UAAU,CAAC5K,MAAM,EAAE,0BAA0B,CAAC;YAC9CA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC+H,qBAAqB;YACtCvJ,MAAM,CAAC2L,WAAW,GAAGhL,CAAC;UACxB;UACA;QAEF,KAAKa,CAAC,CAAC6H,mBAAmB;UACxB,IAAI1I,CAAC,KAAKX,MAAM,CAACU,CAAC,EAAE;YAClB,IAAIC,CAAC,KAAK,GAAG,EAAE;cACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACgI,qBAAqB;YACxC,CAAC,MAAM;cACLxJ,MAAM,CAAC2L,WAAW,IAAIhL,CAAC;YACzB;YACA;UACF;UACA8K,MAAM,CAACzL,MAAM,CAAC;UACdA,MAAM,CAACU,CAAC,GAAG,EAAE;UACbV,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC8H,mBAAmB;UACpC;QAEF,KAAK9H,CAAC,CAAC8H,mBAAmB;UACxB,IAAIjC,YAAY,CAAC1G,CAAC,CAAC,EAAE;YACnBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyH,MAAM;UACzB,CAAC,MAAM,IAAItI,CAAC,KAAK,GAAG,EAAE;YACpBkL,OAAO,CAAC7L,MAAM,CAAC;UACjB,CAAC,MAAM,IAAIW,CAAC,KAAK,GAAG,EAAE;YACpBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACwH,cAAc;UACjC,CAAC,MAAM,IAAIxB,OAAO,CAACP,SAAS,EAAEtG,CAAC,CAAC,EAAE;YAChCiK,UAAU,CAAC5K,MAAM,EAAE,kCAAkC,CAAC;YACtDA,MAAM,CAAC0L,UAAU,GAAG/K,CAAC;YACrBX,MAAM,CAAC2L,WAAW,GAAG,EAAE;YACvB3L,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAAC0H,WAAW;UAC9B,CAAC,MAAM;YACL0B,UAAU,CAAC5K,MAAM,EAAE,wBAAwB,CAAC;UAC9C;UACA;QAEF,KAAKwB,CAAC,CAAC+H,qBAAqB;UAC1B,IAAI,CAAChC,WAAW,CAAC5G,CAAC,CAAC,EAAE;YACnB,IAAIA,CAAC,KAAK,GAAG,EAAE;cACbX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACiI,qBAAqB;YACxC,CAAC,MAAM;cACLzJ,MAAM,CAAC2L,WAAW,IAAIhL,CAAC;YACzB;YACA;UACF;UACA8K,MAAM,CAACzL,MAAM,CAAC;UACd,IAAIW,CAAC,KAAK,GAAG,EAAE;YACbkL,OAAO,CAAC7L,MAAM,CAAC;UACjB,CAAC,MAAM;YACLA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACyH,MAAM;UACzB;UACA;QAEF,KAAKzH,CAAC,CAACkI,SAAS;UACd,IAAI,CAAC1J,MAAM,CAAC+K,OAAO,EAAE;YACnB,IAAI1D,YAAY,CAAC1G,CAAC,CAAC,EAAE;cACnB;YACF,CAAC,MAAM,IAAIgH,QAAQ,CAACV,SAAS,EAAEtG,CAAC,CAAC,EAAE;cACjC,IAAIX,MAAM,CAAC4D,MAAM,EAAE;gBACjB5D,MAAM,CAAC4D,MAAM,IAAI,IAAI,GAAGjD,CAAC;gBACzBX,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoI,MAAM;cACzB,CAAC,MAAM;gBACLgB,UAAU,CAAC5K,MAAM,EAAE,iCAAiC,CAAC;cACvD;YACF,CAAC,MAAM;cACLA,MAAM,CAAC+K,OAAO,GAAGpK,CAAC;YACpB;UACF,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;YACpB2L,QAAQ,CAACtM,MAAM,CAAC;UAClB,CAAC,MAAM,IAAIwH,OAAO,CAACN,QAAQ,EAAEvG,CAAC,CAAC,EAAE;YAC/BX,MAAM,CAAC+K,OAAO,IAAIpK,CAAC;UACrB,CAAC,MAAM,IAAIX,MAAM,CAAC4D,MAAM,EAAE;YACxB5D,MAAM,CAAC4D,MAAM,IAAI,IAAI,GAAG5D,MAAM,CAAC+K,OAAO;YACtC/K,MAAM,CAAC+K,OAAO,GAAG,EAAE;YACnB/K,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACoI,MAAM;UACzB,CAAC,MAAM;YACL,IAAI,CAACvC,YAAY,CAAC1G,CAAC,CAAC,EAAE;cACpBiK,UAAU,CAAC5K,MAAM,EAAE,gCAAgC,CAAC;YACtD;YACAA,MAAM,CAACuB,KAAK,GAAGC,CAAC,CAACmI,mBAAmB;UACtC;UACA;QAEF,KAAKnI,CAAC,CAACmI,mBAAmB;UACxB,IAAItC,YAAY,CAAC1G,CAAC,CAAC,EAAE;YACnB;UACF;UACA,IAAIA,CAAC,KAAK,GAAG,EAAE;YACb2L,QAAQ,CAACtM,MAAM,CAAC;UAClB,CAAC,MAAM;YACL4K,UAAU,CAAC5K,MAAM,EAAE,mCAAmC,CAAC;UACzD;UACA;QAEF,KAAKwB,CAAC,CAACuG,WAAW;QAClB,KAAKvG,CAAC,CAACgI,qBAAqB;QAC5B,KAAKhI,CAAC,CAACiI,qBAAqB;UAC1B,IAAI2E,WAAW;UACf,IAAIC,MAAM;UACV,QAAQrO,MAAM,CAACuB,KAAK;YAClB,KAAKC,CAAC,CAACuG,WAAW;cAChBqG,WAAW,GAAG5M,CAAC,CAACsG,IAAI;cACpBuG,MAAM,GAAG,UAAU;cACnB;YAEF,KAAK7M,CAAC,CAACgI,qBAAqB;cAC1B4E,WAAW,GAAG5M,CAAC,CAAC6H,mBAAmB;cACnCgF,MAAM,GAAG,aAAa;cACtB;YAEF,KAAK7M,CAAC,CAACiI,qBAAqB;cAC1B2E,WAAW,GAAG5M,CAAC,CAAC+H,qBAAqB;cACrC8E,MAAM,GAAG,aAAa;cACtB;UACJ;UAEA,IAAI1N,CAAC,KAAK,GAAG,EAAE;YACbX,MAAM,CAACqO,MAAM,CAAC,IAAIzB,WAAW,CAAC5M,MAAM,CAAC;YACrCA,MAAM,CAAC6M,MAAM,GAAG,EAAE;YAClB7M,MAAM,CAACuB,KAAK,GAAG6M,WAAW;UAC5B,CAAC,MAAM,IAAI5G,OAAO,CAACxH,MAAM,CAAC6M,MAAM,CAACtJ,MAAM,GAAG6D,UAAU,GAAGD,WAAW,EAAExG,CAAC,CAAC,EAAE;YACtEX,MAAM,CAAC6M,MAAM,IAAIlM,CAAC;UACpB,CAAC,MAAM;YACLiK,UAAU,CAAC5K,MAAM,EAAE,kCAAkC,CAAC;YACtDA,MAAM,CAACqO,MAAM,CAAC,IAAI,GAAG,GAAGrO,MAAM,CAAC6M,MAAM,GAAGlM,CAAC;YACzCX,MAAM,CAAC6M,MAAM,GAAG,EAAE;YAClB7M,MAAM,CAACuB,KAAK,GAAG6M,WAAW;UAC5B;UAEA;QAEF;UACE,MAAM,IAAIzD,KAAK,CAAC3K,MAAM,EAAE,iBAAiB,GAAGA,MAAM,CAACuB,KAAK,CAAC;MAC7D;IACF,CAAC,CAAC;;IAEF,IAAIvB,MAAM,CAACoC,QAAQ,IAAIpC,MAAM,CAACY,mBAAmB,EAAE;MACjDqC,iBAAiB,CAACjD,MAAM,CAAC;IAC3B;IACA,OAAOA,MAAM;EACf;;EAEA;EACA;EACA,IAAI,CAACiK,MAAM,CAACoD,aAAa,EAAE;IACxB,aAAY;MACX,IAAIiB,kBAAkB,GAAGrE,MAAM,CAACC,YAAY;MAC5C,IAAIqE,KAAK,GAAGpL,IAAI,CAACoL,KAAK;MACtB,IAAIlB,aAAa,GAAG,SAAAA,CAAA,EAAY;QAC9B,IAAImB,QAAQ,GAAG,MAAM;QACrB,IAAIC,SAAS,GAAG,EAAE;QAClB,IAAIC,aAAa;QACjB,IAAIC,YAAY;QAChB,IAAIC,KAAK,GAAG,CAAC,CAAC;QACd,IAAIrL,MAAM,GAAGiD,SAAS,CAACjD,MAAM;QAC7B,IAAI,CAACA,MAAM,EAAE;UACX,OAAO,EAAE;QACX;QACA,IAAIiK,MAAM,GAAG,EAAE;QACf,OAAO,EAAEoB,KAAK,GAAGrL,MAAM,EAAE;UACvB,IAAIsL,SAAS,GAAGC,MAAM,CAACtI,SAAS,CAACoI,KAAK,CAAC,CAAC;UACxC,IACE,CAACG,QAAQ,CAACF,SAAS,CAAC;UAAI;UACxBA,SAAS,GAAG,CAAC;UAAI;UACjBA,SAAS,GAAG,QAAQ;UAAI;UACxBN,KAAK,CAACM,SAAS,CAAC,KAAKA,SAAS,CAAC;UAAA,EAC/B;YACA,MAAMG,UAAU,CAAC,sBAAsB,GAAGH,SAAS,CAAC;UACtD;UACA,IAAIA,SAAS,IAAI,MAAM,EAAE;YAAE;YACzBJ,SAAS,CAACzL,IAAI,CAAC6L,SAAS,CAAC;UAC3B,CAAC,MAAM;YAAE;YACP;YACAA,SAAS,IAAI,OAAO;YACpBH,aAAa,GAAG,CAACG,SAAS,IAAI,EAAE,IAAI,MAAM;YAC1CF,YAAY,GAAIE,SAAS,GAAG,KAAK,GAAI,MAAM;YAC3CJ,SAAS,CAACzL,IAAI,CAAC0L,aAAa,EAAEC,YAAY,CAAC;UAC7C;UACA,IAAIC,KAAK,GAAG,CAAC,KAAKrL,MAAM,IAAIkL,SAAS,CAAClL,MAAM,GAAGiL,QAAQ,EAAE;YACvDhB,MAAM,IAAIc,kBAAkB,CAAC5J,KAAK,CAAC,IAAI,EAAE+J,SAAS,CAAC;YACnDA,SAAS,CAAClL,MAAM,GAAG,CAAC;UACtB;QACF;QACA,OAAOiK,MAAM;MACf,CAAC;MACD;MACA,IAAI5L,MAAM,CAACwD,cAAc,EAAE;QACzBxD,MAAM,CAACwD,cAAc,CAAC6E,MAAM,EAAE,eAAe,EAAE;UAC7CpE,KAAK,EAAEwH,aAAa;UACpB1H,YAAY,EAAE,IAAI;UAClBf,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLqF,MAAM,CAACoD,aAAa,GAAGA,aAAa;MACtC;IACF,CAAC,EAAC,CAAC;EACL;AACF,CAAC,EAAE,OAAO4B,OAAO,KAAK,WAAW,GAAG,IAAI,CAAClP,GAAG,GAAG,CAAC,CAAC,GAAGkP,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}