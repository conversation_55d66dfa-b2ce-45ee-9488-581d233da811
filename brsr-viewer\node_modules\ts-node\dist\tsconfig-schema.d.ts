import type { TsConfigOptions } from './index';
/**
 * tsconfig schema which includes "ts-node" options.
 * @allOf [{"$ref": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json"}]
 */
export interface TsConfigSchema {
    /**
     * ts-node options.  See also: https://typestrong.org/ts-node/docs/configuration
     *
     * ts-node offers TypeScript execution and REPL for node.js, with source map support.
     */
    'ts-node': TsConfigOptions;
}
