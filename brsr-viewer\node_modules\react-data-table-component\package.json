{"name": "react-data-table-component", "version": "7.7.0", "description": "A simple to use declarative react based data table", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "browser": "dist/index.cjs.js", "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["react", "data", "table", "tables", "react-table", "react-data-table", "react-data-table-component"], "repository": "https://github.com/jbetancur/react-data-table-component", "author": "jbetancur", "license": "Apache-2.0", "prepublish": "tsc", "scripts": {"prepublishOnly": "npm run build", "build:dev": "rollup -c rollup/rollup.config.dev.js -m", "build:umd": "rollup -c rollup/rollup.config.umd.js", "build:cjs": "rollup -c rollup/rollup.config.cjs.js", "build:es": "rollup -c rollup/rollup.config.es.js", "build": "rimraf dist && npm run build:dev && npm run build:cjs && npm run build:umd && npm run build:es", "start": "npm run build:dev -- -w", "test": "jest --passWithNoTests --verbose --coverage", "test:tdd": "jest --watch", "test:tdd-coverage": "jest --watch --coverage", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.tsx && eslint . --ext .js --config eslintrc-js.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "deploy-storybook": "gh-pages -d storybook-static"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/eslint-parser": "^7.26.8", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@faker-js/faker": "^8.4.1", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-node-resolve": "^15.3.1", "@storybook/addon-a11y": "^7.6.20", "@storybook/addon-essentials": "^7.6.20", "@storybook/addon-storysource": "^7.6.20", "@storybook/react": "^7.6.20", "@storybook/react-webpack5": "^7.6.20", "@storybook/theming": "^7.6.20", "@testing-library/react": "^14.3.1", "@types/babel__preset-env": "^7.10.0", "@types/jest": "^29.5.14", "@types/lodash-es": "^4.17.12", "@types/lodash.orderby": "^4.6.9", "@types/node": "^20.17.19", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "axios": "^1.7.9", "codecov": "^3.8.3", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.6.15", "gh-pages": "^6.3.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-styled-components": "^7.2.0", "jest-watch-typeahead": "^2.2.2", "lodash-es": "^4.17.21", "memoize-one": "^6.0.0", "moment": "^2.30.1", "postcss": "^8.5.3", "postcss-styled-syntax": "^0.6.4", "prettier": "^3.5.2", "react": "^18.3.1", "react-app-polyfill": "^3.0.0", "react-dom": "^18.3.1", "remark-gfm": "^3.0.1", "rimraf": "^5.0.10", "rollup": "^2.79.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.36.0", "rollup-plugin-visualizer": "^5.14.0", "storybook": "^7.6.20", "styled-components": "^6.1.15", "stylelint": "^16.14.1", "stylelint-config-standard": "^36.0.1", "stylelint-prettier": "^5.0.3", "ts-jest": "^29.2.6", "typescript": "^5.7.3"}, "dependencies": {"deepmerge": "^4.3.1"}, "peerDependencies": {"react": ">= 17.0.0", "styled-components": ">= 5.0.0"}, "peerDependenciesMeta": {"styled-components": {"optional": false}}, "packageManager": "yarn@4.6.0"}