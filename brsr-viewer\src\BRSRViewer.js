import React, { useState } from "react";
import * as XLSX from "xlsx";
import axios from "axios";
import { parseStringPromise } from "xml2js";
import DataTable from "react-data-table-component";

export default function BRSRViewer() {
  const [companyData, setCompanyData] = useState([]);
  const [searchCompany, setSearchCompany] = useState("");
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });
  const [searchBRSR, setSearchBRSR] = useState("");
  const [activeTab, setActiveTab] = useState("Environment");
  const [unifiedData, setUnifiedData] = useState([]);
  const [loadingAll, setLoadingAll] = useState(false);
  const [loadingSingle, setLoadingSingle] = useState(false);

  /** Read Excel **/
  const handleExcelUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (evt) => {
      const workbook = XLSX.read(evt.target.result, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const json = XLSX.utils.sheet_to_json(worksheet);
      setCompanyData(json);
    };
    reader.readAsBinaryString(file);
  };

  /** Categorize field names **/
  const categorizeField = (name) => {
    const lower = name.toLowerCase();
    if (lower.includes("energy") || lower.includes("emission") || lower.includes("water") || lower.includes("waste"))
      return "Environment";
    if (lower.includes("employee") || lower.includes("social") || lower.includes("community") || lower.includes("csr"))
      return "Social";
    return "Governance";
  };

  /** Parse XML into categories **/
  const parseBRSRXML = async (xmlText) => {
    const result = await parseStringPromise(xmlText, { explicitArray: false });
    const xbrl = result["xbrli:xbrl"];
    const contexts = {};
    const units = {};

    // Parse contexts
    const contextArr = Array.isArray(xbrl["xbrli:context"])
      ? xbrl["xbrli:context"]
      : [xbrl["xbrli:context"]];
    contextArr.forEach((ctx) => {
      contexts[ctx.$.id] = {
        entity: ctx["xbrli:entity"]["xbrli:identifier"]._,
        startDate: ctx["xbrli:period"]["xbrli:startDate"],
        endDate: ctx["xbrli:period"]["xbrli:endDate"],
      };
    });

    // Parse units
    if (xbrl["xbrli:unit"]) {
      const unitArr = Array.isArray(xbrl["xbrli:unit"]) ? xbrl["xbrli:unit"] : [xbrl["xbrli:unit"]];
      unitArr.forEach((u) => {
        units[u.$.id] = u["xbrli:measure"];
      });
    }

    // Group data into categories
    const categories = { Environment: [], Social: [], Governance: [] };

    Object.keys(xbrl).forEach((tag) => {
      if (tag.startsWith("in-capmkt:")) {
        const element = xbrl[tag];
        const elements = Array.isArray(element) ? element : [element];
        elements.forEach((el) => {
          const context = contexts[el.$.contextRef];
          const unit = el.$.unitRef ? units[el.$.unitRef] : null;
          const category = categorizeField(tag);

          categories[category].push({
            metric: tag.replace("in-capmkt:", ""),
            value: el._,
            unit,
            period: context ? `${context.startDate} → ${context.endDate}` : "",
            company: context?.entity,
          });
        });
      }
    });

    return categories;
  };

  /** View single company **/
  const handleCompanySelect = async (row) => {
    setSelectedCompany(row);
    setLoadingSingle(true);
    setBrsrData({ Environment: [], Social: [], Governance: [] });

    if (row["XML Link"]) {
      try {
        const res = await axios.get(row["XML Link"]);
        const categories = await parseBRSRXML(res.data);
        setBrsrData(categories);
      } catch (err) {
        console.error("Error loading XML:", err);
      } finally {
        setLoadingSingle(false);
      }
    }
  };

  /** Fetch all companies **/
  const fetchAllCompanies = async () => {
    setLoadingAll(true);
    const allData = [];
    for (const row of companyData) {
      if (row["XML Link"]) {
        try {
          const res = await axios.get(row["XML Link"]);
          const categories = await parseBRSRXML(res.data);
          ["Environment", "Social", "Governance"].forEach((cat) => {
            categories[cat].forEach((d) =>
              allData.push({
                ...d,
                category: cat,
                companyName: row["Company Name"],
                year: row["Year"],
              })
            );
          });
        } catch (err) {
          console.warn("Failed to fetch:", row["Company Name"]);
        }
      }
    }
    setUnifiedData(allData);
    setLoadingAll(false);
  };

  /** Filter data **/
  const filteredTabData = brsrData[activeTab].filter(
    (row) =>
      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||
      row.value.toLowerCase().includes(searchBRSR.toLowerCase())
  );

  return (
    <div className="container-fluid py-4" style={{backgroundColor: '#f8f9fa', minHeight: '100vh'}}>
      <h1 className="display-4 fw-bold text-center mb-5 text-dark">
        📊 BRSR Report Viewer (Excel + XML)
      </h1>

      {/* Upload Excel */}
      <div className="row justify-content-center mb-4">
        <div className="col-md-6">
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={handleExcelUpload}
            className="form-control shadow-sm"
          />
        </div>
      </div>

      {/* Search and Fetch All */}
      <div className="row justify-content-center mb-4">
        <div className="col-md-4">
          <input
            type="text"
            placeholder="Search company..."
            value={searchCompany}
            onChange={(e) => setSearchCompany(e.target.value)}
            className="form-control shadow-sm"
          />
        </div>
        <div className="col-md-2">
          <button
            onClick={fetchAllCompanies}
            disabled={loadingAll || companyData.length === 0}
            className={`btn w-100 ${
              loadingAll ? "btn-secondary" : "btn-success"
            }`}
          >
            {loadingAll ? "Fetching All..." : "Fetch All Companies"}
          </button>
        </div>
      </div>

      {/* Company Table */}
      <div className="card shadow mb-4">
        <div className="card-body">
          <DataTable
            title="Companies"
            columns={[
              { name: "Company", selector: (row) => row["Company Name"], sortable: true },
              { name: "Year", selector: (row) => row["Year"], sortable: true },
              {
                name: "Actions",
                cell: (row) => (
                  <button
                    onClick={() => handleCompanySelect(row)}
                    className="btn btn-primary btn-sm"
                  >
                    View BRSR
                  </button>
                ),
              },
            ]}
            data={companyData.filter((r) =>
              r["Company Name"]?.toLowerCase().includes(searchCompany.toLowerCase())
            )}
            pagination
            highlightOnHover
            dense
          />
        </div>
      </div>

      {/* Unified Data Table */}
      {unifiedData.length > 0 && (
        <div className="card shadow mb-4">
          <div className="card-body">
            <h2 className="card-title h4 mb-4">Unified BRSR Data</h2>
            <DataTable
              columns={[
                { name: "Company", selector: (row) => row.companyName, sortable: true },
                { name: "Year", selector: (row) => row.year, sortable: true },
                { name: "Category", selector: (row) => row.category, sortable: true },
                { name: "Metric", selector: (row) => row.metric, sortable: true },
                { name: "Value", selector: (row) => row.value, sortable: true },
              ]}
              data={unifiedData}
              pagination
              dense
              highlightOnHover
            />
          </div>
        </div>
      )}

      {/* Tabs for single company */}
      {selectedCompany && (
        <div className="card shadow">
          <div className="card-body">
            <h2 className="card-title h4 mb-4">
              {selectedCompany["Company Name"]} ({selectedCompany["Year"]})
            </h2>

            {/* Tabs */}
            <ul className="nav nav-tabs mb-4">
              {["Environment", "Social", "Governance"].map((tab) => (
                <li className="nav-item" key={tab}>
                  <button
                    onClick={() => setActiveTab(tab)}
                    className={`nav-link ${
                      activeTab === tab ? "active" : ""
                    }`}
                    style={{
                      backgroundColor: activeTab === tab
                        ? tab === "Environment"
                          ? "#198754"
                          : tab === "Social"
                          ? "#6f42c1"
                          : "#fd7e14"
                        : "",
                      color: activeTab === tab ? "white" : "",
                      border: "none"
                    }}
                  >
                    {tab}
                  </button>
                </li>
              ))}
            </ul>

            {/* Search */}
            <div className="row justify-content-center mb-4">
              <div className="col-md-6">
                <input
                  type="text"
                  placeholder={`Search in ${activeTab}...`}
                  value={searchBRSR}
                  onChange={(e) => setSearchBRSR(e.target.value)}
                  className="form-control shadow-sm"
                />
              </div>
            </div>

            {/* Table */}
            {loadingSingle ? (
              <div className="d-flex justify-content-center py-5">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            ) : (
              <DataTable
                columns={[
                  { name: "Metric", selector: (row) => row.metric, sortable: true },
                  { name: "Value", selector: (row) => row.value, sortable: true },
                  { name: "Unit", selector: (row) => row.unit || "-", sortable: true },
                  { name: "Period", selector: (row) => row.period, sortable: true },
                ]}
                data={filteredTabData}
                pagination
                dense
                highlightOnHover
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}
