import React, { useState } from "react";
import * as XLSX from "xlsx";
import axios from "axios";
import DataTable from "react-data-table-component";

export default function BRSRViewer() {
  const [companyData, setCompanyData] = useState([]);
  const [searchCompany, setSearchCompany] = useState("");
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });
  const [searchBRSR, setSearchBRSR] = useState("");
  const [activeTab, setActiveTab] = useState("Environment");
  const [unifiedData, setUnifiedData] = useState([]);
  const [loadingAll, setLoadingAll] = useState(false);
  const [loadingSingle, setLoadingSingle] = useState(false);

  /** Read Excel **/
  const handleExcelUpload = (e) => {
    console.log("File upload triggered");
    const file = e.target.files[0];
    if (!file) {
      console.log("No file selected");
      return;
    }

    console.log("File selected:", file.name, file.type, file.size);

    const reader = new FileReader();
    reader.onload = (evt) => {
      try {
        console.log("File read successfully, parsing...");
        let json;

        if (file.name.toLowerCase().endsWith('.csv')) {
          // Handle CSV files with proper parsing
          console.log("Processing CSV file");
          const csvText = evt.target.result;

          // Better CSV parsing function
          const parseCSVLine = (line) => {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
              const char = line[i];

              if (char === '"') {
                inQuotes = !inQuotes;
              } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
              } else {
                current += char;
              }
            }
            result.push(current.trim());
            return result;
          };

          const lines = csvText.split('\n').filter(line => line.trim());
          const headers = parseCSVLine(lines[0]).map(h => h.replace(/"/g, '').trim());
          console.log("CSV headers:", headers);

          json = [];
          for (let i = 1; i < lines.length; i++) {
            const values = parseCSVLine(lines[i]).map(v => v.replace(/"/g, '').trim());
            const row = {};
            headers.forEach((header, index) => {
              row[header] = values[index] || '';
            });
            if (Object.values(row).some(val => val)) { // Only add non-empty rows
              json.push(row);
            }
          }
        } else {
          // Handle Excel files
          console.log("Processing Excel file");
          const workbook = XLSX.read(evt.target.result, { type: "binary" });
          console.log("Workbook created, sheet names:", workbook.SheetNames);

          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          json = XLSX.utils.sheet_to_json(worksheet);
        }

        console.log("JSON data parsed:", json.length, "rows");
        console.log("Sample data:", json.slice(0, 2));

        // Log all column names to help debug
        if (json.length > 0) {
          console.log("Available columns:", Object.keys(json[0]));
        }

        setCompanyData(json);
      } catch (error) {
        console.error("Error parsing file:", error);
        alert("Error parsing file: " + error.message);
      }
    };

    reader.onerror = (error) => {
      console.error("Error reading file:", error);
      alert("Error reading file");
    };

    // Use different reading methods for different file types
    if (file.name.toLowerCase().endsWith('.csv')) {
      reader.readAsText(file);
    } else {
      reader.readAsBinaryString(file);
    }
  };

  /** Categorize field names **/
  const categorizeField = (name) => {
    const lower = name.toLowerCase();
    if (lower.includes("energy") || lower.includes("emission") || lower.includes("water") || lower.includes("waste"))
      return "Environment";
    if (lower.includes("employee") || lower.includes("social") || lower.includes("community") || lower.includes("csr"))
      return "Social";
    return "Governance";
  };

  /** Parse XML into categories **/
  const parseBRSRXML = (xmlText) => {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, "text/xml");
    const contexts = {};
    const units = {};

    // Parse contexts
    const contextElements = xmlDoc.getElementsByTagName("xbrli:context");
    Array.from(contextElements).forEach((ctx) => {
      const id = ctx.getAttribute("id");
      const entityElement = ctx.getElementsByTagName("xbrli:identifier")[0];
      const startDateElement = ctx.getElementsByTagName("xbrli:startDate")[0];
      const endDateElement = ctx.getElementsByTagName("xbrli:endDate")[0];

      contexts[id] = {
        entity: entityElement ? entityElement.textContent : "",
        startDate: startDateElement ? startDateElement.textContent : "",
        endDate: endDateElement ? endDateElement.textContent : "",
      };
    });

    // Parse units
    const unitElements = xmlDoc.getElementsByTagName("xbrli:unit");
    Array.from(unitElements).forEach((u) => {
      const id = u.getAttribute("id");
      const measureElement = u.getElementsByTagName("xbrli:measure")[0];
      units[id] = measureElement ? measureElement.textContent : "";
    });

    // Group data into categories
    const categories = { Environment: [], Social: [], Governance: [] };

    // Get all elements that start with "in-capmkt:"
    const allElements = xmlDoc.getElementsByTagName("*");
    Array.from(allElements).forEach((element) => {
      const tagName = element.tagName;
      if (tagName && tagName.startsWith("in-capmkt:")) {
        const contextRef = element.getAttribute("contextRef");
        const unitRef = element.getAttribute("unitRef");
        const context = contexts[contextRef];
        const unit = units[unitRef];
        const category = categorizeField(tagName);

        categories[category].push({
          metric: tagName.replace("in-capmkt:", ""),
          value: element.textContent || "",
          unit: unit || null,
          period: context ? `${context.startDate} → ${context.endDate}` : "",
          company: context?.entity || "",
        });
      }
    });

    return categories;
  };

  /** View single company **/
  const handleCompanySelect = async (row) => {
    console.log("Company selected:", row);
    console.log("All row keys:", Object.keys(row));
    console.log("Row values:", Object.values(row));

    setSelectedCompany(row);
    setLoadingSingle(true);
    setBrsrData({ Environment: [], Social: [], Governance: [] });

    const xmlLink = getColumnValue(row, ["**XBRL", "XBRL", "XML Link", "XMLLink", "xml_link", "Link", "URL", "url"]);
    console.log("XML Link found:", xmlLink);
    console.log("Checking each possible column:");
    ["**XBRL", "XBRL", "XML Link", "XMLLink", "xml_link", "Link", "URL", "url"].forEach(col => {
      console.log(`  ${col}:`, row[col]);
    });

    if (xmlLink && xmlLink.trim() && xmlLink !== 'No Link') {
      try {
        console.log("Fetching XML from:", xmlLink);
        const res = await axios.get(xmlLink);
        console.log("XML response received, length:", res.data.length);

        const categories = parseBRSRXML(res.data);
        console.log("Parsed categories:", categories);

        setBrsrData(categories);
      } catch (err) {
        console.error("Error loading XML:", err);
        alert("Error loading XML: " + err.message);
      } finally {
        setLoadingSingle(false);
      }
    } else {
      console.log("No valid XML Link found for this company");
      console.log("xmlLink value:", `"${xmlLink}"`);
      setLoadingSingle(false);
      alert("No XML Link found for this company");
    }
  };

  /** Fetch all companies **/
  const fetchAllCompanies = async () => {
    setLoadingAll(true);
    const allData = [];
    for (const row of companyData) {
      if (row["XML Link"]) {
        try {
          const res = await axios.get(row["XML Link"]);
          const categories = parseBRSRXML(res.data);
          ["Environment", "Social", "Governance"].forEach((cat) => {
            categories[cat].forEach((d) =>
              allData.push({
                ...d,
                category: cat,
                companyName: row["Company Name"],
                year: row["Year"],
              })
            );
          });
        } catch (err) {
          console.warn("Failed to fetch:", row["Company Name"]);
        }
      }
    }
    setUnifiedData(allData);
    setLoadingAll(false);
  };

  /** Helper function to get column value with flexible naming **/
  const getColumnValue = (row, possibleNames) => {
    for (const name of possibleNames) {
      if (row[name] !== undefined && row[name] !== null && row[name] !== '') {
        return row[name];
      }
    }
    return '';
  };

  /** Filter data **/
  const filteredTabData = brsrData[activeTab].filter(
    (row) =>
      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||
      row.value.toLowerCase().includes(searchBRSR.toLowerCase())
  );

  return (
    <div style={{ padding: '24px', backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333', marginBottom: '24px', textAlign: 'center' }}>
        📊 BRSR Report Viewer (Excel + XML)
      </h1>

      {/* Upload Excel */}
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '24px' }}>
        <input
          type="file"
          accept=".xlsx,.xls,.csv"
          onChange={handleExcelUpload}
          style={{
            width: '320px',
            padding: '12px',
            border: '1px solid #ddd',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}
        />
        {companyData.length > 0 && (
          <div style={{ marginTop: '8px' }}>
            <div style={{ color: '#28a745', fontWeight: '500' }}>
              ✅ {companyData.length} companies loaded
            </div>
            <div style={{ fontSize: '12px', color: '#6c757d', marginTop: '4px' }}>
              Available columns: {Object.keys(companyData[0]).join(', ')}
            </div>
          </div>
        )}
        <div style={{ marginTop: '8px' }}>
          <button
            onClick={() => {
              // Add sample data for testing
              const sampleData = [
                {
                  "Company Name": "Sample Corp Ltd",
                  "Year": "2023",
                  "XML Link": "https://httpbin.org/xml" // This returns sample XML for testing
                },
                {
                  "Company Name": "Test Industries",
                  "Year": "2023",
                  "XML Link": "https://httpbin.org/xml"
                }
              ];
              setCompanyData(sampleData);
              console.log("Sample data loaded:", sampleData);
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            Load Sample Data (for testing)
          </button>
        </div>
      </div>

      {/* Search and Fetch All */}
      <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', marginBottom: '16px' }}>
        <input
          type="text"
          placeholder="Search company..."
          value={searchCompany}
          onChange={(e) => setSearchCompany(e.target.value)}
          style={{
            width: '320px',
            padding: '12px',
            border: '1px solid #ddd',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}
        />
        <button
          onClick={fetchAllCompanies}
          disabled={loadingAll || companyData.length === 0}
          style={{
            padding: '12px 16px',
            borderRadius: '8px',
            border: 'none',
            color: 'white',
            backgroundColor: loadingAll ? '#6c757d' : '#28a745',
            cursor: loadingAll ? 'not-allowed' : 'pointer',
            opacity: loadingAll ? 0.6 : 1
          }}
        >
          {loadingAll ? "Fetching All..." : "Fetch All Companies"}
        </button>
      </div>

      {/* Company Table */}
      {companyData.length > 0 ? (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          padding: '16px',
          marginBottom: '32px'
        }}>
          <DataTable
            title={`Companies (${companyData.length} loaded)`}
            columns={[
              {
                name: "Company",
                selector: (row) => getColumnValue(row, ["COMPANY", "Company Name", "Company", "CompanyName", "Name", "company_name", "company"]),
                sortable: true
              },
              {
                name: "Year",
                selector: (row) => getColumnValue(row, ["FROM YEAR", "TO YEAR", "Year", "year", "YEAR", "FY", "fy"]),
                sortable: true
              },
              {
                name: "XML Link",
                selector: (row) => getColumnValue(row, ["**XBRL", "XBRL", "XML Link", "XMLLink", "xml_link", "Link", "URL", "url"]),
                sortable: true,
                cell: (row) => {
                  const link = getColumnValue(row, ["**XBRL", "XBRL", "XML Link", "XMLLink", "xml_link", "Link", "URL", "url"]);
                  return link ? (
                    <a href={link} target="_blank" rel="noopener noreferrer" style={{color: '#007bff', fontSize: '12px'}}>
                      {link.length > 30 ? link.substring(0, 30) + '...' : link}
                    </a>
                  ) : 'No Link';
                }
              },
              {
                name: "Actions",
                cell: (row) => (
                  <button
                    onClick={() => handleCompanySelect(row)}
                    style={{
                      backgroundColor: '#007bff',
                      color: 'white',
                      padding: '6px 12px',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '14px'
                    }}
                  >
                    View BRSR
                  </button>
                ),
              },
            ]}
            data={companyData.filter((r) => {
              const companyName = getColumnValue(r, ["COMPANY", "Company Name", "Company", "CompanyName", "Name", "company_name", "company"]);
              return companyName.toLowerCase().includes(searchCompany.toLowerCase());
            })}
            pagination
            highlightOnHover
            dense
          />
        </div>
      ) : (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          padding: '32px',
          marginBottom: '32px',
          textAlign: 'center',
          color: '#6c757d'
        }}>
          <p>📁 No companies loaded yet. Please upload an Excel file to get started.</p>
          <p style={{ fontSize: '14px', marginTop: '8px' }}>
            Expected columns: Company Name, Year, XML Link
          </p>
        </div>
      )}

      {/* Unified Data Table */}
      {unifiedData.length > 0 && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          padding: '24px',
          marginBottom: '32px'
        }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>Unified BRSR Data</h2>
          <DataTable
            columns={[
              { name: "Company", selector: (row) => row.companyName, sortable: true },
              { name: "Year", selector: (row) => row.year, sortable: true },
              { name: "Category", selector: (row) => row.category, sortable: true },
              { name: "Metric", selector: (row) => row.metric, sortable: true },
              { name: "Value", selector: (row) => row.value, sortable: true },
            ]}
            data={unifiedData}
            pagination
            dense
            highlightOnHover
          />
        </div>
      )}

      {/* Tabs for single company */}
      {selectedCompany && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          padding: '24px'
        }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>
            {getColumnValue(selectedCompany, ["COMPANY", "Company Name", "Company", "CompanyName", "Name", "company_name", "company"])}
            ({getColumnValue(selectedCompany, ["FROM YEAR", "TO YEAR", "Year", "year", "YEAR", "FY", "fy"])})
          </h2>

          {/* Tabs */}
          <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
            {["Environment", "Social", "Governance"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                style={{
                  padding: '8px 16px',
                  borderRadius: '8px',
                  border: 'none',
                  fontWeight: '500',
                  cursor: 'pointer',
                  backgroundColor: activeTab === tab
                    ? tab === "Environment"
                      ? "#28a745"
                      : tab === "Social"
                      ? "#6f42c1"
                      : "#fd7e14"
                    : "#e9ecef",
                  color: activeTab === tab ? "white" : "#495057"
                }}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Search */}
          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>
            <input
              type="text"
              placeholder={`Search in ${activeTab}...`}
              value={searchBRSR}
              onChange={(e) => setSearchBRSR(e.target.value)}
              style={{
                width: '320px',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}
            />
          </div>

          {/* Table */}
          {loadingSingle ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>
              <span style={{ fontSize: '2rem', color: '#007bff' }}>⏳</span>
            </div>
          ) : (
            <DataTable
              columns={[
                { name: "Metric", selector: (row) => row.metric, sortable: true },
                { name: "Value", selector: (row) => row.value, sortable: true },
                { name: "Unit", selector: (row) => row.unit || "-", sortable: true },
                { name: "Period", selector: (row) => row.period, sortable: true },
              ]}
              data={filteredTabData}
              pagination
              dense
              highlightOnHover
            />
          )}
        </div>
      )}
    </div>
  );
}
