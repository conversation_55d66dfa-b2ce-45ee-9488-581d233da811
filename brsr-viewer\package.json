{"name": "brsr-viewer", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.11.0", "bootstrap": "^5.3.7", "react": "^19.1.1", "react-data-table-component": "^7.7.0", "react-dom": "^19.1.1", "react-scripts": "5.0.1", "timers-browserify": "^2.0.12", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6"}}