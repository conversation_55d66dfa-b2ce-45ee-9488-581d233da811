{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLCharacterData,\n    XMLComment,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  NodeType = require('./NodeType');\n  XMLCharacterData = require('./XMLCharacterData');\n  module.exports = XMLComment = function (superClass) {\n    extend(XMLComment, superClass);\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.name = \"#comment\";\n      this.type = NodeType.Comment;\n      this.value = this.stringify.comment(text);\n    }\n    XMLComment.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLComment.prototype.toString = function (options) {\n      return this.options.writer.comment(this, this.options.writer.filterOptions(options));\n    };\n    return XMLComment;\n  }(XMLCharacterData);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLCharacterData", "XMLComment", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "text", "Error", "debugInfo", "name", "type", "Comment", "value", "stringify", "comment", "clone", "Object", "create", "toString", "options", "writer", "filterOptions"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLComment.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLComment,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.name = \"#comment\";\n      this.type = NodeType.Comment;\n      this.value = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.comment(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLComment;\n\n  })(XMLCharacterData);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,gBAAgB;IAAEC,UAAU;IACxCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7Bb,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCb,gBAAgB,GAAGa,OAAO,CAAC,oBAAoB,CAAC;EAEhDC,MAAM,CAACC,OAAO,GAAGd,UAAU,GAAI,UAASe,UAAU,EAAE;IAClDd,MAAM,CAACD,UAAU,EAAEe,UAAU,CAAC;IAE9B,SAASf,UAAUA,CAACG,MAAM,EAAEa,IAAI,EAAE;MAChChB,UAAU,CAACU,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACnD,IAAIa,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAC9D;MACA,IAAI,CAACC,IAAI,GAAG,UAAU;MACtB,IAAI,CAACC,IAAI,GAAGtB,QAAQ,CAACuB,OAAO;MAC5B,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAACC,OAAO,CAACR,IAAI,CAAC;IAC3C;IAEAhB,UAAU,CAACS,SAAS,CAACgB,KAAK,GAAG,YAAW;MACtC,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED3B,UAAU,CAACS,SAAS,CAACmB,QAAQ,GAAG,UAASC,OAAO,EAAE;MAChD,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACN,OAAO,CAAC,IAAI,EAAE,IAAI,CAACK,OAAO,CAACC,MAAM,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC;IACtF,CAAC;IAED,OAAO7B,UAAU;EAEnB,CAAC,CAAED,gBAAgB,CAAC;AAEtB,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}