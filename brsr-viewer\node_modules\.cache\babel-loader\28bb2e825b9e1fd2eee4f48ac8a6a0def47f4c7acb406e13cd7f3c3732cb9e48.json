{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLNamedNodeMap;\n  module.exports = XMLNamedNodeMap = function () {\n    function XMLNamedNodeMap(nodes) {\n      this.nodes = nodes;\n    }\n    Object.defineProperty(XMLNamedNodeMap.prototype, 'length', {\n      get: function () {\n        return Object.keys(this.nodes).length || 0;\n      }\n    });\n    XMLNamedNodeMap.prototype.clone = function () {\n      return this.nodes = null;\n    };\n    XMLNamedNodeMap.prototype.getNamedItem = function (name) {\n      return this.nodes[name];\n    };\n    XMLNamedNodeMap.prototype.setNamedItem = function (node) {\n      var oldNode;\n      oldNode = this.nodes[node.nodeName];\n      this.nodes[node.nodeName] = node;\n      return oldNode || null;\n    };\n    XMLNamedNodeMap.prototype.removeNamedItem = function (name) {\n      var oldNode;\n      oldNode = this.nodes[name];\n      delete this.nodes[name];\n      return oldNode || null;\n    };\n    XMLNamedNodeMap.prototype.item = function (index) {\n      return this.nodes[Object.keys(this.nodes)[index]] || null;\n    };\n    XMLNamedNodeMap.prototype.getNamedItemNS = function (namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    XMLNamedNodeMap.prototype.setNamedItemNS = function (node) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    XMLNamedNodeMap.prototype.removeNamedItemNS = function (namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    return XMLNamedNodeMap;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["XMLNamedNodeMap", "module", "exports", "nodes", "Object", "defineProperty", "prototype", "get", "keys", "length", "clone", "getNamedItem", "name", "setNamedItem", "node", "oldNode", "nodeName", "removeNamedItem", "item", "index", "getNamedItemNS", "namespaceURI", "localName", "Error", "setNamedItemNS", "removeNamedItemNS", "call"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNamedNodeMap;\n\n  module.exports = XMLNamedNodeMap = (function() {\n    function XMLNamedNodeMap(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNamedNodeMap.prototype, 'length', {\n      get: function() {\n        return Object.keys(this.nodes).length || 0;\n      }\n    });\n\n    XMLNamedNodeMap.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItem = function(name) {\n      return this.nodes[name];\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItem = function(node) {\n      var oldNode;\n      oldNode = this.nodes[node.nodeName];\n      this.nodes[node.nodeName] = node;\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItem = function(name) {\n      var oldNode;\n      oldNode = this.nodes[name];\n      delete this.nodes[name];\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.item = function(index) {\n      return this.nodes[Object.keys(this.nodes)[index]] || null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItemNS = function(node) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLNamedNodeMap;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,eAAe;EAEnBC,MAAM,CAACC,OAAO,GAAGF,eAAe,GAAI,YAAW;IAC7C,SAASA,eAAeA,CAACG,KAAK,EAAE;MAC9B,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB;IAEAC,MAAM,CAACC,cAAc,CAACL,eAAe,CAACM,SAAS,EAAE,QAAQ,EAAE;MACzDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAOH,MAAM,CAACI,IAAI,CAAC,IAAI,CAACL,KAAK,CAAC,CAACM,MAAM,IAAI,CAAC;MAC5C;IACF,CAAC,CAAC;IAEFT,eAAe,CAACM,SAAS,CAACI,KAAK,GAAG,YAAW;MAC3C,OAAO,IAAI,CAACP,KAAK,GAAG,IAAI;IAC1B,CAAC;IAEDH,eAAe,CAACM,SAAS,CAACK,YAAY,GAAG,UAASC,IAAI,EAAE;MACtD,OAAO,IAAI,CAACT,KAAK,CAACS,IAAI,CAAC;IACzB,CAAC;IAEDZ,eAAe,CAACM,SAAS,CAACO,YAAY,GAAG,UAASC,IAAI,EAAE;MACtD,IAAIC,OAAO;MACXA,OAAO,GAAG,IAAI,CAACZ,KAAK,CAACW,IAAI,CAACE,QAAQ,CAAC;MACnC,IAAI,CAACb,KAAK,CAACW,IAAI,CAACE,QAAQ,CAAC,GAAGF,IAAI;MAChC,OAAOC,OAAO,IAAI,IAAI;IACxB,CAAC;IAEDf,eAAe,CAACM,SAAS,CAACW,eAAe,GAAG,UAASL,IAAI,EAAE;MACzD,IAAIG,OAAO;MACXA,OAAO,GAAG,IAAI,CAACZ,KAAK,CAACS,IAAI,CAAC;MAC1B,OAAO,IAAI,CAACT,KAAK,CAACS,IAAI,CAAC;MACvB,OAAOG,OAAO,IAAI,IAAI;IACxB,CAAC;IAEDf,eAAe,CAACM,SAAS,CAACY,IAAI,GAAG,UAASC,KAAK,EAAE;MAC/C,OAAO,IAAI,CAAChB,KAAK,CAACC,MAAM,CAACI,IAAI,CAAC,IAAI,CAACL,KAAK,CAAC,CAACgB,KAAK,CAAC,CAAC,IAAI,IAAI;IAC3D,CAAC;IAEDnB,eAAe,CAACM,SAAS,CAACc,cAAc,GAAG,UAASC,YAAY,EAAEC,SAAS,EAAE;MAC3E,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAEDvB,eAAe,CAACM,SAAS,CAACkB,cAAc,GAAG,UAASV,IAAI,EAAE;MACxD,MAAM,IAAIS,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAEDvB,eAAe,CAACM,SAAS,CAACmB,iBAAiB,GAAG,UAASJ,YAAY,EAAEC,SAAS,EAAE;MAC9E,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAED,OAAOvB,eAAe;EAExB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAE0B,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}