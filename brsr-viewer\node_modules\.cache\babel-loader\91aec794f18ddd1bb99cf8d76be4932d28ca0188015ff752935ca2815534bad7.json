{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\brsr_reports\\\\brsr-viewer\\\\src\\\\BRSRViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BRSRViewer() {\n  _s();\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({\n    Environment: [],\n    Social: [],\n    Governance: []\n  });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = evt => {\n      const workbook = XLSX.read(evt.target.result, {\n        type: \"binary\"\n      });\n      const sheetName = workbook.SheetNames[0];\n      const worksheet = workbook.Sheets[sheetName];\n      const json = XLSX.utils.sheet_to_json(worksheet);\n      setCompanyData(json);\n    };\n    reader.readAsBinaryString(file);\n  };\n\n  /** Categorize field names **/\n  const categorizeField = name => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\")) return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\")) return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = xmlText => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach(ctx => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\"\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach(u => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = {\n      Environment: [],\n      Social: [],\n      Governance: []\n    };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach(element => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: (context === null || context === void 0 ? void 0 : context.entity) || \"\"\n        });\n      }\n    });\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async row => {\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({\n      Environment: [],\n      Social: [],\n      Governance: []\n    });\n    if (row[\"XML Link\"]) {\n      try {\n        const res = await axios.get(row[\"XML Link\"]);\n        const categories = parseBRSRXML(res.data);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n      } finally {\n        setLoadingSingle(false);\n      }\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach(cat => {\n            categories[cat].forEach(d => allData.push({\n              ...d,\n              category: cat,\n              companyName: row[\"Company Name\"],\n              year: row[\"Year\"]\n            }));\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(row => row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) || row.value.toLowerCase().includes(searchBRSR.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        fontSize: '2rem',\n        fontWeight: 'bold',\n        color: '#333',\n        marginBottom: '24px',\n        textAlign: 'center'\n      },\n      children: \"\\uD83D\\uDCCA BRSR Report Viewer (Excel + XML)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".xlsx,.xls\",\n        onChange: handleExcelUpload,\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '16px',\n        marginBottom: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Search company...\",\n        value: searchCompany,\n        onChange: e => setSearchCompany(e.target.value),\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchAllCompanies,\n        disabled: loadingAll || companyData.length === 0,\n        style: {\n          padding: '12px 16px',\n          borderRadius: '8px',\n          border: 'none',\n          color: 'white',\n          backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n          cursor: loadingAll ? 'not-allowed' : 'pointer',\n          opacity: loadingAll ? 0.6 : 1\n        },\n        children: loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '16px',\n        marginBottom: '32px'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataTable, {\n        title: \"Companies\",\n        columns: [{\n          name: \"Company\",\n          selector: row => row[\"Company Name\"],\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => row[\"Year\"],\n          sortable: true\n        }, {\n          name: \"Actions\",\n          cell: row => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCompanySelect(row),\n            style: {\n              backgroundColor: '#007bff',\n              color: 'white',\n              padding: '6px 12px',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            },\n            children: \"View BRSR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this)\n        }],\n        data: companyData.filter(r => {\n          var _r$CompanyName;\n          return (_r$CompanyName = r[\"Company Name\"]) === null || _r$CompanyName === void 0 ? void 0 : _r$CompanyName.toLowerCase().includes(searchCompany.toLowerCase());\n        }),\n        pagination: true,\n        highlightOnHover: true,\n        dense: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), unifiedData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px',\n        marginBottom: '32px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: \"Unified BRSR Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Company\",\n          selector: row => row.companyName,\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => row.year,\n          sortable: true\n        }, {\n          name: \"Category\",\n          selector: row => row.category,\n          sortable: true\n        }, {\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }],\n        data: unifiedData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 9\n    }, this), selectedCompany && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: [selectedCompany[\"Company Name\"], \" (\", selectedCompany[\"Year\"], \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '16px',\n          marginBottom: '16px'\n        },\n        children: [\"Environment\", \"Social\", \"Governance\"].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab),\n          style: {\n            padding: '8px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            fontWeight: '500',\n            cursor: 'pointer',\n            backgroundColor: activeTab === tab ? tab === \"Environment\" ? \"#28a745\" : tab === \"Social\" ? \"#6f42c1\" : \"#fd7e14\" : \"#e9ecef\",\n            color: activeTab === tab ? \"white\" : \"#495057\"\n          },\n          children: tab\n        }, tab, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: `Search in ${activeTab}...`,\n          value: searchBRSR,\n          onChange: e => setSearchBRSR(e.target.value),\n          style: {\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this), loadingSingle ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '40px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2rem',\n            color: '#007bff'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }, {\n          name: \"Unit\",\n          selector: row => row.unit || \"-\",\n          sortable: true\n        }, {\n          name: \"Period\",\n          selector: row => row.period,\n          sortable: true\n        }],\n        data: filteredTabData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n}\n_s(BRSRViewer, \"XqWeNAlGtEHiedqF0JxGSKVABwY=\");\n_c = BRSRViewer;\nvar _c;\n$RefreshReg$(_c, \"BRSRViewer\");", "map": {"version": 3, "names": ["React", "useState", "XLSX", "axios", "DataTable", "jsxDEV", "_jsxDEV", "BRSRViewer", "_s", "companyData", "setCompanyData", "searchCompany", "setSearchCompany", "selectedCompany", "setSelectedCompany", "brsrData", "setBrsrData", "Environment", "Social", "Governance", "searchBRSR", "setSearchBRSR", "activeTab", "setActiveTab", "unifiedData", "setUnifiedData", "loadingAll", "setLoadingAll", "loadingSingle", "setLoading<PERSON>ingle", "handleExcelUpload", "e", "file", "target", "files", "reader", "FileReader", "onload", "evt", "workbook", "read", "result", "type", "sheetName", "SheetNames", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "readAsBinaryString", "categorizeField", "name", "lower", "toLowerCase", "includes", "parseBRSRXML", "xmlText", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "contexts", "units", "contextElements", "getElementsByTagName", "Array", "from", "for<PERSON>ach", "ctx", "id", "getAttribute", "entityElement", "startDateElement", "endDateElement", "entity", "textContent", "startDate", "endDate", "unitElements", "u", "measureElement", "categories", "allElements", "element", "tagName", "startsWith", "contextRef", "unitRef", "context", "unit", "category", "push", "metric", "replace", "value", "period", "company", "handleCompanySelect", "row", "res", "get", "data", "err", "console", "error", "fetchAllCompanies", "allData", "cat", "d", "companyName", "year", "warn", "filteredTabData", "filter", "style", "padding", "backgroundColor", "minHeight", "children", "fontSize", "fontWeight", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "accept", "onChange", "width", "border", "borderRadius", "boxShadow", "gap", "placeholder", "onClick", "disabled", "length", "cursor", "opacity", "title", "columns", "selector", "sortable", "cell", "r", "_r$CompanyName", "pagination", "highlightOnHover", "dense", "map", "tab", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/src/BRSRViewer.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\n\nexport default function BRSRViewer() {\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = (e) => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = (evt) => {\n      const workbook = XLSX.read(evt.target.result, { type: \"binary\" });\n      const sheetName = workbook.SheetNames[0];\n      const worksheet = workbook.Sheets[sheetName];\n      const json = XLSX.utils.sheet_to_json(worksheet);\n      setCompanyData(json);\n    };\n    reader.readAsBinaryString(file);\n  };\n\n  /** Categorize field names **/\n  const categorizeField = (name) => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\"))\n      return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\"))\n      return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = (xmlText) => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach((ctx) => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\",\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach((u) => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = { Environment: [], Social: [], Governance: [] };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach((element) => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: context?.entity || \"\",\n        });\n      }\n    });\n\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async (row) => {\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({ Environment: [], Social: [], Governance: [] });\n\n    if (row[\"XML Link\"]) {\n      try {\n        const res = await axios.get(row[\"XML Link\"]);\n        const categories = parseBRSRXML(res.data);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n      } finally {\n        setLoadingSingle(false);\n      }\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach((cat) => {\n            categories[cat].forEach((d) =>\n              allData.push({\n                ...d,\n                category: cat,\n                companyName: row[\"Company Name\"],\n                year: row[\"Year\"],\n              })\n            );\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(\n    (row) =>\n      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||\n      row.value.toLowerCase().includes(searchBRSR.toLowerCase())\n  );\n\n  return (\n    <div style={{ padding: '24px', backgroundColor: '#f8f9fa', minHeight: '100vh' }}>\n      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333', marginBottom: '24px', textAlign: 'center' }}>\n        📊 BRSR Report Viewer (Excel + XML)\n      </h1>\n\n      {/* Upload Excel */}\n      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '24px' }}>\n        <input\n          type=\"file\"\n          accept=\".xlsx,.xls\"\n          onChange={handleExcelUpload}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n      </div>\n\n      {/* Search and Fetch All */}\n      <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', marginBottom: '16px' }}>\n        <input\n          type=\"text\"\n          placeholder=\"Search company...\"\n          value={searchCompany}\n          onChange={(e) => setSearchCompany(e.target.value)}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        <button\n          onClick={fetchAllCompanies}\n          disabled={loadingAll || companyData.length === 0}\n          style={{\n            padding: '12px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            color: 'white',\n            backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n            cursor: loadingAll ? 'not-allowed' : 'pointer',\n            opacity: loadingAll ? 0.6 : 1\n          }}\n        >\n          {loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"}\n        </button>\n      </div>\n\n      {/* Company Table */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '16px',\n        marginBottom: '32px'\n      }}>\n        <DataTable\n          title=\"Companies\"\n          columns={[\n            { name: \"Company\", selector: (row) => row[\"Company Name\"], sortable: true },\n            { name: \"Year\", selector: (row) => row[\"Year\"], sortable: true },\n            {\n              name: \"Actions\",\n              cell: (row) => (\n                <button\n                  onClick={() => handleCompanySelect(row)}\n                  style={{\n                    backgroundColor: '#007bff',\n                    color: 'white',\n                    padding: '6px 12px',\n                    border: 'none',\n                    borderRadius: '4px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  }}\n                >\n                  View BRSR\n                </button>\n              ),\n            },\n          ]}\n          data={companyData.filter((r) =>\n            r[\"Company Name\"]?.toLowerCase().includes(searchCompany.toLowerCase())\n          )}\n          pagination\n          highlightOnHover\n          dense\n        />\n      </div>\n\n      {/* Unified Data Table */}\n      {unifiedData.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px',\n          marginBottom: '32px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>Unified BRSR Data</h2>\n          <DataTable\n            columns={[\n              { name: \"Company\", selector: (row) => row.companyName, sortable: true },\n              { name: \"Year\", selector: (row) => row.year, sortable: true },\n              { name: \"Category\", selector: (row) => row.category, sortable: true },\n              { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n              { name: \"Value\", selector: (row) => row.value, sortable: true },\n            ]}\n            data={unifiedData}\n            pagination\n            dense\n            highlightOnHover\n          />\n        </div>\n      )}\n\n      {/* Tabs for single company */}\n      {selectedCompany && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>\n            {selectedCompany[\"Company Name\"]} ({selectedCompany[\"Year\"]})\n          </h2>\n\n          {/* Tabs */}\n          <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>\n            {[\"Environment\", \"Social\", \"Governance\"].map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                style={{\n                  padding: '8px 16px',\n                  borderRadius: '8px',\n                  border: 'none',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  backgroundColor: activeTab === tab\n                    ? tab === \"Environment\"\n                      ? \"#28a745\"\n                      : tab === \"Social\"\n                      ? \"#6f42c1\"\n                      : \"#fd7e14\"\n                    : \"#e9ecef\",\n                  color: activeTab === tab ? \"white\" : \"#495057\"\n                }}\n              >\n                {tab}\n              </button>\n            ))}\n          </div>\n\n          {/* Search */}\n          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>\n            <input\n              type=\"text\"\n              placeholder={`Search in ${activeTab}...`}\n              value={searchBRSR}\n              onChange={(e) => setSearchBRSR(e.target.value)}\n              style={{\n                width: '320px',\n                padding: '12px',\n                border: '1px solid #ddd',\n                borderRadius: '8px',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }}\n            />\n          </div>\n\n          {/* Table */}\n          {loadingSingle ? (\n            <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>\n              <span style={{ fontSize: '2rem', color: '#007bff' }}>⏳</span>\n            </div>\n          ) : (\n            <DataTable\n              columns={[\n                { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n                { name: \"Value\", selector: (row) => row.value, sortable: true },\n                { name: \"Unit\", selector: (row) => row.unit || \"-\", sortable: true },\n                { name: \"Period\", selector: (row) => row.period, sortable: true },\n              ]}\n              data={filteredTabData}\n              pagination\n              dense\n              highlightOnHover\n            />\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IAAEgB,WAAW,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC;EACzF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;IACX,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,GAAG,IAAK;MACvB,MAAMC,QAAQ,GAAGrC,IAAI,CAACsC,IAAI,CAACF,GAAG,CAACL,MAAM,CAACQ,MAAM,EAAE;QAAEC,IAAI,EAAE;MAAS,CAAC,CAAC;MACjE,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,UAAU,CAAC,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGN,QAAQ,CAACO,MAAM,CAACH,SAAS,CAAC;MAC5C,MAAMI,IAAI,GAAG7C,IAAI,CAAC8C,KAAK,CAACC,aAAa,CAACJ,SAAS,CAAC;MAChDnC,cAAc,CAACqC,IAAI,CAAC;IACtB,CAAC;IACDZ,MAAM,CAACe,kBAAkB,CAAClB,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,KAAK,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;IAChC,IAAID,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,EAC9G,OAAO,aAAa;IACtB,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAC,EAChH,OAAO,QAAQ;IACjB,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;IAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,OAAO,EAAE,UAAU,CAAC;IAC1D,MAAMK,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,MAAMC,eAAe,GAAGJ,MAAM,CAACK,oBAAoB,CAAC,eAAe,CAAC;IACpEC,KAAK,CAACC,IAAI,CAACH,eAAe,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;MAC3C,MAAMC,EAAE,GAAGD,GAAG,CAACE,YAAY,CAAC,IAAI,CAAC;MACjC,MAAMC,aAAa,GAAGH,GAAG,CAACJ,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACrE,MAAMQ,gBAAgB,GAAGJ,GAAG,CAACJ,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMS,cAAc,GAAGL,GAAG,CAACJ,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MAEnEH,QAAQ,CAACQ,EAAE,CAAC,GAAG;QACbK,MAAM,EAAEH,aAAa,GAAGA,aAAa,CAACI,WAAW,GAAG,EAAE;QACtDC,SAAS,EAAEJ,gBAAgB,GAAGA,gBAAgB,CAACG,WAAW,GAAG,EAAE;QAC/DE,OAAO,EAAEJ,cAAc,GAAGA,cAAc,CAACE,WAAW,GAAG;MACzD,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMG,YAAY,GAAGnB,MAAM,CAACK,oBAAoB,CAAC,YAAY,CAAC;IAC9DC,KAAK,CAACC,IAAI,CAACY,YAAY,CAAC,CAACX,OAAO,CAAEY,CAAC,IAAK;MACtC,MAAMV,EAAE,GAAGU,CAAC,CAACT,YAAY,CAAC,IAAI,CAAC;MAC/B,MAAMU,cAAc,GAAGD,CAAC,CAACf,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACjEF,KAAK,CAACO,EAAE,CAAC,GAAGW,cAAc,GAAGA,cAAc,CAACL,WAAW,GAAG,EAAE;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,UAAU,GAAG;MAAEjE,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;;IAElE;IACA,MAAMgE,WAAW,GAAGvB,MAAM,CAACK,oBAAoB,CAAC,GAAG,CAAC;IACpDC,KAAK,CAACC,IAAI,CAACgB,WAAW,CAAC,CAACf,OAAO,CAAEgB,OAAO,IAAK;MAC3C,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO;MAC/B,IAAIA,OAAO,IAAIA,OAAO,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC/C,MAAMC,UAAU,GAAGH,OAAO,CAACb,YAAY,CAAC,YAAY,CAAC;QACrD,MAAMiB,OAAO,GAAGJ,OAAO,CAACb,YAAY,CAAC,SAAS,CAAC;QAC/C,MAAMkB,OAAO,GAAG3B,QAAQ,CAACyB,UAAU,CAAC;QACpC,MAAMG,IAAI,GAAG3B,KAAK,CAACyB,OAAO,CAAC;QAC3B,MAAMG,QAAQ,GAAGxC,eAAe,CAACkC,OAAO,CAAC;QAEzCH,UAAU,CAACS,QAAQ,CAAC,CAACC,IAAI,CAAC;UACxBC,MAAM,EAAER,OAAO,CAACS,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;UACzCC,KAAK,EAAEX,OAAO,CAACR,WAAW,IAAI,EAAE;UAChCc,IAAI,EAAEA,IAAI,IAAI,IAAI;UAClBM,MAAM,EAAEP,OAAO,GAAG,GAAGA,OAAO,CAACZ,SAAS,MAAMY,OAAO,CAACX,OAAO,EAAE,GAAG,EAAE;UAClEmB,OAAO,EAAE,CAAAR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEd,MAAM,KAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMgB,mBAAmB,GAAG,MAAOC,GAAG,IAAK;IACzCrF,kBAAkB,CAACqF,GAAG,CAAC;IACvBtE,gBAAgB,CAAC,IAAI,CAAC;IACtBb,WAAW,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC,CAAC;IAE5D,IAAIgF,GAAG,CAAC,UAAU,CAAC,EAAE;MACnB,IAAI;QACF,MAAMC,GAAG,GAAG,MAAMjG,KAAK,CAACkG,GAAG,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAMjB,UAAU,GAAG1B,YAAY,CAAC4C,GAAG,CAACE,IAAI,CAAC;QACzCtF,WAAW,CAACkE,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOqB,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,SAAS;QACR1E,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF;EACF,CAAC;;EAED;EACA,MAAM6E,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC/E,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMgF,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMR,GAAG,IAAI1F,WAAW,EAAE;MAC7B,IAAI0F,GAAG,CAAC,UAAU,CAAC,EAAE;QACnB,IAAI;UACF,MAAMC,GAAG,GAAG,MAAMjG,KAAK,CAACkG,GAAG,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;UAC5C,MAAMjB,UAAU,GAAG1B,YAAY,CAAC4C,GAAG,CAACE,IAAI,CAAC;UACzC,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAClC,OAAO,CAAEwC,GAAG,IAAK;YACvD1B,UAAU,CAAC0B,GAAG,CAAC,CAACxC,OAAO,CAAEyC,CAAC,IACxBF,OAAO,CAACf,IAAI,CAAC;cACX,GAAGiB,CAAC;cACJlB,QAAQ,EAAEiB,GAAG;cACbE,WAAW,EAAEX,GAAG,CAAC,cAAc,CAAC;cAChCY,IAAI,EAAEZ,GAAG,CAAC,MAAM;YAClB,CAAC,CACH,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZC,OAAO,CAACQ,IAAI,CAAC,kBAAkB,EAAEb,GAAG,CAAC,cAAc,CAAC,CAAC;QACvD;MACF;IACF;IACA1E,cAAc,CAACkF,OAAO,CAAC;IACvBhF,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMsF,eAAe,GAAGlG,QAAQ,CAACO,SAAS,CAAC,CAAC4F,MAAM,CAC/Cf,GAAG,IACFA,GAAG,CAACN,MAAM,CAACvC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAAC,IAC3D6C,GAAG,CAACJ,KAAK,CAACzC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAC7D,CAAC;EAED,oBACEhD,OAAA;IAAK6G,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC9EjH,OAAA;MAAI6G,KAAK,EAAE;QAAEK,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,YAAY,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAE/G;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGL1H,OAAA;MAAK6G,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEP,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,eAC9EjH,OAAA;QACEoC,IAAI,EAAC,MAAM;QACXyF,MAAM,EAAC,YAAY;QACnBC,QAAQ,EAAEtG,iBAAkB;QAC5BqF,KAAK,EAAE;UACLkB,KAAK,EAAE,OAAO;UACdjB,OAAO,EAAE,MAAM;UACfkB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1H,OAAA;MAAK6G,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEO,GAAG,EAAE,MAAM;QAAEd,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAC3FjH,OAAA;QACEoC,IAAI,EAAC,MAAM;QACXgG,WAAW,EAAC,mBAAmB;QAC/B3C,KAAK,EAAEpF,aAAc;QACrByH,QAAQ,EAAGrG,CAAC,IAAKnB,gBAAgB,CAACmB,CAAC,CAACE,MAAM,CAAC8D,KAAK,CAAE;QAClDoB,KAAK,EAAE;UACLkB,KAAK,EAAE,OAAO;UACdjB,OAAO,EAAE,MAAM;UACfkB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF1H,OAAA;QACEqI,OAAO,EAAEjC,iBAAkB;QAC3BkC,QAAQ,EAAElH,UAAU,IAAIjB,WAAW,CAACoI,MAAM,KAAK,CAAE;QACjD1B,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBmB,YAAY,EAAE,KAAK;UACnBD,MAAM,EAAE,MAAM;UACdZ,KAAK,EAAE,OAAO;UACdL,eAAe,EAAE3F,UAAU,GAAG,SAAS,GAAG,SAAS;UACnDoH,MAAM,EAAEpH,UAAU,GAAG,aAAa,GAAG,SAAS;UAC9CqH,OAAO,EAAErH,UAAU,GAAG,GAAG,GAAG;QAC9B,CAAE;QAAA6F,QAAA,EAED7F,UAAU,GAAG,iBAAiB,GAAG;MAAqB;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1H,OAAA;MAAK6G,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBkB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCpB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,eACAjH,OAAA,CAACF,SAAS;QACR4I,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAE,CACP;UAAE7F,IAAI,EAAE,SAAS;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAAC,cAAc,CAAC;UAAEgD,QAAQ,EAAE;QAAK,CAAC,EAC3E;UAAE/F,IAAI,EAAE,MAAM;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAAC,MAAM,CAAC;UAAEgD,QAAQ,EAAE;QAAK,CAAC,EAChE;UACE/F,IAAI,EAAE,SAAS;UACfgG,IAAI,EAAGjD,GAAG,iBACR7F,OAAA;YACEqI,OAAO,EAAEA,CAAA,KAAMzC,mBAAmB,CAACC,GAAG,CAAE;YACxCgB,KAAK,EAAE;cACLE,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,OAAO;cACdN,OAAO,EAAE,UAAU;cACnBkB,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBO,MAAM,EAAE,SAAS;cACjBtB,QAAQ,EAAE;YACZ,CAAE;YAAAD,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAEZ,CAAC,CACD;QACF1B,IAAI,EAAE7F,WAAW,CAACyG,MAAM,CAAEmC,CAAC;UAAA,IAAAC,cAAA;UAAA,QAAAA,cAAA,GACzBD,CAAC,CAAC,cAAc,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmBhG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5C,aAAa,CAAC2C,WAAW,CAAC,CAAC,CAAC;QAAA,CACxE,CAAE;QACFiG,UAAU;QACVC,gBAAgB;QAChBC,KAAK;MAAA;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLxG,WAAW,CAACqH,MAAM,GAAG,CAAC,iBACrBvI,OAAA;MAAK6G,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBkB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCpB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,gBACAjH,OAAA;QAAI6G,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClG1H,OAAA,CAACF,SAAS;QACR6I,OAAO,EAAE,CACP;UAAE7F,IAAI,EAAE,SAAS;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACW,WAAW;UAAEqC,QAAQ,EAAE;QAAK,CAAC,EACvE;UAAE/F,IAAI,EAAE,MAAM;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACY,IAAI;UAAEoC,QAAQ,EAAE;QAAK,CAAC,EAC7D;UAAE/F,IAAI,EAAE,UAAU;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACR,QAAQ;UAAEwD,QAAQ,EAAE;QAAK,CAAC,EACrE;UAAE/F,IAAI,EAAE,QAAQ;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACN,MAAM;UAAEsD,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAE/F,IAAI,EAAE,OAAO;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACJ,KAAK;UAAEoD,QAAQ,EAAE;QAAK,CAAC,CAC/D;QACF7C,IAAI,EAAE9E,WAAY;QAClB+H,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAnH,eAAe,iBACdP,OAAA;MAAK6G,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBkB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCpB,OAAO,EAAE;MACX,CAAE;MAAAG,QAAA,gBACAjH,OAAA;QAAI6G,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,GACxE1G,eAAe,CAAC,cAAc,CAAC,EAAC,IAAE,EAACA,eAAe,CAAC,MAAM,CAAC,EAAC,GAC9D;MAAA;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGL1H,OAAA;QAAK6G,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,MAAM;UAAEd,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAChE,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACmC,GAAG,CAAEC,GAAG,iBAC/CrJ,OAAA;UAEEqI,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAACoI,GAAG,CAAE;UACjCxC,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBmB,YAAY,EAAE,KAAK;YACnBD,MAAM,EAAE,MAAM;YACdb,UAAU,EAAE,KAAK;YACjBqB,MAAM,EAAE,SAAS;YACjBzB,eAAe,EAAE/F,SAAS,KAAKqI,GAAG,GAC9BA,GAAG,KAAK,aAAa,GACnB,SAAS,GACTA,GAAG,KAAK,QAAQ,GAChB,SAAS,GACT,SAAS,GACX,SAAS;YACbjC,KAAK,EAAEpG,SAAS,KAAKqI,GAAG,GAAG,OAAO,GAAG;UACvC,CAAE;UAAApC,QAAA,EAEDoC;QAAG,GAlBCA,GAAG;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1H,OAAA;QAAK6G,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEP,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC9EjH,OAAA;UACEoC,IAAI,EAAC,MAAM;UACXgG,WAAW,EAAE,aAAapH,SAAS,KAAM;UACzCyE,KAAK,EAAE3E,UAAW;UAClBgH,QAAQ,EAAGrG,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACE,MAAM,CAAC8D,KAAK,CAAE;UAC/CoB,KAAK,EAAE;YACLkB,KAAK,EAAE,OAAO;YACdjB,OAAO,EAAE,MAAM;YACfkB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACb;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLpG,aAAa,gBACZtB,OAAA;QAAK6G,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEd,OAAO,EAAE;QAAS,CAAE;QAAAG,QAAA,eAC3EjH,OAAA;UAAM6G,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAEN1H,OAAA,CAACF,SAAS;QACR6I,OAAO,EAAE,CACP;UAAE7F,IAAI,EAAE,QAAQ;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACN,MAAM;UAAEsD,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAE/F,IAAI,EAAE,OAAO;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACJ,KAAK;UAAEoD,QAAQ,EAAE;QAAK,CAAC,EAC/D;UAAE/F,IAAI,EAAE,MAAM;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACT,IAAI,IAAI,GAAG;UAAEyD,QAAQ,EAAE;QAAK,CAAC,EACpE;UAAE/F,IAAI,EAAE,QAAQ;UAAE8F,QAAQ,EAAG/C,GAAG,IAAKA,GAAG,CAACH,MAAM;UAAEmD,QAAQ,EAAE;QAAK,CAAC,CACjE;QACF7C,IAAI,EAAEW,eAAgB;QACtBsC,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACxH,EAAA,CA5VuBD,UAAU;AAAAqJ,EAAA,GAAVrJ,UAAU;AAAA,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}