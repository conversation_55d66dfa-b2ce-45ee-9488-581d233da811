{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLCharacterData,\n    XMLNode,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  XMLNode = require('./XMLNode');\n  module.exports = XMLCharacterData = function (superClass) {\n    extend(XMLCharacterData, superClass);\n    function XMLCharacterData(parent) {\n      XMLCharacterData.__super__.constructor.call(this, parent);\n      this.value = '';\n    }\n    Object.defineProperty(XMLCharacterData.prototype, 'data', {\n      get: function () {\n        return this.value;\n      },\n      set: function (value) {\n        return this.value = value || '';\n      }\n    });\n    Object.defineProperty(XMLCharacterData.prototype, 'length', {\n      get: function () {\n        return this.value.length;\n      }\n    });\n    Object.defineProperty(XMLCharacterData.prototype, 'textContent', {\n      get: function () {\n        return this.value;\n      },\n      set: function (value) {\n        return this.value = value || '';\n      }\n    });\n    XMLCharacterData.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLCharacterData.prototype.substringData = function (offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLCharacterData.prototype.appendData = function (arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLCharacterData.prototype.insertData = function (offset, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLCharacterData.prototype.deleteData = function (offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLCharacterData.prototype.replaceData = function (offset, count, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLCharacterData.prototype.isEqualNode = function (node) {\n      if (!XMLCharacterData.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.data !== this.data) {\n        return false;\n      }\n      return true;\n    };\n    return XMLCharacterData;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["XMLCharacterData", "XMLNode", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "value", "Object", "defineProperty", "get", "set", "length", "clone", "create", "substringData", "offset", "count", "Error", "debugInfo", "appendData", "arg", "insertData", "deleteData", "replaceData", "isEqualNode", "node", "apply", "arguments", "data"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLCharacterData.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCharacterData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLCharacterData = (function(superClass) {\n    extend(XMLCharacterData, superClass);\n\n    function XMLCharacterData(parent) {\n      XMLCharacterData.__super__.constructor.call(this, parent);\n      this.value = '';\n    }\n\n    Object.defineProperty(XMLCharacterData.prototype, 'data', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'length', {\n      get: function() {\n        return this.value.length;\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    XMLCharacterData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCharacterData.prototype.substringData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.appendData = function(arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.insertData = function(offset, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.deleteData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.replaceData = function(offset, count, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.isEqualNode = function(node) {\n      if (!XMLCharacterData.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.data !== this.data) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLCharacterData;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,gBAAgB;IAAEC,OAAO;IAC3BC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,OAAO,GAAGY,OAAO,CAAC,WAAW,CAAC;EAE9BC,MAAM,CAACC,OAAO,GAAGf,gBAAgB,GAAI,UAASgB,UAAU,EAAE;IACxDd,MAAM,CAACF,gBAAgB,EAAEgB,UAAU,CAAC;IAEpC,SAAShB,gBAAgBA,CAACI,MAAM,EAAE;MAChCJ,gBAAgB,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACzD,IAAI,CAACa,KAAK,GAAG,EAAE;IACjB;IAEAC,MAAM,CAACC,cAAc,CAACnB,gBAAgB,CAACU,SAAS,EAAE,MAAM,EAAE;MACxDU,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACH,KAAK;MACnB,CAAC;MACDI,GAAG,EAAE,SAAAA,CAASJ,KAAK,EAAE;QACnB,OAAO,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI,EAAE;MACjC;IACF,CAAC,CAAC;IAEFC,MAAM,CAACC,cAAc,CAACnB,gBAAgB,CAACU,SAAS,EAAE,QAAQ,EAAE;MAC1DU,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACH,KAAK,CAACK,MAAM;MAC1B;IACF,CAAC,CAAC;IAEFJ,MAAM,CAACC,cAAc,CAACnB,gBAAgB,CAACU,SAAS,EAAE,aAAa,EAAE;MAC/DU,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACH,KAAK;MACnB,CAAC;MACDI,GAAG,EAAE,SAAAA,CAASJ,KAAK,EAAE;QACnB,OAAO,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI,EAAE;MACjC;IACF,CAAC,CAAC;IAEFjB,gBAAgB,CAACU,SAAS,CAACa,KAAK,GAAG,YAAW;MAC5C,OAAOL,MAAM,CAACM,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAEDxB,gBAAgB,CAACU,SAAS,CAACe,aAAa,GAAG,UAASC,MAAM,EAAEC,KAAK,EAAE;MACjE,MAAM,IAAIC,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7B,gBAAgB,CAACU,SAAS,CAACoB,UAAU,GAAG,UAASC,GAAG,EAAE;MACpD,MAAM,IAAIH,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7B,gBAAgB,CAACU,SAAS,CAACsB,UAAU,GAAG,UAASN,MAAM,EAAEK,GAAG,EAAE;MAC5D,MAAM,IAAIH,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7B,gBAAgB,CAACU,SAAS,CAACuB,UAAU,GAAG,UAASP,MAAM,EAAEC,KAAK,EAAE;MAC9D,MAAM,IAAIC,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7B,gBAAgB,CAACU,SAAS,CAACwB,WAAW,GAAG,UAASR,MAAM,EAAEC,KAAK,EAAEI,GAAG,EAAE;MACpE,MAAM,IAAIH,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7B,gBAAgB,CAACU,SAAS,CAACyB,WAAW,GAAG,UAASC,IAAI,EAAE;MACtD,IAAI,CAACpC,gBAAgB,CAACW,SAAS,CAACwB,WAAW,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACH,WAAW,CAACC,IAAI,CAAC,EAAE;QACpF,OAAO,KAAK;MACd;MACA,IAAIA,IAAI,CAACG,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;QAC3B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IAED,OAAOvC,gBAAgB;EAEzB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}