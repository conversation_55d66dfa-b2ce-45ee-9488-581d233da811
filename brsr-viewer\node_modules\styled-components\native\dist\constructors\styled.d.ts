import * as React from 'react';
import { BaseObject, KnownTarget, WebTarget } from '../types';
import { Styled as StyledInstance } from './constructWithOptions';
declare const baseStyled: <Target extends WebTarget, InjectedProps extends object = BaseObject>(tag: Target) => StyledInstance<"web", Target, Target extends KnownTarget ? React.ComponentPropsWithRef<Target> & InjectedProps : InjectedProps, BaseObject>;
declare const styled: (<Target extends WebTarget, InjectedProps extends object = BaseObject>(tag: Target) => StyledInstance<"web", Target, Target extends KnownTarget ? React.ComponentPropsWithRef<Target> & InjectedProps : InjectedProps, BaseObject>) & {
    object: StyledInstance<"web", "object", React.DetailedHTMLProps<React.ObjectHTMLAttributes<HTMLObjectElement>, HTMLObjectElement>, BaseObject>;
    g: StyledInstance<"web", "g", React.SVGProps<SVGGElement>, BaseObject>;
    map: StyledInstance<"web", "map", React.DetailedHTMLProps<React.MapHTMLAttributes<HTMLMapElement>, HTMLMapElement>, BaseObject>;
    big: StyledInstance<"web", "big", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    link: StyledInstance<"web", "link", React.DetailedHTMLProps<React.LinkHTMLAttributes<HTMLLinkElement>, HTMLLinkElement>, BaseObject>;
    small: StyledInstance<"web", "small", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    sub: StyledInstance<"web", "sub", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    sup: StyledInstance<"web", "sup", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    a: StyledInstance<"web", "a", React.DetailedHTMLProps<React.AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, BaseObject>;
    abbr: StyledInstance<"web", "abbr", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    address: StyledInstance<"web", "address", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    area: StyledInstance<"web", "area", React.DetailedHTMLProps<React.AreaHTMLAttributes<HTMLAreaElement>, HTMLAreaElement>, BaseObject>;
    article: StyledInstance<"web", "article", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    aside: StyledInstance<"web", "aside", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    audio: StyledInstance<"web", "audio", React.DetailedHTMLProps<React.AudioHTMLAttributes<HTMLAudioElement>, HTMLAudioElement>, BaseObject>;
    b: StyledInstance<"web", "b", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    base: StyledInstance<"web", "base", React.DetailedHTMLProps<React.BaseHTMLAttributes<HTMLBaseElement>, HTMLBaseElement>, BaseObject>;
    bdi: StyledInstance<"web", "bdi", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    bdo: StyledInstance<"web", "bdo", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    blockquote: StyledInstance<"web", "blockquote", React.DetailedHTMLProps<React.BlockquoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, BaseObject>;
    body: StyledInstance<"web", "body", React.DetailedHTMLProps<React.HTMLAttributes<HTMLBodyElement>, HTMLBodyElement>, BaseObject>;
    br: StyledInstance<"web", "br", React.DetailedHTMLProps<React.HTMLAttributes<HTMLBRElement>, HTMLBRElement>, BaseObject>;
    button: StyledInstance<"web", "button", React.DetailedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, BaseObject>;
    canvas: StyledInstance<"web", "canvas", React.DetailedHTMLProps<React.CanvasHTMLAttributes<HTMLCanvasElement>, HTMLCanvasElement>, BaseObject>;
    caption: StyledInstance<"web", "caption", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    cite: StyledInstance<"web", "cite", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    code: StyledInstance<"web", "code", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    col: StyledInstance<"web", "col", React.DetailedHTMLProps<React.ColHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, BaseObject>;
    colgroup: StyledInstance<"web", "colgroup", React.DetailedHTMLProps<React.ColgroupHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, BaseObject>;
    data: StyledInstance<"web", "data", React.DetailedHTMLProps<React.DataHTMLAttributes<HTMLDataElement>, HTMLDataElement>, BaseObject>;
    datalist: StyledInstance<"web", "datalist", React.DetailedHTMLProps<React.HTMLAttributes<HTMLDataListElement>, HTMLDataListElement>, BaseObject>;
    dd: StyledInstance<"web", "dd", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    del: StyledInstance<"web", "del", React.DetailedHTMLProps<React.DelHTMLAttributes<HTMLModElement>, HTMLModElement>, BaseObject>;
    details: StyledInstance<"web", "details", React.DetailedHTMLProps<React.DetailsHTMLAttributes<HTMLDetailsElement>, HTMLDetailsElement>, BaseObject>;
    dfn: StyledInstance<"web", "dfn", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    dialog: StyledInstance<"web", "dialog", React.DetailedHTMLProps<React.DialogHTMLAttributes<HTMLDialogElement>, HTMLDialogElement>, BaseObject>;
    div: StyledInstance<"web", "div", React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, BaseObject>;
    dl: StyledInstance<"web", "dl", React.DetailedHTMLProps<React.HTMLAttributes<HTMLDListElement>, HTMLDListElement>, BaseObject>;
    dt: StyledInstance<"web", "dt", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    em: StyledInstance<"web", "em", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    embed: StyledInstance<"web", "embed", React.DetailedHTMLProps<React.EmbedHTMLAttributes<HTMLEmbedElement>, HTMLEmbedElement>, BaseObject>;
    fieldset: StyledInstance<"web", "fieldset", React.DetailedHTMLProps<React.FieldsetHTMLAttributes<HTMLFieldSetElement>, HTMLFieldSetElement>, BaseObject>;
    figcaption: StyledInstance<"web", "figcaption", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    figure: StyledInstance<"web", "figure", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    footer: StyledInstance<"web", "footer", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    form: StyledInstance<"web", "form", React.DetailedHTMLProps<React.FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>, BaseObject>;
    h1: StyledInstance<"web", "h1", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, BaseObject>;
    h2: StyledInstance<"web", "h2", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, BaseObject>;
    h3: StyledInstance<"web", "h3", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, BaseObject>;
    h4: StyledInstance<"web", "h4", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, BaseObject>;
    h5: StyledInstance<"web", "h5", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, BaseObject>;
    h6: StyledInstance<"web", "h6", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, BaseObject>;
    header: StyledInstance<"web", "header", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    hgroup: StyledInstance<"web", "hgroup", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    hr: StyledInstance<"web", "hr", React.DetailedHTMLProps<React.HTMLAttributes<HTMLHRElement>, HTMLHRElement>, BaseObject>;
    html: StyledInstance<"web", "html", React.DetailedHTMLProps<React.HtmlHTMLAttributes<HTMLHtmlElement>, HTMLHtmlElement>, BaseObject>;
    i: StyledInstance<"web", "i", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    iframe: StyledInstance<"web", "iframe", React.DetailedHTMLProps<React.IframeHTMLAttributes<HTMLIFrameElement>, HTMLIFrameElement>, BaseObject>;
    img: StyledInstance<"web", "img", React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, BaseObject>;
    input: StyledInstance<"web", "input", React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, BaseObject>;
    ins: StyledInstance<"web", "ins", React.DetailedHTMLProps<React.InsHTMLAttributes<HTMLModElement>, HTMLModElement>, BaseObject>;
    kbd: StyledInstance<"web", "kbd", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    keygen: StyledInstance<"web", "keygen", React.DetailedHTMLProps<React.KeygenHTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    label: StyledInstance<"web", "label", React.DetailedHTMLProps<React.LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, BaseObject>;
    legend: StyledInstance<"web", "legend", React.DetailedHTMLProps<React.HTMLAttributes<HTMLLegendElement>, HTMLLegendElement>, BaseObject>;
    li: StyledInstance<"web", "li", React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, BaseObject>;
    main: StyledInstance<"web", "main", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    mark: StyledInstance<"web", "mark", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    menu: StyledInstance<"web", "menu", React.DetailedHTMLProps<React.MenuHTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    menuitem: StyledInstance<"web", "menuitem", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    meta: StyledInstance<"web", "meta", React.DetailedHTMLProps<React.MetaHTMLAttributes<HTMLMetaElement>, HTMLMetaElement>, BaseObject>;
    meter: StyledInstance<"web", "meter", React.DetailedHTMLProps<React.MeterHTMLAttributes<HTMLMeterElement>, HTMLMeterElement>, BaseObject>;
    nav: StyledInstance<"web", "nav", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    noscript: StyledInstance<"web", "noscript", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    ol: StyledInstance<"web", "ol", React.DetailedHTMLProps<React.OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, BaseObject>;
    optgroup: StyledInstance<"web", "optgroup", React.DetailedHTMLProps<React.OptgroupHTMLAttributes<HTMLOptGroupElement>, HTMLOptGroupElement>, BaseObject>;
    option: StyledInstance<"web", "option", React.DetailedHTMLProps<React.OptionHTMLAttributes<HTMLOptionElement>, HTMLOptionElement>, BaseObject>;
    output: StyledInstance<"web", "output", React.DetailedHTMLProps<React.OutputHTMLAttributes<HTMLOutputElement>, HTMLOutputElement>, BaseObject>;
    p: StyledInstance<"web", "p", React.DetailedHTMLProps<React.HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>, BaseObject>;
    param: StyledInstance<"web", "param", React.DetailedHTMLProps<React.ParamHTMLAttributes<HTMLParamElement>, HTMLParamElement>, BaseObject>;
    picture: StyledInstance<"web", "picture", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    pre: StyledInstance<"web", "pre", React.DetailedHTMLProps<React.HTMLAttributes<HTMLPreElement>, HTMLPreElement>, BaseObject>;
    progress: StyledInstance<"web", "progress", React.DetailedHTMLProps<React.ProgressHTMLAttributes<HTMLProgressElement>, HTMLProgressElement>, BaseObject>;
    q: StyledInstance<"web", "q", React.DetailedHTMLProps<React.QuoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, BaseObject>;
    rp: StyledInstance<"web", "rp", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    rt: StyledInstance<"web", "rt", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    ruby: StyledInstance<"web", "ruby", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    s: StyledInstance<"web", "s", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    samp: StyledInstance<"web", "samp", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    script: StyledInstance<"web", "script", React.DetailedHTMLProps<React.ScriptHTMLAttributes<HTMLScriptElement>, HTMLScriptElement>, BaseObject>;
    section: StyledInstance<"web", "section", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    select: StyledInstance<"web", "select", React.DetailedHTMLProps<React.SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement>, BaseObject>;
    source: StyledInstance<"web", "source", React.DetailedHTMLProps<React.SourceHTMLAttributes<HTMLSourceElement>, HTMLSourceElement>, BaseObject>;
    span: StyledInstance<"web", "span", React.DetailedHTMLProps<React.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, BaseObject>;
    strong: StyledInstance<"web", "strong", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    style: StyledInstance<"web", "style", React.DetailedHTMLProps<React.StyleHTMLAttributes<HTMLStyleElement>, HTMLStyleElement>, BaseObject>;
    summary: StyledInstance<"web", "summary", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    table: StyledInstance<"web", "table", React.DetailedHTMLProps<React.TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, BaseObject>;
    tbody: StyledInstance<"web", "tbody", React.DetailedHTMLProps<React.HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, BaseObject>;
    td: StyledInstance<"web", "td", React.DetailedHTMLProps<React.TdHTMLAttributes<HTMLTableDataCellElement>, HTMLTableDataCellElement>, BaseObject>;
    textarea: StyledInstance<"web", "textarea", React.DetailedHTMLProps<React.TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, BaseObject>;
    tfoot: StyledInstance<"web", "tfoot", React.DetailedHTMLProps<React.HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, BaseObject>;
    th: StyledInstance<"web", "th", React.DetailedHTMLProps<React.ThHTMLAttributes<HTMLTableHeaderCellElement>, HTMLTableHeaderCellElement>, BaseObject>;
    thead: StyledInstance<"web", "thead", React.DetailedHTMLProps<React.HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, BaseObject>;
    time: StyledInstance<"web", "time", React.DetailedHTMLProps<React.TimeHTMLAttributes<HTMLTimeElement>, HTMLTimeElement>, BaseObject>;
    tr: StyledInstance<"web", "tr", React.DetailedHTMLProps<React.HTMLAttributes<HTMLTableRowElement>, HTMLTableRowElement>, BaseObject>;
    track: StyledInstance<"web", "track", React.DetailedHTMLProps<React.TrackHTMLAttributes<HTMLTrackElement>, HTMLTrackElement>, BaseObject>;
    u: StyledInstance<"web", "u", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    ul: StyledInstance<"web", "ul", React.DetailedHTMLProps<React.HTMLAttributes<HTMLUListElement>, HTMLUListElement>, BaseObject>;
    var: StyledInstance<"web", "var", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    video: StyledInstance<"web", "video", React.DetailedHTMLProps<React.VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>, BaseObject>;
    wbr: StyledInstance<"web", "wbr", React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, BaseObject>;
    svg: StyledInstance<"web", "svg", React.SVGProps<SVGSVGElement>, BaseObject>;
    circle: StyledInstance<"web", "circle", React.SVGProps<SVGCircleElement>, BaseObject>;
    clipPath: StyledInstance<"web", "clipPath", React.SVGProps<SVGClipPathElement>, BaseObject>;
    defs: StyledInstance<"web", "defs", React.SVGProps<SVGDefsElement>, BaseObject>;
    ellipse: StyledInstance<"web", "ellipse", React.SVGProps<SVGEllipseElement>, BaseObject>;
    foreignObject: StyledInstance<"web", "foreignObject", React.SVGProps<SVGForeignObjectElement>, BaseObject>;
    image: StyledInstance<"web", "image", React.SVGProps<SVGImageElement>, BaseObject>;
    line: StyledInstance<"web", "line", React.SVGLineElementAttributes<SVGLineElement>, BaseObject>;
    linearGradient: StyledInstance<"web", "linearGradient", React.SVGProps<SVGLinearGradientElement>, BaseObject>;
    marker: StyledInstance<"web", "marker", React.SVGProps<SVGMarkerElement>, BaseObject>;
    mask: StyledInstance<"web", "mask", React.SVGProps<SVGMaskElement>, BaseObject>;
    path: StyledInstance<"web", "path", React.SVGProps<SVGPathElement>, BaseObject>;
    pattern: StyledInstance<"web", "pattern", React.SVGProps<SVGPatternElement>, BaseObject>;
    polygon: StyledInstance<"web", "polygon", React.SVGProps<SVGPolygonElement>, BaseObject>;
    polyline: StyledInstance<"web", "polyline", React.SVGProps<SVGPolylineElement>, BaseObject>;
    radialGradient: StyledInstance<"web", "radialGradient", React.SVGProps<SVGRadialGradientElement>, BaseObject>;
    rect: StyledInstance<"web", "rect", React.SVGProps<SVGRectElement>, BaseObject>;
    stop: StyledInstance<"web", "stop", React.SVGProps<SVGStopElement>, BaseObject>;
    text: StyledInstance<"web", "text", React.SVGTextElementAttributes<SVGTextElement>, BaseObject>;
    tspan: StyledInstance<"web", "tspan", React.SVGProps<SVGTSpanElement>, BaseObject>;
    use: StyledInstance<"web", "use", React.SVGProps<SVGUseElement>, BaseObject>;
};
export default styled;
export { StyledInstance };
/**
 * This is the type of the `styled` HOC.
 */
export type Styled = typeof styled;
/**
 * Use this higher-order type for scenarios where you are wrapping `styled`
 * and providing extra props as a third-party library.
 */
export type LibraryStyled<LibraryProps extends object = BaseObject> = <Target extends WebTarget>(tag: Target) => typeof baseStyled<Target, LibraryProps>;
