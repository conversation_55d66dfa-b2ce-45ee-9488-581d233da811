{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\brsr_reports\\\\brsr-viewer\\\\src\\\\BRSRViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BRSRViewer() {\n  _s();\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({\n    Environment: [],\n    Social: [],\n    Governance: []\n  });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = e => {\n    console.log(\"File upload triggered\");\n    const file = e.target.files[0];\n    if (!file) {\n      console.log(\"No file selected\");\n      return;\n    }\n    console.log(\"File selected:\", file.name, file.type, file.size);\n    const reader = new FileReader();\n    reader.onload = evt => {\n      try {\n        console.log(\"File read successfully, parsing...\");\n        let json;\n        if (file.name.toLowerCase().endsWith('.csv')) {\n          // Handle CSV files\n          console.log(\"Processing CSV file\");\n          const csvText = evt.target.result;\n          const lines = csvText.split('\\n');\n          const headers = lines[0].split(',').map(h => h.replace(/\"/g, '').trim());\n          console.log(\"CSV headers:\", headers);\n          json = [];\n          for (let i = 1; i < lines.length; i++) {\n            if (lines[i].trim()) {\n              const values = lines[i].split(',').map(v => v.replace(/\"/g, '').trim());\n              const row = {};\n              headers.forEach((header, index) => {\n                row[header] = values[index] || '';\n              });\n              json.push(row);\n            }\n          }\n        } else {\n          // Handle Excel files\n          console.log(\"Processing Excel file\");\n          const workbook = XLSX.read(evt.target.result, {\n            type: \"binary\"\n          });\n          console.log(\"Workbook created, sheet names:\", workbook.SheetNames);\n          const sheetName = workbook.SheetNames[0];\n          const worksheet = workbook.Sheets[sheetName];\n          json = XLSX.utils.sheet_to_json(worksheet);\n        }\n        console.log(\"JSON data parsed:\", json.length, \"rows\");\n        console.log(\"Sample data:\", json.slice(0, 2));\n\n        // Log all column names to help debug\n        if (json.length > 0) {\n          console.log(\"Available columns:\", Object.keys(json[0]));\n        }\n        setCompanyData(json);\n      } catch (error) {\n        console.error(\"Error parsing file:\", error);\n        alert(\"Error parsing file: \" + error.message);\n      }\n    };\n    reader.onerror = error => {\n      console.error(\"Error reading file:\", error);\n      alert(\"Error reading file\");\n    };\n\n    // Use different reading methods for different file types\n    if (file.name.toLowerCase().endsWith('.csv')) {\n      reader.readAsText(file);\n    } else {\n      reader.readAsBinaryString(file);\n    }\n  };\n\n  /** Categorize field names **/\n  const categorizeField = name => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\")) return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\")) return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = xmlText => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach(ctx => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\"\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach(u => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = {\n      Environment: [],\n      Social: [],\n      Governance: []\n    };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach(element => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: (context === null || context === void 0 ? void 0 : context.entity) || \"\"\n        });\n      }\n    });\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async row => {\n    console.log(\"Company selected:\", row);\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({\n      Environment: [],\n      Social: [],\n      Governance: []\n    });\n    const xmlLink = getColumnValue(row, [\"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n    if (xmlLink) {\n      try {\n        console.log(\"Fetching XML from:\", xmlLink);\n        const res = await axios.get(xmlLink);\n        console.log(\"XML response received, length:\", res.data.length);\n        const categories = parseBRSRXML(res.data);\n        console.log(\"Parsed categories:\", categories);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n        alert(\"Error loading XML: \" + err.message);\n      } finally {\n        setLoadingSingle(false);\n      }\n    } else {\n      console.log(\"No XML Link found for this company\");\n      setLoadingSingle(false);\n      alert(\"No XML Link found for this company\");\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach(cat => {\n            categories[cat].forEach(d => allData.push({\n              ...d,\n              category: cat,\n              companyName: row[\"Company Name\"],\n              year: row[\"Year\"]\n            }));\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Helper function to get column value with flexible naming **/\n  const getColumnValue = (row, possibleNames) => {\n    for (const name of possibleNames) {\n      if (row[name] !== undefined && row[name] !== null && row[name] !== '') {\n        return row[name];\n      }\n    }\n    return '';\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(row => row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) || row.value.toLowerCase().includes(searchBRSR.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        fontSize: '2rem',\n        fontWeight: 'bold',\n        color: '#333',\n        marginBottom: '24px',\n        textAlign: 'center'\n      },\n      children: \"\\uD83D\\uDCCA BRSR Report Viewer (Excel + XML)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".xlsx,.xls,.csv\",\n        onChange: handleExcelUpload,\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), companyData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#28a745',\n            fontWeight: '500'\n          },\n          children: [\"\\u2705 \", companyData.length, \" companies loaded\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#6c757d',\n            marginTop: '4px'\n          },\n          children: [\"Available columns: \", Object.keys(companyData[0]).join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            // Add sample data for testing\n            const sampleData = [{\n              \"Company Name\": \"Sample Corp Ltd\",\n              \"Year\": \"2023\",\n              \"XML Link\": \"https://httpbin.org/xml\" // This returns sample XML for testing\n            }, {\n              \"Company Name\": \"Test Industries\",\n              \"Year\": \"2023\",\n              \"XML Link\": \"https://httpbin.org/xml\"\n            }];\n            setCompanyData(sampleData);\n            console.log(\"Sample data loaded:\", sampleData);\n          },\n          style: {\n            padding: '8px 16px',\n            backgroundColor: '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          },\n          children: \"Load Sample Data (for testing)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '16px',\n        marginBottom: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Search company...\",\n        value: searchCompany,\n        onChange: e => setSearchCompany(e.target.value),\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchAllCompanies,\n        disabled: loadingAll || companyData.length === 0,\n        style: {\n          padding: '12px 16px',\n          borderRadius: '8px',\n          border: 'none',\n          color: 'white',\n          backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n          cursor: loadingAll ? 'not-allowed' : 'pointer',\n          opacity: loadingAll ? 0.6 : 1\n        },\n        children: loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), companyData.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '16px',\n        marginBottom: '32px'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataTable, {\n        title: `Companies (${companyData.length} loaded)`,\n        columns: [{\n          name: \"Company\",\n          selector: row => getColumnValue(row, [\"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]),\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => getColumnValue(row, [\"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"]),\n          sortable: true\n        }, {\n          name: \"XML Link\",\n          selector: row => getColumnValue(row, [\"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]),\n          sortable: true,\n          cell: row => {\n            const link = getColumnValue(row, [\"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n            return link ? /*#__PURE__*/_jsxDEV(\"a\", {\n              href: link,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                color: '#007bff',\n                fontSize: '12px'\n              },\n              children: link.length > 30 ? link.substring(0, 30) + '...' : link\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 21\n            }, this) : 'No Link';\n          }\n        }, {\n          name: \"Actions\",\n          cell: row => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCompanySelect(row),\n            style: {\n              backgroundColor: '#007bff',\n              color: 'white',\n              padding: '6px 12px',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            },\n            children: \"View BRSR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 19\n          }, this)\n        }],\n        data: companyData.filter(r => {\n          const companyName = getColumnValue(r, [\"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]);\n          return companyName.toLowerCase().includes(searchCompany.toLowerCase());\n        }),\n        pagination: true,\n        highlightOnHover: true,\n        dense: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '32px',\n        marginBottom: '32px',\n        textAlign: 'center',\n        color: '#6c757d'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\uD83D\\uDCC1 No companies loaded yet. Please upload an Excel file to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '14px',\n          marginTop: '8px'\n        },\n        children: \"Expected columns: Company Name, Year, XML Link\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 9\n    }, this), unifiedData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px',\n        marginBottom: '32px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: \"Unified BRSR Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Company\",\n          selector: row => row.companyName,\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => row.year,\n          sortable: true\n        }, {\n          name: \"Category\",\n          selector: row => row.category,\n          sortable: true\n        }, {\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }],\n        data: unifiedData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 9\n    }, this), selectedCompany && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: [getColumnValue(selectedCompany, [\"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]), \"(\", getColumnValue(selectedCompany, [\"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"]), \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '16px',\n          marginBottom: '16px'\n        },\n        children: [\"Environment\", \"Social\", \"Governance\"].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab),\n          style: {\n            padding: '8px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            fontWeight: '500',\n            cursor: 'pointer',\n            backgroundColor: activeTab === tab ? tab === \"Environment\" ? \"#28a745\" : tab === \"Social\" ? \"#6f42c1\" : \"#fd7e14\" : \"#e9ecef\",\n            color: activeTab === tab ? \"white\" : \"#495057\"\n          },\n          children: tab\n        }, tab, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: `Search in ${activeTab}...`,\n          value: searchBRSR,\n          onChange: e => setSearchBRSR(e.target.value),\n          style: {\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 11\n      }, this), loadingSingle ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '40px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2rem',\n            color: '#007bff'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }, {\n          name: \"Unit\",\n          selector: row => row.unit || \"-\",\n          sortable: true\n        }, {\n          name: \"Period\",\n          selector: row => row.period,\n          sortable: true\n        }],\n        data: filteredTabData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n}\n_s(BRSRViewer, \"XqWeNAlGtEHiedqF0JxGSKVABwY=\");\n_c = BRSRViewer;\nvar _c;\n$RefreshReg$(_c, \"BRSRViewer\");", "map": {"version": 3, "names": ["React", "useState", "XLSX", "axios", "DataTable", "jsxDEV", "_jsxDEV", "BRSRViewer", "_s", "companyData", "setCompanyData", "searchCompany", "setSearchCompany", "selectedCompany", "setSelectedCompany", "brsrData", "setBrsrData", "Environment", "Social", "Governance", "searchBRSR", "setSearchBRSR", "activeTab", "setActiveTab", "unifiedData", "setUnifiedData", "loadingAll", "setLoadingAll", "loadingSingle", "setLoading<PERSON>ingle", "handleExcelUpload", "e", "console", "log", "file", "target", "files", "name", "type", "size", "reader", "FileReader", "onload", "evt", "json", "toLowerCase", "endsWith", "csvText", "result", "lines", "split", "headers", "map", "h", "replace", "trim", "i", "length", "values", "v", "row", "for<PERSON>ach", "header", "index", "push", "workbook", "read", "SheetNames", "sheetName", "worksheet", "Sheets", "utils", "sheet_to_json", "slice", "Object", "keys", "error", "alert", "message", "onerror", "readAsText", "readAsBinaryString", "categorizeField", "lower", "includes", "parseBRSRXML", "xmlText", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "contexts", "units", "contextElements", "getElementsByTagName", "Array", "from", "ctx", "id", "getAttribute", "entityElement", "startDateElement", "endDateElement", "entity", "textContent", "startDate", "endDate", "unitElements", "u", "measureElement", "categories", "allElements", "element", "tagName", "startsWith", "contextRef", "unitRef", "context", "unit", "category", "metric", "value", "period", "company", "handleCompanySelect", "xmlLink", "getColumnValue", "res", "get", "data", "err", "fetchAllCompanies", "allData", "cat", "d", "companyName", "year", "warn", "possibleNames", "undefined", "filteredTabData", "filter", "style", "padding", "backgroundColor", "minHeight", "children", "fontSize", "fontWeight", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "alignItems", "accept", "onChange", "width", "border", "borderRadius", "boxShadow", "marginTop", "join", "onClick", "sampleData", "cursor", "justifyContent", "gap", "placeholder", "disabled", "opacity", "title", "columns", "selector", "sortable", "cell", "link", "href", "rel", "substring", "r", "pagination", "highlightOnHover", "dense", "tab", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/src/BRSRViewer.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\n\nexport default function BRSRViewer() {\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = (e) => {\n    console.log(\"File upload triggered\");\n    const file = e.target.files[0];\n    if (!file) {\n      console.log(\"No file selected\");\n      return;\n    }\n\n    console.log(\"File selected:\", file.name, file.type, file.size);\n\n    const reader = new FileReader();\n    reader.onload = (evt) => {\n      try {\n        console.log(\"File read successfully, parsing...\");\n        let json;\n\n        if (file.name.toLowerCase().endsWith('.csv')) {\n          // Handle CSV files\n          console.log(\"Processing CSV file\");\n          const csvText = evt.target.result;\n          const lines = csvText.split('\\n');\n          const headers = lines[0].split(',').map(h => h.replace(/\"/g, '').trim());\n          console.log(\"CSV headers:\", headers);\n\n          json = [];\n          for (let i = 1; i < lines.length; i++) {\n            if (lines[i].trim()) {\n              const values = lines[i].split(',').map(v => v.replace(/\"/g, '').trim());\n              const row = {};\n              headers.forEach((header, index) => {\n                row[header] = values[index] || '';\n              });\n              json.push(row);\n            }\n          }\n        } else {\n          // Handle Excel files\n          console.log(\"Processing Excel file\");\n          const workbook = XLSX.read(evt.target.result, { type: \"binary\" });\n          console.log(\"Workbook created, sheet names:\", workbook.SheetNames);\n\n          const sheetName = workbook.SheetNames[0];\n          const worksheet = workbook.Sheets[sheetName];\n          json = XLSX.utils.sheet_to_json(worksheet);\n        }\n\n        console.log(\"JSON data parsed:\", json.length, \"rows\");\n        console.log(\"Sample data:\", json.slice(0, 2));\n\n        // Log all column names to help debug\n        if (json.length > 0) {\n          console.log(\"Available columns:\", Object.keys(json[0]));\n        }\n\n        setCompanyData(json);\n      } catch (error) {\n        console.error(\"Error parsing file:\", error);\n        alert(\"Error parsing file: \" + error.message);\n      }\n    };\n\n    reader.onerror = (error) => {\n      console.error(\"Error reading file:\", error);\n      alert(\"Error reading file\");\n    };\n\n    // Use different reading methods for different file types\n    if (file.name.toLowerCase().endsWith('.csv')) {\n      reader.readAsText(file);\n    } else {\n      reader.readAsBinaryString(file);\n    }\n  };\n\n  /** Categorize field names **/\n  const categorizeField = (name) => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\"))\n      return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\"))\n      return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = (xmlText) => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach((ctx) => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\",\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach((u) => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = { Environment: [], Social: [], Governance: [] };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach((element) => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: context?.entity || \"\",\n        });\n      }\n    });\n\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async (row) => {\n    console.log(\"Company selected:\", row);\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({ Environment: [], Social: [], Governance: [] });\n\n    const xmlLink = getColumnValue(row, [\"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n\n    if (xmlLink) {\n      try {\n        console.log(\"Fetching XML from:\", xmlLink);\n        const res = await axios.get(xmlLink);\n        console.log(\"XML response received, length:\", res.data.length);\n\n        const categories = parseBRSRXML(res.data);\n        console.log(\"Parsed categories:\", categories);\n\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n        alert(\"Error loading XML: \" + err.message);\n      } finally {\n        setLoadingSingle(false);\n      }\n    } else {\n      console.log(\"No XML Link found for this company\");\n      setLoadingSingle(false);\n      alert(\"No XML Link found for this company\");\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach((cat) => {\n            categories[cat].forEach((d) =>\n              allData.push({\n                ...d,\n                category: cat,\n                companyName: row[\"Company Name\"],\n                year: row[\"Year\"],\n              })\n            );\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Helper function to get column value with flexible naming **/\n  const getColumnValue = (row, possibleNames) => {\n    for (const name of possibleNames) {\n      if (row[name] !== undefined && row[name] !== null && row[name] !== '') {\n        return row[name];\n      }\n    }\n    return '';\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(\n    (row) =>\n      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||\n      row.value.toLowerCase().includes(searchBRSR.toLowerCase())\n  );\n\n  return (\n    <div style={{ padding: '24px', backgroundColor: '#f8f9fa', minHeight: '100vh' }}>\n      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333', marginBottom: '24px', textAlign: 'center' }}>\n        📊 BRSR Report Viewer (Excel + XML)\n      </h1>\n\n      {/* Upload Excel */}\n      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '24px' }}>\n        <input\n          type=\"file\"\n          accept=\".xlsx,.xls,.csv\"\n          onChange={handleExcelUpload}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        {companyData.length > 0 && (\n          <div style={{ marginTop: '8px' }}>\n            <div style={{ color: '#28a745', fontWeight: '500' }}>\n              ✅ {companyData.length} companies loaded\n            </div>\n            <div style={{ fontSize: '12px', color: '#6c757d', marginTop: '4px' }}>\n              Available columns: {Object.keys(companyData[0]).join(', ')}\n            </div>\n          </div>\n        )}\n        <div style={{ marginTop: '8px' }}>\n          <button\n            onClick={() => {\n              // Add sample data for testing\n              const sampleData = [\n                {\n                  \"Company Name\": \"Sample Corp Ltd\",\n                  \"Year\": \"2023\",\n                  \"XML Link\": \"https://httpbin.org/xml\" // This returns sample XML for testing\n                },\n                {\n                  \"Company Name\": \"Test Industries\",\n                  \"Year\": \"2023\",\n                  \"XML Link\": \"https://httpbin.org/xml\"\n                }\n              ];\n              setCompanyData(sampleData);\n              console.log(\"Sample data loaded:\", sampleData);\n            }}\n            style={{\n              padding: '8px 16px',\n              backgroundColor: '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            }}\n          >\n            Load Sample Data (for testing)\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Fetch All */}\n      <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', marginBottom: '16px' }}>\n        <input\n          type=\"text\"\n          placeholder=\"Search company...\"\n          value={searchCompany}\n          onChange={(e) => setSearchCompany(e.target.value)}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        <button\n          onClick={fetchAllCompanies}\n          disabled={loadingAll || companyData.length === 0}\n          style={{\n            padding: '12px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            color: 'white',\n            backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n            cursor: loadingAll ? 'not-allowed' : 'pointer',\n            opacity: loadingAll ? 0.6 : 1\n          }}\n        >\n          {loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"}\n        </button>\n      </div>\n\n      {/* Company Table */}\n      {companyData.length > 0 ? (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '16px',\n          marginBottom: '32px'\n        }}>\n          <DataTable\n            title={`Companies (${companyData.length} loaded)`}\n            columns={[\n              {\n                name: \"Company\",\n                selector: (row) => getColumnValue(row, [\"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]),\n                sortable: true\n              },\n              {\n                name: \"Year\",\n                selector: (row) => getColumnValue(row, [\"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"]),\n                sortable: true\n              },\n              {\n                name: \"XML Link\",\n                selector: (row) => getColumnValue(row, [\"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]),\n                sortable: true,\n                cell: (row) => {\n                  const link = getColumnValue(row, [\"XML Link\", \"XMLLink\", \"xml_link\", \"Link\", \"URL\", \"url\"]);\n                  return link ? (\n                    <a href={link} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#007bff', fontSize: '12px'}}>\n                      {link.length > 30 ? link.substring(0, 30) + '...' : link}\n                    </a>\n                  ) : 'No Link';\n                }\n              },\n              {\n                name: \"Actions\",\n                cell: (row) => (\n                  <button\n                    onClick={() => handleCompanySelect(row)}\n                    style={{\n                      backgroundColor: '#007bff',\n                      color: 'white',\n                      padding: '6px 12px',\n                      border: 'none',\n                      borderRadius: '4px',\n                      cursor: 'pointer',\n                      fontSize: '14px'\n                    }}\n                  >\n                    View BRSR\n                  </button>\n                ),\n              },\n            ]}\n            data={companyData.filter((r) => {\n              const companyName = getColumnValue(r, [\"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"]);\n              return companyName.toLowerCase().includes(searchCompany.toLowerCase());\n            })}\n            pagination\n            highlightOnHover\n            dense\n          />\n        </div>\n      ) : (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '32px',\n          marginBottom: '32px',\n          textAlign: 'center',\n          color: '#6c757d'\n        }}>\n          <p>📁 No companies loaded yet. Please upload an Excel file to get started.</p>\n          <p style={{ fontSize: '14px', marginTop: '8px' }}>\n            Expected columns: Company Name, Year, XML Link\n          </p>\n        </div>\n      )}\n\n      {/* Unified Data Table */}\n      {unifiedData.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px',\n          marginBottom: '32px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>Unified BRSR Data</h2>\n          <DataTable\n            columns={[\n              { name: \"Company\", selector: (row) => row.companyName, sortable: true },\n              { name: \"Year\", selector: (row) => row.year, sortable: true },\n              { name: \"Category\", selector: (row) => row.category, sortable: true },\n              { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n              { name: \"Value\", selector: (row) => row.value, sortable: true },\n            ]}\n            data={unifiedData}\n            pagination\n            dense\n            highlightOnHover\n          />\n        </div>\n      )}\n\n      {/* Tabs for single company */}\n      {selectedCompany && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>\n            {getColumnValue(selectedCompany, [\"Company Name\", \"Company\", \"CompanyName\", \"Name\", \"company_name\", \"company\"])}\n            ({getColumnValue(selectedCompany, [\"Year\", \"year\", \"YEAR\", \"FY\", \"fy\"])})\n          </h2>\n\n          {/* Tabs */}\n          <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>\n            {[\"Environment\", \"Social\", \"Governance\"].map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                style={{\n                  padding: '8px 16px',\n                  borderRadius: '8px',\n                  border: 'none',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  backgroundColor: activeTab === tab\n                    ? tab === \"Environment\"\n                      ? \"#28a745\"\n                      : tab === \"Social\"\n                      ? \"#6f42c1\"\n                      : \"#fd7e14\"\n                    : \"#e9ecef\",\n                  color: activeTab === tab ? \"white\" : \"#495057\"\n                }}\n              >\n                {tab}\n              </button>\n            ))}\n          </div>\n\n          {/* Search */}\n          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>\n            <input\n              type=\"text\"\n              placeholder={`Search in ${activeTab}...`}\n              value={searchBRSR}\n              onChange={(e) => setSearchBRSR(e.target.value)}\n              style={{\n                width: '320px',\n                padding: '12px',\n                border: '1px solid #ddd',\n                borderRadius: '8px',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }}\n            />\n          </div>\n\n          {/* Table */}\n          {loadingSingle ? (\n            <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>\n              <span style={{ fontSize: '2rem', color: '#007bff' }}>⏳</span>\n            </div>\n          ) : (\n            <DataTable\n              columns={[\n                { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n                { name: \"Value\", selector: (row) => row.value, sortable: true },\n                { name: \"Unit\", selector: (row) => row.unit || \"-\", sortable: true },\n                { name: \"Period\", selector: (row) => row.period, sortable: true },\n              ]}\n              data={filteredTabData}\n              pagination\n              dense\n              highlightOnHover\n            />\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IAAEgB,WAAW,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC;EACzF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC/BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,MAAMC,IAAI,GAAGH,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;MACTF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,IAAI,CAACG,IAAI,EAAEH,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,IAAI,CAAC;IAE9D,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,GAAG,IAAK;MACvB,IAAI;QACFX,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,IAAIW,IAAI;QAER,IAAIV,IAAI,CAACG,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC5C;UACAd,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAClC,MAAMc,OAAO,GAAGJ,GAAG,CAACR,MAAM,CAACa,MAAM;UACjC,MAAMC,KAAK,GAAGF,OAAO,CAACG,KAAK,CAAC,IAAI,CAAC;UACjC,MAAMC,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;UACxEvB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkB,OAAO,CAAC;UAEpCP,IAAI,GAAG,EAAE;UACT,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;YACrC,IAAIP,KAAK,CAACO,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,EAAE;cACnB,MAAMG,MAAM,GAAGT,KAAK,CAACO,CAAC,CAAC,CAACN,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACL,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;cACvE,MAAMK,GAAG,GAAG,CAAC,CAAC;cACdT,OAAO,CAACU,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;gBACjCH,GAAG,CAACE,MAAM,CAAC,GAAGJ,MAAM,CAACK,KAAK,CAAC,IAAI,EAAE;cACnC,CAAC,CAAC;cACFnB,IAAI,CAACoB,IAAI,CAACJ,GAAG,CAAC;YAChB;UACF;QACF,CAAC,MAAM;UACL;UACA5B,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UACpC,MAAMgC,QAAQ,GAAG/D,IAAI,CAACgE,IAAI,CAACvB,GAAG,CAACR,MAAM,CAACa,MAAM,EAAE;YAAEV,IAAI,EAAE;UAAS,CAAC,CAAC;UACjEN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgC,QAAQ,CAACE,UAAU,CAAC;UAElE,MAAMC,SAAS,GAAGH,QAAQ,CAACE,UAAU,CAAC,CAAC,CAAC;UACxC,MAAME,SAAS,GAAGJ,QAAQ,CAACK,MAAM,CAACF,SAAS,CAAC;UAC5CxB,IAAI,GAAG1C,IAAI,CAACqE,KAAK,CAACC,aAAa,CAACH,SAAS,CAAC;QAC5C;QAEArC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEW,IAAI,CAACa,MAAM,EAAE,MAAM,CAAC;QACrDzB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEW,IAAI,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE7C;QACA,IAAI7B,IAAI,CAACa,MAAM,GAAG,CAAC,EAAE;UACnBzB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyC,MAAM,CAACC,IAAI,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;QAEAlC,cAAc,CAACkC,IAAI,CAAC;MACtB,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACd5C,OAAO,CAAC4C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CC,KAAK,CAAC,sBAAsB,GAAGD,KAAK,CAACE,OAAO,CAAC;MAC/C;IACF,CAAC;IAEDtC,MAAM,CAACuC,OAAO,GAAIH,KAAK,IAAK;MAC1B5C,OAAO,CAAC4C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,KAAK,CAAC,oBAAoB,CAAC;IAC7B,CAAC;;IAED;IACA,IAAI3C,IAAI,CAACG,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC5CN,MAAM,CAACwC,UAAU,CAAC9C,IAAI,CAAC;IACzB,CAAC,MAAM;MACLM,MAAM,CAACyC,kBAAkB,CAAC/C,IAAI,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMgD,eAAe,GAAI7C,IAAI,IAAK;IAChC,MAAM8C,KAAK,GAAG9C,IAAI,CAACQ,WAAW,CAAC,CAAC;IAChC,IAAIsC,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,EAC9G,OAAO,aAAa;IACtB,IAAID,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC,KAAK,CAAC,EAChH,OAAO,QAAQ;IACjB,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;IAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,OAAO,EAAE,UAAU,CAAC;IAC1D,MAAMK,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,MAAMC,eAAe,GAAGJ,MAAM,CAACK,oBAAoB,CAAC,eAAe,CAAC;IACpEC,KAAK,CAACC,IAAI,CAACH,eAAe,CAAC,CAAChC,OAAO,CAAEoC,GAAG,IAAK;MAC3C,MAAMC,EAAE,GAAGD,GAAG,CAACE,YAAY,CAAC,IAAI,CAAC;MACjC,MAAMC,aAAa,GAAGH,GAAG,CAACH,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACrE,MAAMO,gBAAgB,GAAGJ,GAAG,CAACH,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMQ,cAAc,GAAGL,GAAG,CAACH,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MAEnEH,QAAQ,CAACO,EAAE,CAAC,GAAG;QACbK,MAAM,EAAEH,aAAa,GAAGA,aAAa,CAACI,WAAW,GAAG,EAAE;QACtDC,SAAS,EAAEJ,gBAAgB,GAAGA,gBAAgB,CAACG,WAAW,GAAG,EAAE;QAC/DE,OAAO,EAAEJ,cAAc,GAAGA,cAAc,CAACE,WAAW,GAAG;MACzD,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMG,YAAY,GAAGlB,MAAM,CAACK,oBAAoB,CAAC,YAAY,CAAC;IAC9DC,KAAK,CAACC,IAAI,CAACW,YAAY,CAAC,CAAC9C,OAAO,CAAE+C,CAAC,IAAK;MACtC,MAAMV,EAAE,GAAGU,CAAC,CAACT,YAAY,CAAC,IAAI,CAAC;MAC/B,MAAMU,cAAc,GAAGD,CAAC,CAACd,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACjEF,KAAK,CAACM,EAAE,CAAC,GAAGW,cAAc,GAAGA,cAAc,CAACL,WAAW,GAAG,EAAE;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,UAAU,GAAG;MAAE7F,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;;IAElE;IACA,MAAM4F,WAAW,GAAGtB,MAAM,CAACK,oBAAoB,CAAC,GAAG,CAAC;IACpDC,KAAK,CAACC,IAAI,CAACe,WAAW,CAAC,CAAClD,OAAO,CAAEmD,OAAO,IAAK;MAC3C,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO;MAC/B,IAAIA,OAAO,IAAIA,OAAO,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC/C,MAAMC,UAAU,GAAGH,OAAO,CAACb,YAAY,CAAC,YAAY,CAAC;QACrD,MAAMiB,OAAO,GAAGJ,OAAO,CAACb,YAAY,CAAC,SAAS,CAAC;QAC/C,MAAMkB,OAAO,GAAG1B,QAAQ,CAACwB,UAAU,CAAC;QACpC,MAAMG,IAAI,GAAG1B,KAAK,CAACwB,OAAO,CAAC;QAC3B,MAAMG,QAAQ,GAAGrC,eAAe,CAAC+B,OAAO,CAAC;QAEzCH,UAAU,CAACS,QAAQ,CAAC,CAACvD,IAAI,CAAC;UACxBwD,MAAM,EAAEP,OAAO,CAAC3D,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;UACzCmE,KAAK,EAAET,OAAO,CAACR,WAAW,IAAI,EAAE;UAChCc,IAAI,EAAEA,IAAI,IAAI,IAAI;UAClBI,MAAM,EAAEL,OAAO,GAAG,GAAGA,OAAO,CAACZ,SAAS,MAAMY,OAAO,CAACX,OAAO,EAAE,GAAG,EAAE;UAClEiB,OAAO,EAAE,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEd,MAAM,KAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAG,MAAOhE,GAAG,IAAK;IACzC5B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2B,GAAG,CAAC;IACrC9C,kBAAkB,CAAC8C,GAAG,CAAC;IACvB/B,gBAAgB,CAAC,IAAI,CAAC;IACtBb,WAAW,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC,CAAC;IAE5D,MAAM0G,OAAO,GAAGC,cAAc,CAAClE,GAAG,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAE9F,IAAIiE,OAAO,EAAE;MACX,IAAI;QACF7F,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4F,OAAO,CAAC;QAC1C,MAAME,GAAG,GAAG,MAAM5H,KAAK,CAAC6H,GAAG,CAACH,OAAO,CAAC;QACpC7F,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8F,GAAG,CAACE,IAAI,CAACxE,MAAM,CAAC;QAE9D,MAAMqD,UAAU,GAAGzB,YAAY,CAAC0C,GAAG,CAACE,IAAI,CAAC;QACzCjG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6E,UAAU,CAAC;QAE7C9F,WAAW,CAAC8F,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZlG,OAAO,CAAC4C,KAAK,CAAC,oBAAoB,EAAEsD,GAAG,CAAC;QACxCrD,KAAK,CAAC,qBAAqB,GAAGqD,GAAG,CAACpD,OAAO,CAAC;MAC5C,CAAC,SAAS;QACRjD,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDJ,gBAAgB,CAAC,KAAK,CAAC;MACvBgD,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMsD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCxG,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMyG,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMxE,GAAG,IAAInD,WAAW,EAAE;MAC7B,IAAImD,GAAG,CAAC,UAAU,CAAC,EAAE;QACnB,IAAI;UACF,MAAMmE,GAAG,GAAG,MAAM5H,KAAK,CAAC6H,GAAG,CAACpE,GAAG,CAAC,UAAU,CAAC,CAAC;UAC5C,MAAMkD,UAAU,GAAGzB,YAAY,CAAC0C,GAAG,CAACE,IAAI,CAAC;UACzC,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACpE,OAAO,CAAEwE,GAAG,IAAK;YACvDvB,UAAU,CAACuB,GAAG,CAAC,CAACxE,OAAO,CAAEyE,CAAC,IACxBF,OAAO,CAACpE,IAAI,CAAC;cACX,GAAGsE,CAAC;cACJf,QAAQ,EAAEc,GAAG;cACbE,WAAW,EAAE3E,GAAG,CAAC,cAAc,CAAC;cAChC4E,IAAI,EAAE5E,GAAG,CAAC,MAAM;YAClB,CAAC,CACH,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOsE,GAAG,EAAE;UACZlG,OAAO,CAACyG,IAAI,CAAC,kBAAkB,EAAE7E,GAAG,CAAC,cAAc,CAAC,CAAC;QACvD;MACF;IACF;IACAnC,cAAc,CAAC2G,OAAO,CAAC;IACvBzG,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMmG,cAAc,GAAGA,CAAClE,GAAG,EAAE8E,aAAa,KAAK;IAC7C,KAAK,MAAMrG,IAAI,IAAIqG,aAAa,EAAE;MAChC,IAAI9E,GAAG,CAACvB,IAAI,CAAC,KAAKsG,SAAS,IAAI/E,GAAG,CAACvB,IAAI,CAAC,KAAK,IAAI,IAAIuB,GAAG,CAACvB,IAAI,CAAC,KAAK,EAAE,EAAE;QACrE,OAAOuB,GAAG,CAACvB,IAAI,CAAC;MAClB;IACF;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMuG,eAAe,GAAG7H,QAAQ,CAACO,SAAS,CAAC,CAACuH,MAAM,CAC/CjF,GAAG,IACFA,GAAG,CAAC4D,MAAM,CAAC3E,WAAW,CAAC,CAAC,CAACuC,QAAQ,CAAChE,UAAU,CAACyB,WAAW,CAAC,CAAC,CAAC,IAC3De,GAAG,CAAC6D,KAAK,CAAC5E,WAAW,CAAC,CAAC,CAACuC,QAAQ,CAAChE,UAAU,CAACyB,WAAW,CAAC,CAAC,CAC7D,CAAC;EAED,oBACEvC,OAAA;IAAKwI,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC9E5I,OAAA;MAAIwI,KAAK,EAAE;QAAEK,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,YAAY,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAE/G;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLrJ,OAAA;MAAKwI,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAER,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACnG5I,OAAA;QACEgC,IAAI,EAAC,MAAM;QACXyH,MAAM,EAAC,iBAAiB;QACxBC,QAAQ,EAAElI,iBAAkB;QAC5BgH,KAAK,EAAE;UACLmB,KAAK,EAAE,OAAO;UACdlB,OAAO,EAAE,MAAM;UACfmB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDlJ,WAAW,CAACgD,MAAM,GAAG,CAAC,iBACrBnD,OAAA;QAAKwI,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,gBAC/B5I,OAAA;UAAKwI,KAAK,EAAE;YAAEO,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAM,CAAE;UAAAF,QAAA,GAAC,SACjD,EAACzI,WAAW,CAACgD,MAAM,EAAC,mBACxB;QAAA;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrJ,OAAA;UAAKwI,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE,SAAS;YAAEgB,SAAS,EAAE;UAAM,CAAE;UAAAnB,QAAA,GAAC,qBACjD,EAACxE,MAAM,CAACC,IAAI,CAAClE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC6J,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACDrJ,OAAA;QAAKwI,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,eAC/B5I,OAAA;UACEiK,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,MAAMC,UAAU,GAAG,CACjB;cACE,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,MAAM;cACd,UAAU,EAAE,yBAAyB,CAAC;YACxC,CAAC,EACD;cACE,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,MAAM;cACd,UAAU,EAAE;YACd,CAAC,CACF;YACD9J,cAAc,CAAC8J,UAAU,CAAC;YAC1BxI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuI,UAAU,CAAC;UAChD,CAAE;UACF1B,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBC,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACda,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBM,MAAM,EAAE,SAAS;YACjBtB,QAAQ,EAAE;UACZ,CAAE;UAAAD,QAAA,EACH;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrJ,OAAA;MAAKwI,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEc,cAAc,EAAE,QAAQ;QAAEC,GAAG,EAAE,MAAM;QAAErB,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAC3F5I,OAAA;QACEgC,IAAI,EAAC,MAAM;QACXsI,WAAW,EAAC,mBAAmB;QAC/BnD,KAAK,EAAE9G,aAAc;QACrBqJ,QAAQ,EAAGjI,CAAC,IAAKnB,gBAAgB,CAACmB,CAAC,CAACI,MAAM,CAACsF,KAAK,CAAE;QAClDqB,KAAK,EAAE;UACLmB,KAAK,EAAE,OAAO;UACdlB,OAAO,EAAE,MAAM;UACfmB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFrJ,OAAA;QACEiK,OAAO,EAAEpC,iBAAkB;QAC3B0C,QAAQ,EAAEnJ,UAAU,IAAIjB,WAAW,CAACgD,MAAM,KAAK,CAAE;QACjDqF,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBoB,YAAY,EAAE,KAAK;UACnBD,MAAM,EAAE,MAAM;UACdb,KAAK,EAAE,OAAO;UACdL,eAAe,EAAEtH,UAAU,GAAG,SAAS,GAAG,SAAS;UACnD+I,MAAM,EAAE/I,UAAU,GAAG,aAAa,GAAG,SAAS;UAC9CoJ,OAAO,EAAEpJ,UAAU,GAAG,GAAG,GAAG;QAC9B,CAAE;QAAAwH,QAAA,EAEDxH,UAAU,GAAG,iBAAiB,GAAG;MAAqB;QAAA8H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLlJ,WAAW,CAACgD,MAAM,GAAG,CAAC,gBACrBnD,OAAA;MAAKwI,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,eACA5I,OAAA,CAACF,SAAS;QACR2K,KAAK,EAAE,cAActK,WAAW,CAACgD,MAAM,UAAW;QAClDuH,OAAO,EAAE,CACP;UACE3I,IAAI,EAAE,SAAS;UACf4I,QAAQ,EAAGrH,GAAG,IAAKkE,cAAc,CAAClE,GAAG,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;UACrHsH,QAAQ,EAAE;QACZ,CAAC,EACD;UACE7I,IAAI,EAAE,MAAM;UACZ4I,QAAQ,EAAGrH,GAAG,IAAKkE,cAAc,CAAClE,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;UAC5EsH,QAAQ,EAAE;QACZ,CAAC,EACD;UACE7I,IAAI,EAAE,UAAU;UAChB4I,QAAQ,EAAGrH,GAAG,IAAKkE,cAAc,CAAClE,GAAG,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;UACjGsH,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAGvH,GAAG,IAAK;YACb,MAAMwH,IAAI,GAAGtD,cAAc,CAAClE,GAAG,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC3F,OAAOwH,IAAI,gBACT9K,OAAA;cAAG+K,IAAI,EAAED,IAAK;cAACjJ,MAAM,EAAC,QAAQ;cAACmJ,GAAG,EAAC,qBAAqB;cAACxC,KAAK,EAAE;gBAACO,KAAK,EAAE,SAAS;gBAAEF,QAAQ,EAAE;cAAM,CAAE;cAAAD,QAAA,EAClGkC,IAAI,CAAC3H,MAAM,GAAG,EAAE,GAAG2H,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGH;YAAI;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,GACF,SAAS;UACf;QACF,CAAC,EACD;UACEtH,IAAI,EAAE,SAAS;UACf8I,IAAI,EAAGvH,GAAG,iBACRtD,OAAA;YACEiK,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAChE,GAAG,CAAE;YACxCkF,KAAK,EAAE;cACLE,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,OAAO;cACdN,OAAO,EAAE,UAAU;cACnBmB,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBM,MAAM,EAAE,SAAS;cACjBtB,QAAQ,EAAE;YACZ,CAAE;YAAAD,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAEZ,CAAC,CACD;QACF1B,IAAI,EAAExH,WAAW,CAACoI,MAAM,CAAE2C,CAAC,IAAK;UAC9B,MAAMjD,WAAW,GAAGT,cAAc,CAAC0D,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;UACpH,OAAOjD,WAAW,CAAC1F,WAAW,CAAC,CAAC,CAACuC,QAAQ,CAACzE,aAAa,CAACkC,WAAW,CAAC,CAAC,CAAC;QACxE,CAAC,CAAE;QACH4I,UAAU;QACVC,gBAAgB;QAChBC,KAAK;MAAA;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENrJ,OAAA;MAAKwI,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE;MACT,CAAE;MAAAH,QAAA,gBACA5I,OAAA;QAAA4I,QAAA,EAAG;MAAuE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9ErJ,OAAA;QAAGwI,KAAK,EAAE;UAAEK,QAAQ,EAAE,MAAM;UAAEkB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,EAAC;MAElD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGAnI,WAAW,CAACiC,MAAM,GAAG,CAAC,iBACrBnD,OAAA;MAAKwI,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,gBACA5I,OAAA;QAAIwI,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClGrJ,OAAA,CAACF,SAAS;QACR4K,OAAO,EAAE,CACP;UAAE3I,IAAI,EAAE,SAAS;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC2E,WAAW;UAAE2C,QAAQ,EAAE;QAAK,CAAC,EACvE;UAAE7I,IAAI,EAAE,MAAM;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC4E,IAAI;UAAE0C,QAAQ,EAAE;QAAK,CAAC,EAC7D;UAAE7I,IAAI,EAAE,UAAU;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC2D,QAAQ;UAAE2D,QAAQ,EAAE;QAAK,CAAC,EACrE;UAAE7I,IAAI,EAAE,QAAQ;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC4D,MAAM;UAAE0D,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAE7I,IAAI,EAAE,OAAO;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC6D,KAAK;UAAEyD,QAAQ,EAAE;QAAK,CAAC,CAC/D;QACFjD,IAAI,EAAEzG,WAAY;QAClBiK,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGA9I,eAAe,iBACdP,OAAA;MAAKwI,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE;MACX,CAAE;MAAAG,QAAA,gBACA5I,OAAA;QAAIwI,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,GACxEpB,cAAc,CAACjH,eAAe,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,EAAC,GAC/G,EAACiH,cAAc,CAACjH,eAAe,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAC,GAC1E;MAAA;QAAA2I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGLrJ,OAAA;QAAKwI,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE,MAAM;UAAErB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAChE,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC9F,GAAG,CAAEwI,GAAG,iBAC/CtL,OAAA;UAEEiK,OAAO,EAAEA,CAAA,KAAMhJ,YAAY,CAACqK,GAAG,CAAE;UACjC9C,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBoB,YAAY,EAAE,KAAK;YACnBD,MAAM,EAAE,MAAM;YACdd,UAAU,EAAE,KAAK;YACjBqB,MAAM,EAAE,SAAS;YACjBzB,eAAe,EAAE1H,SAAS,KAAKsK,GAAG,GAC9BA,GAAG,KAAK,aAAa,GACnB,SAAS,GACTA,GAAG,KAAK,QAAQ,GAChB,SAAS,GACT,SAAS,GACX,SAAS;YACbvC,KAAK,EAAE/H,SAAS,KAAKsK,GAAG,GAAG,OAAO,GAAG;UACvC,CAAE;UAAA1C,QAAA,EAED0C;QAAG,GAlBCA,GAAG;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrJ,OAAA;QAAKwI,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEc,cAAc,EAAE,QAAQ;UAAEpB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC9E5I,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXsI,WAAW,EAAE,aAAatJ,SAAS,KAAM;UACzCmG,KAAK,EAAErG,UAAW;UAClB4I,QAAQ,EAAGjI,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACI,MAAM,CAACsF,KAAK,CAAE;UAC/CqB,KAAK,EAAE;YACLmB,KAAK,EAAE,OAAO;YACdlB,OAAO,EAAE,MAAM;YACfmB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACb;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL/H,aAAa,gBACZtB,OAAA;QAAKwI,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEc,cAAc,EAAE,QAAQ;UAAE3B,OAAO,EAAE;QAAS,CAAE;QAAAG,QAAA,eAC3E5I,OAAA;UAAMwI,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAENrJ,OAAA,CAACF,SAAS;QACR4K,OAAO,EAAE,CACP;UAAE3I,IAAI,EAAE,QAAQ;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC4D,MAAM;UAAE0D,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAE7I,IAAI,EAAE,OAAO;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC6D,KAAK;UAAEyD,QAAQ,EAAE;QAAK,CAAC,EAC/D;UAAE7I,IAAI,EAAE,MAAM;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC0D,IAAI,IAAI,GAAG;UAAE4D,QAAQ,EAAE;QAAK,CAAC,EACpE;UAAE7I,IAAI,EAAE,QAAQ;UAAE4I,QAAQ,EAAGrH,GAAG,IAAKA,GAAG,CAAC8D,MAAM;UAAEwD,QAAQ,EAAE;QAAK,CAAC,CACjE;QACFjD,IAAI,EAAEW,eAAgB;QACtB6C,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnJ,EAAA,CAjgBuBD,UAAU;AAAAsL,EAAA,GAAVtL,UAAU;AAAA,IAAAsL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}