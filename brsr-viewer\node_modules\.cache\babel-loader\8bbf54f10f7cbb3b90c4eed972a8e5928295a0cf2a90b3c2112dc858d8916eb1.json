{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLDOMImplementation;\n  module.exports = XMLDOMImplementation = function () {\n    function XMLDOMImplementation() {}\n    XMLDOMImplementation.prototype.hasFeature = function (feature, version) {\n      return true;\n    };\n    XMLDOMImplementation.prototype.createDocumentType = function (qualifiedName, publicId, systemId) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    XMLDOMImplementation.prototype.createDocument = function (namespaceURI, qualifiedName, doctype) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    XMLDOMImplementation.prototype.createHTMLDocument = function (title) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    XMLDOMImplementation.prototype.getFeature = function (feature, version) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n    return XMLDOMImplementation;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["XMLDOMImplementation", "module", "exports", "prototype", "hasFeature", "feature", "version", "createDocumentType", "qualifiedName", "publicId", "systemId", "Error", "createDocument", "namespaceURI", "doctype", "createHTMLDocument", "title", "getFeature", "call"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDOMImplementation.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMImplementation;\n\n  module.exports = XMLDOMImplementation = (function() {\n    function XMLDOMImplementation() {}\n\n    XMLDOMImplementation.prototype.hasFeature = function(feature, version) {\n      return true;\n    };\n\n    XMLDOMImplementation.prototype.createDocumentType = function(qualifiedName, publicId, systemId) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createDocument = function(namespaceURI, qualifiedName, doctype) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createHTMLDocument = function(title) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLDOMImplementation;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,oBAAoB;EAExBC,MAAM,CAACC,OAAO,GAAGF,oBAAoB,GAAI,YAAW;IAClD,SAASA,oBAAoBA,CAAA,EAAG,CAAC;IAEjCA,oBAAoB,CAACG,SAAS,CAACC,UAAU,GAAG,UAASC,OAAO,EAAEC,OAAO,EAAE;MACrE,OAAO,IAAI;IACb,CAAC;IAEDN,oBAAoB,CAACG,SAAS,CAACI,kBAAkB,GAAG,UAASC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;MAC9F,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAEDX,oBAAoB,CAACG,SAAS,CAACS,cAAc,GAAG,UAASC,YAAY,EAAEL,aAAa,EAAEM,OAAO,EAAE;MAC7F,MAAM,IAAIH,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAEDX,oBAAoB,CAACG,SAAS,CAACY,kBAAkB,GAAG,UAASC,KAAK,EAAE;MAClE,MAAM,IAAIL,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAEDX,oBAAoB,CAACG,SAAS,CAACc,UAAU,GAAG,UAASZ,OAAO,EAAEC,OAAO,EAAE;MACrE,MAAM,IAAIK,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC;IAED,OAAOX,oBAAoB;EAE7B,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEkB,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}