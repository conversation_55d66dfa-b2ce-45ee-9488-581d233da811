{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var DocumentPosition,\n    NodeType,\n    XMLCData,\n    XMLComment,\n    XMLDeclaration,\n    XMLDocType,\n    XMLDummy,\n    XMLElement,\n    XMLNamedNodeMap,\n    XMLNode,\n    XMLNodeList,\n    XMLProcessingInstruction,\n    XMLRaw,\n    XMLText,\n    getValue,\n    isEmpty,\n    isFunction,\n    isObject,\n    ref1,\n    hasProp = {}.hasOwnProperty;\n  ref1 = require('./Utility'), isObject = ref1.isObject, isFunction = ref1.isFunction, isEmpty = ref1.isEmpty, getValue = ref1.getValue;\n  XMLElement = null;\n  XMLCData = null;\n  XMLComment = null;\n  XMLDeclaration = null;\n  XMLDocType = null;\n  XMLRaw = null;\n  XMLText = null;\n  XMLProcessingInstruction = null;\n  XMLDummy = null;\n  NodeType = null;\n  XMLNodeList = null;\n  XMLNamedNodeMap = null;\n  DocumentPosition = null;\n  module.exports = XMLNode = function () {\n    function XMLNode(parent1) {\n      this.parent = parent1;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.value = null;\n      this.children = [];\n      this.baseURI = null;\n      if (!XMLElement) {\n        XMLElement = require('./XMLElement');\n        XMLCData = require('./XMLCData');\n        XMLComment = require('./XMLComment');\n        XMLDeclaration = require('./XMLDeclaration');\n        XMLDocType = require('./XMLDocType');\n        XMLRaw = require('./XMLRaw');\n        XMLText = require('./XMLText');\n        XMLProcessingInstruction = require('./XMLProcessingInstruction');\n        XMLDummy = require('./XMLDummy');\n        NodeType = require('./NodeType');\n        XMLNodeList = require('./XMLNodeList');\n        XMLNamedNodeMap = require('./XMLNamedNodeMap');\n        DocumentPosition = require('./DocumentPosition');\n      }\n    }\n    Object.defineProperty(XMLNode.prototype, 'nodeName', {\n      get: function () {\n        return this.name;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'nodeType', {\n      get: function () {\n        return this.type;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'nodeValue', {\n      get: function () {\n        return this.value;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'parentNode', {\n      get: function () {\n        return this.parent;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'childNodes', {\n      get: function () {\n        if (!this.childNodeList || !this.childNodeList.nodes) {\n          this.childNodeList = new XMLNodeList(this.children);\n        }\n        return this.childNodeList;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'firstChild', {\n      get: function () {\n        return this.children[0] || null;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'lastChild', {\n      get: function () {\n        return this.children[this.children.length - 1] || null;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'previousSibling', {\n      get: function () {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i - 1] || null;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'nextSibling', {\n      get: function () {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i + 1] || null;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'ownerDocument', {\n      get: function () {\n        return this.document() || null;\n      }\n    });\n    Object.defineProperty(XMLNode.prototype, 'textContent', {\n      get: function () {\n        var child, j, len, ref2, str;\n        if (this.nodeType === NodeType.Element || this.nodeType === NodeType.DocumentFragment) {\n          str = '';\n          ref2 = this.children;\n          for (j = 0, len = ref2.length; j < len; j++) {\n            child = ref2[j];\n            if (child.textContent) {\n              str += child.textContent;\n            }\n          }\n          return str;\n        } else {\n          return null;\n        }\n      },\n      set: function (value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n    XMLNode.prototype.setParent = function (parent) {\n      var child, j, len, ref2, results;\n      this.parent = parent;\n      if (parent) {\n        this.options = parent.options;\n        this.stringify = parent.stringify;\n      }\n      ref2 = this.children;\n      results = [];\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        results.push(child.setParent(this));\n      }\n      return results;\n    };\n    XMLNode.prototype.element = function (name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref2, ref3, val;\n      lastChild = null;\n      if (attributes === null && text == null) {\n        ref2 = [{}, null], attributes = ref2[0], text = ref2[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref3 = [attributes, text], text = ref3[0], attributes = ref3[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val) && isEmpty(val)) {\n            lastChild = this.dummy();\n          } else if (isObject(val) && isEmpty(val)) {\n            lastChild = this.element(key);\n          } else if (!this.options.keepNullNodes && val == null) {\n            lastChild = this.dummy();\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            if (!this.options.ignoreDecorators && this.stringify.convertTextKey && key.indexOf(this.stringify.convertTextKey) === 0) {\n              lastChild = this.element(val);\n            } else {\n              lastChild = this.element(key);\n              lastChild.element(val);\n            }\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (!this.options.keepNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n    XMLNode.prototype.insertBefore = function (name, attributes, text) {\n      var child, i, newChild, refChild, removed;\n      if (name != null ? name.type : void 0) {\n        newChild = name;\n        refChild = attributes;\n        newChild.setParent(this);\n        if (refChild) {\n          i = children.indexOf(refChild);\n          removed = children.splice(i);\n          children.push(newChild);\n          Array.prototype.push.apply(children, removed);\n        } else {\n          children.push(newChild);\n        }\n        return newChild;\n      } else {\n        if (this.isRoot) {\n          throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n        }\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        child = this.parent.element(name, attributes, text);\n        Array.prototype.push.apply(this.parent.children, removed);\n        return child;\n      }\n    };\n    XMLNode.prototype.insertAfter = function (name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n    XMLNode.prototype.remove = function () {\n      var i, ref2;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref2 = [])), ref2;\n      return this.parent;\n    };\n    XMLNode.prototype.node = function (name, attributes, text) {\n      var child, ref2;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n    XMLNode.prototype.text = function (value) {\n      var child;\n      if (isObject(value)) {\n        this.element(value);\n      }\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n    XMLNode.prototype.cdata = function (value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n    XMLNode.prototype.comment = function (value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n    XMLNode.prototype.commentBefore = function (value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n    XMLNode.prototype.commentAfter = function (value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n    XMLNode.prototype.raw = function (value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n    XMLNode.prototype.dummy = function () {\n      var child;\n      child = new XMLDummy(this);\n      return child;\n    };\n    XMLNode.prototype.instruction = function (target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n    XMLNode.prototype.instructionBefore = function (target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n    XMLNode.prototype.instructionAfter = function (target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n    XMLNode.prototype.declaration = function (version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children.length === 0) {\n        doc.children.unshift(xmldec);\n      } else if (doc.children[0].type === NodeType.Declaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n    XMLNode.prototype.dtd = function (pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref2, ref3;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref2 = doc.children;\n      for (i = j = 0, len = ref2.length; j < len; i = ++j) {\n        child = ref2[i];\n        if (child.type === NodeType.DocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref3 = doc.children;\n      for (i = k = 0, len1 = ref3.length; k < len1; i = ++k) {\n        child = ref3[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n    XMLNode.prototype.up = function () {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n    XMLNode.prototype.root = function () {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n    XMLNode.prototype.document = function () {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n    XMLNode.prototype.end = function (options) {\n      return this.document().end(options);\n    };\n    XMLNode.prototype.prev = function () {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n    XMLNode.prototype.next = function () {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n    XMLNode.prototype.importDocument = function (doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n    XMLNode.prototype.debugInfo = function (name) {\n      var ref2, ref3;\n      name = name || this.name;\n      if (name == null && !((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref3 = this.parent) != null ? ref3.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n    XMLNode.prototype.ele = function (name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n    XMLNode.prototype.nod = function (name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n    XMLNode.prototype.txt = function (value) {\n      return this.text(value);\n    };\n    XMLNode.prototype.dat = function (value) {\n      return this.cdata(value);\n    };\n    XMLNode.prototype.com = function (value) {\n      return this.comment(value);\n    };\n    XMLNode.prototype.ins = function (target, value) {\n      return this.instruction(target, value);\n    };\n    XMLNode.prototype.doc = function () {\n      return this.document();\n    };\n    XMLNode.prototype.dec = function (version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n    XMLNode.prototype.e = function (name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n    XMLNode.prototype.n = function (name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n    XMLNode.prototype.t = function (value) {\n      return this.text(value);\n    };\n    XMLNode.prototype.d = function (value) {\n      return this.cdata(value);\n    };\n    XMLNode.prototype.c = function (value) {\n      return this.comment(value);\n    };\n    XMLNode.prototype.r = function (value) {\n      return this.raw(value);\n    };\n    XMLNode.prototype.i = function (target, value) {\n      return this.instruction(target, value);\n    };\n    XMLNode.prototype.u = function () {\n      return this.up();\n    };\n    XMLNode.prototype.importXMLBuilder = function (doc) {\n      return this.importDocument(doc);\n    };\n    XMLNode.prototype.replaceChild = function (newChild, oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.removeChild = function (oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.appendChild = function (newChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.hasChildNodes = function () {\n      return this.children.length !== 0;\n    };\n    XMLNode.prototype.cloneNode = function (deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.normalize = function () {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.isSupported = function (feature, version) {\n      return true;\n    };\n    XMLNode.prototype.hasAttributes = function () {\n      return this.attribs.length !== 0;\n    };\n    XMLNode.prototype.compareDocumentPosition = function (other) {\n      var ref, res;\n      ref = this;\n      if (ref === other) {\n        return 0;\n      } else if (this.document() !== other.document()) {\n        res = DocumentPosition.Disconnected | DocumentPosition.ImplementationSpecific;\n        if (Math.random() < 0.5) {\n          res |= DocumentPosition.Preceding;\n        } else {\n          res |= DocumentPosition.Following;\n        }\n        return res;\n      } else if (ref.isAncestor(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Preceding;\n      } else if (ref.isDescendant(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Following;\n      } else if (ref.isPreceding(other)) {\n        return DocumentPosition.Preceding;\n      } else {\n        return DocumentPosition.Following;\n      }\n    };\n    XMLNode.prototype.isSameNode = function (other) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.lookupPrefix = function (namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.isDefaultNamespace = function (namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.lookupNamespaceURI = function (prefix) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.isEqualNode = function (node) {\n      var i, j, ref2;\n      if (node.nodeType !== this.nodeType) {\n        return false;\n      }\n      if (node.children.length !== this.children.length) {\n        return false;\n      }\n      for (i = j = 0, ref2 = this.children.length - 1; 0 <= ref2 ? j <= ref2 : j >= ref2; i = 0 <= ref2 ? ++j : --j) {\n        if (!this.children[i].isEqualNode(node.children[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n    XMLNode.prototype.getFeature = function (feature, version) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.setUserData = function (key, data, handler) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.getUserData = function (key) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n    XMLNode.prototype.contains = function (other) {\n      if (!other) {\n        return false;\n      }\n      return other === this || this.isDescendant(other);\n    };\n    XMLNode.prototype.isDescendant = function (node) {\n      var child, isDescendantChild, j, len, ref2;\n      ref2 = this.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (node === child) {\n          return true;\n        }\n        isDescendantChild = child.isDescendant(node);\n        if (isDescendantChild) {\n          return true;\n        }\n      }\n      return false;\n    };\n    XMLNode.prototype.isAncestor = function (node) {\n      return node.isDescendant(this);\n    };\n    XMLNode.prototype.isPreceding = function (node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos < thisPos;\n      }\n    };\n    XMLNode.prototype.isFollowing = function (node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos > thisPos;\n      }\n    };\n    XMLNode.prototype.treePosition = function (node) {\n      var found, pos;\n      pos = 0;\n      found = false;\n      this.foreachTreeNode(this.document(), function (childNode) {\n        pos++;\n        if (!found && childNode === node) {\n          return found = true;\n        }\n      });\n      if (found) {\n        return pos;\n      } else {\n        return -1;\n      }\n    };\n    XMLNode.prototype.foreachTreeNode = function (node, func) {\n      var child, j, len, ref2, res;\n      node || (node = this.document());\n      ref2 = node.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (res = func(child)) {\n          return res;\n        } else {\n          res = this.foreachTreeNode(child, func);\n          if (res) {\n            return res;\n          }\n        }\n      }\n    };\n    return XMLNode;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["DocumentPosition", "NodeType", "XMLCData", "XMLComment", "XMLDeclaration", "XMLDocType", "XMLDummy", "XMLElement", "XMLNamedNodeMap", "XMLNode", "XMLNodeList", "XMLProcessingInstruction", "XMLRaw", "XMLText", "getValue", "isEmpty", "isFunction", "isObject", "ref1", "hasProp", "hasOwnProperty", "require", "module", "exports", "parent1", "parent", "options", "stringify", "value", "children", "baseURI", "Object", "defineProperty", "prototype", "get", "name", "type", "childNodeList", "nodes", "length", "i", "indexOf", "document", "child", "j", "len", "ref2", "str", "nodeType", "Element", "DocumentFragment", "textContent", "set", "Error", "debugInfo", "setParent", "results", "push", "element", "attributes", "text", "childNode", "item", "k", "key", "<PERSON><PERSON><PERSON><PERSON>", "len1", "ref3", "val", "Array", "isArray", "apply", "call", "ignoreDecorators", "convertAttKey", "attribute", "substr", "separateArrayItems", "dummy", "keepNullNodes", "convertTextKey", "convertCDataKey", "cdata", "convertCommentKey", "comment", "convertRawKey", "raw", "convertPIKey", "instruction", "node", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "removed", "splice", "isRoot", "insertAfter", "remove", "concat", "commentBefore", "commentAfter", "target", "insTarget", "insValue", "instructionBefore", "instructionAfter", "declaration", "version", "encoding", "standalone", "doc", "xmldec", "unshift", "Declaration", "root", "dtd", "pubID", "sysID", "doctype", "DocType", "up", "Document", "rootObject", "end", "prev", "next", "importDocument", "clonedRoot", "clone", "ele", "nod", "txt", "dat", "com", "ins", "dec", "e", "n", "t", "d", "c", "r", "u", "importXMLBuilder", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "hasChildNodes", "cloneNode", "deep", "normalize", "isSupported", "feature", "hasAttributes", "attribs", "compareDocumentPosition", "other", "ref", "res", "Disconnected", "ImplementationSpecific", "Math", "random", "Preceding", "Following", "isAncestor", "Contains", "isDescendant", "isPreceding", "isSameNode", "lookupPrefix", "namespaceURI", "isDefaultNamespace", "lookupNamespaceURI", "prefix", "isEqualNode", "getFeature", "setUserData", "data", "handler", "getUserData", "contains", "isDescendant<PERSON><PERSON><PERSON>", "nodePos", "thisPos", "treePosition", "isFollowing", "found", "pos", "foreachTreeNode", "func"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLNode.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var DocumentPosition, NodeType, XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNamedNodeMap, XMLNode, XMLNodeList, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref1,\n    hasProp = {}.hasOwnProperty;\n\n  ref1 = require('./Utility'), isObject = ref1.isObject, isFunction = ref1.isFunction, isEmpty = ref1.isEmpty, getValue = ref1.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  NodeType = null;\n\n  XMLNodeList = null;\n\n  XMLNamedNodeMap = null;\n\n  DocumentPosition = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent1) {\n      this.parent = parent1;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.value = null;\n      this.children = [];\n      this.baseURI = null;\n      if (!XMLElement) {\n        XMLElement = require('./XMLElement');\n        XMLCData = require('./XMLCData');\n        XMLComment = require('./XMLComment');\n        XMLDeclaration = require('./XMLDeclaration');\n        XMLDocType = require('./XMLDocType');\n        XMLRaw = require('./XMLRaw');\n        XMLText = require('./XMLText');\n        XMLProcessingInstruction = require('./XMLProcessingInstruction');\n        XMLDummy = require('./XMLDummy');\n        NodeType = require('./NodeType');\n        XMLNodeList = require('./XMLNodeList');\n        XMLNamedNodeMap = require('./XMLNamedNodeMap');\n        DocumentPosition = require('./DocumentPosition');\n      }\n    }\n\n    Object.defineProperty(XMLNode.prototype, 'nodeName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeValue', {\n      get: function() {\n        return this.value;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'parentNode', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'childNodes', {\n      get: function() {\n        if (!this.childNodeList || !this.childNodeList.nodes) {\n          this.childNodeList = new XMLNodeList(this.children);\n        }\n        return this.childNodeList;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'firstChild', {\n      get: function() {\n        return this.children[0] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'lastChild', {\n      get: function() {\n        return this.children[this.children.length - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'previousSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nextSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i + 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'ownerDocument', {\n      get: function() {\n        return this.document() || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'textContent', {\n      get: function() {\n        var child, j, len, ref2, str;\n        if (this.nodeType === NodeType.Element || this.nodeType === NodeType.DocumentFragment) {\n          str = '';\n          ref2 = this.children;\n          for (j = 0, len = ref2.length; j < len; j++) {\n            child = ref2[j];\n            if (child.textContent) {\n              str += child.textContent;\n            }\n          }\n          return str;\n        } else {\n          return null;\n        }\n      },\n      set: function(value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLNode.prototype.setParent = function(parent) {\n      var child, j, len, ref2, results;\n      this.parent = parent;\n      if (parent) {\n        this.options = parent.options;\n        this.stringify = parent.stringify;\n      }\n      ref2 = this.children;\n      results = [];\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        results.push(child.setParent(this));\n      }\n      return results;\n    };\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref2, ref3, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref2 = [{}, null], attributes = ref2[0], text = ref2[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref3 = [attributes, text], text = ref3[0], attributes = ref3[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val) && isEmpty(val)) {\n            lastChild = this.dummy();\n          } else if (isObject(val) && isEmpty(val)) {\n            lastChild = this.element(key);\n          } else if (!this.options.keepNullNodes && (val == null)) {\n            lastChild = this.dummy();\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            if (!this.options.ignoreDecorators && this.stringify.convertTextKey && key.indexOf(this.stringify.convertTextKey) === 0) {\n              lastChild = this.element(val);\n            } else {\n              lastChild = this.element(key);\n              lastChild.element(val);\n            }\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (!this.options.keepNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, newChild, refChild, removed;\n      if (name != null ? name.type : void 0) {\n        newChild = name;\n        refChild = attributes;\n        newChild.setParent(this);\n        if (refChild) {\n          i = children.indexOf(refChild);\n          removed = children.splice(i);\n          children.push(newChild);\n          Array.prototype.push.apply(children, removed);\n        } else {\n          children.push(newChild);\n        }\n        return newChild;\n      } else {\n        if (this.isRoot) {\n          throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n        }\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        child = this.parent.element(name, attributes, text);\n        Array.prototype.push.apply(this.parent.children, removed);\n        return child;\n      }\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref2;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref2 = [])), ref2;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref2;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      if (isObject(value)) {\n        this.element(value);\n      }\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children.length === 0) {\n        doc.children.unshift(xmldec);\n      } else if (doc.children[0].type === NodeType.Declaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref2, ref3;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref2 = doc.children;\n      for (i = j = 0, len = ref2.length; j < len; i = ++j) {\n        child = ref2[i];\n        if (child.type === NodeType.DocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref3 = doc.children;\n      for (i = k = 0, len1 = ref3.length; k < len1; i = ++k) {\n        child = ref3[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref2, ref3;\n      name = name || this.name;\n      if ((name == null) && !((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref3 = this.parent) != null ? ref3.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    XMLNode.prototype.replaceChild = function(newChild, oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.removeChild = function(oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.appendChild = function(newChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.hasChildNodes = function() {\n      return this.children.length !== 0;\n    };\n\n    XMLNode.prototype.cloneNode = function(deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.normalize = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isSupported = function(feature, version) {\n      return true;\n    };\n\n    XMLNode.prototype.hasAttributes = function() {\n      return this.attribs.length !== 0;\n    };\n\n    XMLNode.prototype.compareDocumentPosition = function(other) {\n      var ref, res;\n      ref = this;\n      if (ref === other) {\n        return 0;\n      } else if (this.document() !== other.document()) {\n        res = DocumentPosition.Disconnected | DocumentPosition.ImplementationSpecific;\n        if (Math.random() < 0.5) {\n          res |= DocumentPosition.Preceding;\n        } else {\n          res |= DocumentPosition.Following;\n        }\n        return res;\n      } else if (ref.isAncestor(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Preceding;\n      } else if (ref.isDescendant(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Following;\n      } else if (ref.isPreceding(other)) {\n        return DocumentPosition.Preceding;\n      } else {\n        return DocumentPosition.Following;\n      }\n    };\n\n    XMLNode.prototype.isSameNode = function(other) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupPrefix = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isDefaultNamespace = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupNamespaceURI = function(prefix) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isEqualNode = function(node) {\n      var i, j, ref2;\n      if (node.nodeType !== this.nodeType) {\n        return false;\n      }\n      if (node.children.length !== this.children.length) {\n        return false;\n      }\n      for (i = j = 0, ref2 = this.children.length - 1; 0 <= ref2 ? j <= ref2 : j >= ref2; i = 0 <= ref2 ? ++j : --j) {\n        if (!this.children[i].isEqualNode(node.children[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    XMLNode.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.setUserData = function(key, data, handler) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.getUserData = function(key) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.contains = function(other) {\n      if (!other) {\n        return false;\n      }\n      return other === this || this.isDescendant(other);\n    };\n\n    XMLNode.prototype.isDescendant = function(node) {\n      var child, isDescendantChild, j, len, ref2;\n      ref2 = this.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (node === child) {\n          return true;\n        }\n        isDescendantChild = child.isDescendant(node);\n        if (isDescendantChild) {\n          return true;\n        }\n      }\n      return false;\n    };\n\n    XMLNode.prototype.isAncestor = function(node) {\n      return node.isDescendant(this);\n    };\n\n    XMLNode.prototype.isPreceding = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos < thisPos;\n      }\n    };\n\n    XMLNode.prototype.isFollowing = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos > thisPos;\n      }\n    };\n\n    XMLNode.prototype.treePosition = function(node) {\n      var found, pos;\n      pos = 0;\n      found = false;\n      this.foreachTreeNode(this.document(), function(childNode) {\n        pos++;\n        if (!found && childNode === node) {\n          return found = true;\n        }\n      });\n      if (found) {\n        return pos;\n      } else {\n        return -1;\n      }\n    };\n\n    XMLNode.prototype.foreachTreeNode = function(node, func) {\n      var child, j, len, ref2, res;\n      node || (node = this.document());\n      ref2 = node.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (res = func(child)) {\n          return res;\n        } else {\n          res = this.foreachTreeNode(child, func);\n          if (res) {\n            return res;\n          }\n        }\n      }\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,gBAAgB;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,UAAU;IAAEC,cAAc;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,UAAU;IAAEC,eAAe;IAAEC,OAAO;IAAEC,WAAW;IAAEC,wBAAwB;IAAEC,MAAM;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,IAAI;IACrOC,OAAO,GAAG,CAAC,CAAC,CAACC,cAAc;EAE7BF,IAAI,GAAGG,OAAO,CAAC,WAAW,CAAC,EAAEJ,QAAQ,GAAGC,IAAI,CAACD,QAAQ,EAAED,UAAU,GAAGE,IAAI,CAACF,UAAU,EAAED,OAAO,GAAGG,IAAI,CAACH,OAAO,EAAED,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;EAErIP,UAAU,GAAG,IAAI;EAEjBL,QAAQ,GAAG,IAAI;EAEfC,UAAU,GAAG,IAAI;EAEjBC,cAAc,GAAG,IAAI;EAErBC,UAAU,GAAG,IAAI;EAEjBO,MAAM,GAAG,IAAI;EAEbC,OAAO,GAAG,IAAI;EAEdF,wBAAwB,GAAG,IAAI;EAE/BL,QAAQ,GAAG,IAAI;EAEfL,QAAQ,GAAG,IAAI;EAEfS,WAAW,GAAG,IAAI;EAElBF,eAAe,GAAG,IAAI;EAEtBR,gBAAgB,GAAG,IAAI;EAEvBsB,MAAM,CAACC,OAAO,GAAGd,OAAO,GAAI,YAAW;IACrC,SAASA,OAAOA,CAACe,OAAO,EAAE;MACxB,IAAI,CAACC,MAAM,GAAGD,OAAO;MACrB,IAAI,IAAI,CAACC,MAAM,EAAE;QACf,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;QAClC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACF,MAAM,CAACE,SAAS;MACxC;MACA,IAAI,CAACC,KAAK,GAAG,IAAI;MACjB,IAAI,CAACC,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACvB,UAAU,EAAE;QACfA,UAAU,GAAGc,OAAO,CAAC,cAAc,CAAC;QACpCnB,QAAQ,GAAGmB,OAAO,CAAC,YAAY,CAAC;QAChClB,UAAU,GAAGkB,OAAO,CAAC,cAAc,CAAC;QACpCjB,cAAc,GAAGiB,OAAO,CAAC,kBAAkB,CAAC;QAC5ChB,UAAU,GAAGgB,OAAO,CAAC,cAAc,CAAC;QACpCT,MAAM,GAAGS,OAAO,CAAC,UAAU,CAAC;QAC5BR,OAAO,GAAGQ,OAAO,CAAC,WAAW,CAAC;QAC9BV,wBAAwB,GAAGU,OAAO,CAAC,4BAA4B,CAAC;QAChEf,QAAQ,GAAGe,OAAO,CAAC,YAAY,CAAC;QAChCpB,QAAQ,GAAGoB,OAAO,CAAC,YAAY,CAAC;QAChCX,WAAW,GAAGW,OAAO,CAAC,eAAe,CAAC;QACtCb,eAAe,GAAGa,OAAO,CAAC,mBAAmB,CAAC;QAC9CrB,gBAAgB,GAAGqB,OAAO,CAAC,oBAAoB,CAAC;MAClD;IACF;IAEAU,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,UAAU,EAAE;MACnDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACC,IAAI;MAClB;IACF,CAAC,CAAC;IAEFJ,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,UAAU,EAAE;MACnDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACE,IAAI;MAClB;IACF,CAAC,CAAC;IAEFL,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,WAAW,EAAE;MACpDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACN,KAAK;MACnB;IACF,CAAC,CAAC;IAEFG,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,YAAY,EAAE;MACrDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACT,MAAM;MACpB;IACF,CAAC,CAAC;IAEFM,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,YAAY,EAAE;MACrDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,IAAI,CAAC,IAAI,CAACG,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACC,KAAK,EAAE;UACpD,IAAI,CAACD,aAAa,GAAG,IAAI3B,WAAW,CAAC,IAAI,CAACmB,QAAQ,CAAC;QACrD;QACA,OAAO,IAAI,CAACQ,aAAa;MAC3B;IACF,CAAC,CAAC;IAEFN,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,YAAY,EAAE;MACrDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACL,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;MACjC;IACF,CAAC,CAAC;IAEFE,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,WAAW,EAAE;MACpDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACL,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;MACxD;IACF,CAAC,CAAC;IAEFR,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,iBAAiB,EAAE;MAC1DC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,IAAIM,CAAC;QACLA,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;QACtC,OAAO,IAAI,CAAChB,MAAM,CAACI,QAAQ,CAACW,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI;MAC5C;IACF,CAAC,CAAC;IAEFT,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,aAAa,EAAE;MACtDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,IAAIM,CAAC;QACLA,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;QACtC,OAAO,IAAI,CAAChB,MAAM,CAACI,QAAQ,CAACW,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI;MAC5C;IACF,CAAC,CAAC;IAEFT,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,eAAe,EAAE;MACxDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACQ,QAAQ,CAAC,CAAC,IAAI,IAAI;MAChC;IACF,CAAC,CAAC;IAEFX,MAAM,CAACC,cAAc,CAACvB,OAAO,CAACwB,SAAS,EAAE,aAAa,EAAE;MACtDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,IAAIS,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG;QAC5B,IAAI,IAAI,CAACC,QAAQ,KAAK/C,QAAQ,CAACgD,OAAO,IAAI,IAAI,CAACD,QAAQ,KAAK/C,QAAQ,CAACiD,gBAAgB,EAAE;UACrFH,GAAG,GAAG,EAAE;UACRD,IAAI,GAAG,IAAI,CAACjB,QAAQ;UACpB,KAAKe,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACP,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;YAC3CD,KAAK,GAAGG,IAAI,CAACF,CAAC,CAAC;YACf,IAAID,KAAK,CAACQ,WAAW,EAAE;cACrBJ,GAAG,IAAIJ,KAAK,CAACQ,WAAW;YAC1B;UACF;UACA,OAAOJ,GAAG;QACZ,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MACDK,GAAG,EAAE,SAAAA,CAASxB,KAAK,EAAE;QACnB,MAAM,IAAIyB,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF7C,OAAO,CAACwB,SAAS,CAACsB,SAAS,GAAG,UAAS9B,MAAM,EAAE;MAC7C,IAAIkB,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEU,OAAO;MAChC,IAAI,CAAC/B,MAAM,GAAGA,MAAM;MACpB,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,OAAO,GAAGD,MAAM,CAACC,OAAO;QAC7B,IAAI,CAACC,SAAS,GAAGF,MAAM,CAACE,SAAS;MACnC;MACAmB,IAAI,GAAG,IAAI,CAACjB,QAAQ;MACpB2B,OAAO,GAAG,EAAE;MACZ,KAAKZ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACP,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC3CD,KAAK,GAAGG,IAAI,CAACF,CAAC,CAAC;QACfY,OAAO,CAACC,IAAI,CAACd,KAAK,CAACY,SAAS,CAAC,IAAI,CAAC,CAAC;MACrC;MACA,OAAOC,OAAO;IAChB,CAAC;IAED/C,OAAO,CAACwB,SAAS,CAACyB,OAAO,GAAG,UAASvB,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MAC3D,IAAIC,SAAS,EAAEC,IAAI,EAAElB,CAAC,EAAEmB,CAAC,EAAEC,GAAG,EAAEC,SAAS,EAAEpB,GAAG,EAAEqB,IAAI,EAAEpB,IAAI,EAAEqB,IAAI,EAAEC,GAAG;MACrEH,SAAS,GAAG,IAAI;MAChB,IAAIN,UAAU,KAAK,IAAI,IAAKC,IAAI,IAAI,IAAK,EAAE;QACzCd,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAEa,UAAU,GAAGb,IAAI,CAAC,CAAC,CAAC,EAAEc,IAAI,GAAGd,IAAI,CAAC,CAAC,CAAC;MACzD;MACA,IAAIa,UAAU,IAAI,IAAI,EAAE;QACtBA,UAAU,GAAG,CAAC,CAAC;MACjB;MACAA,UAAU,GAAG7C,QAAQ,CAAC6C,UAAU,CAAC;MACjC,IAAI,CAAC1C,QAAQ,CAAC0C,UAAU,CAAC,EAAE;QACzBQ,IAAI,GAAG,CAACR,UAAU,EAAEC,IAAI,CAAC,EAAEA,IAAI,GAAGO,IAAI,CAAC,CAAC,CAAC,EAAER,UAAU,GAAGQ,IAAI,CAAC,CAAC,CAAC;MACjE;MACA,IAAIhC,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,GAAGrB,QAAQ,CAACqB,IAAI,CAAC;MACvB;MACA,IAAIkC,KAAK,CAACC,OAAO,CAACnC,IAAI,CAAC,EAAE;QACvB,KAAKS,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGV,IAAI,CAACI,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UAC3CkB,IAAI,GAAG3B,IAAI,CAACS,CAAC,CAAC;UACdqB,SAAS,GAAG,IAAI,CAACP,OAAO,CAACI,IAAI,CAAC;QAChC;MACF,CAAC,MAAM,IAAI9C,UAAU,CAACmB,IAAI,CAAC,EAAE;QAC3B8B,SAAS,GAAG,IAAI,CAACP,OAAO,CAACvB,IAAI,CAACoC,KAAK,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM,IAAItD,QAAQ,CAACkB,IAAI,CAAC,EAAE;QACzB,KAAK6B,GAAG,IAAI7B,IAAI,EAAE;UAChB,IAAI,CAAChB,OAAO,CAACqD,IAAI,CAACrC,IAAI,EAAE6B,GAAG,CAAC,EAAE;UAC9BI,GAAG,GAAGjC,IAAI,CAAC6B,GAAG,CAAC;UACf,IAAIhD,UAAU,CAACoD,GAAG,CAAC,EAAE;YACnBA,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC;UACnB;UACA,IAAI,CAAC,IAAI,CAAC7C,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAAC+C,aAAa,IAAIV,GAAG,CAACvB,OAAO,CAAC,IAAI,CAACd,SAAS,CAAC+C,aAAa,CAAC,KAAK,CAAC,EAAE;YACrHT,SAAS,GAAG,IAAI,CAACU,SAAS,CAACX,GAAG,CAACY,MAAM,CAAC,IAAI,CAACjD,SAAS,CAAC+C,aAAa,CAACnC,MAAM,CAAC,EAAE6B,GAAG,CAAC;UAClF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC1C,OAAO,CAACmD,kBAAkB,IAAIR,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAAIrD,OAAO,CAACqD,GAAG,CAAC,EAAE;YACjFH,SAAS,GAAG,IAAI,CAACa,KAAK,CAAC,CAAC;UAC1B,CAAC,MAAM,IAAI7D,QAAQ,CAACmD,GAAG,CAAC,IAAIrD,OAAO,CAACqD,GAAG,CAAC,EAAE;YACxCH,SAAS,GAAG,IAAI,CAACP,OAAO,CAACM,GAAG,CAAC;UAC/B,CAAC,MAAM,IAAI,CAAC,IAAI,CAACtC,OAAO,CAACqD,aAAa,IAAKX,GAAG,IAAI,IAAK,EAAE;YACvDH,SAAS,GAAG,IAAI,CAACa,KAAK,CAAC,CAAC;UAC1B,CAAC,MAAM,IAAI,CAAC,IAAI,CAACpD,OAAO,CAACmD,kBAAkB,IAAIR,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;YACjE,KAAKL,CAAC,GAAG,CAAC,EAAEG,IAAI,GAAGE,GAAG,CAAC7B,MAAM,EAAEwB,CAAC,GAAGG,IAAI,EAAEH,CAAC,EAAE,EAAE;cAC5CD,IAAI,GAAGM,GAAG,CAACL,CAAC,CAAC;cACbF,SAAS,GAAG,CAAC,CAAC;cACdA,SAAS,CAACG,GAAG,CAAC,GAAGF,IAAI;cACrBG,SAAS,GAAG,IAAI,CAACP,OAAO,CAACG,SAAS,CAAC;YACrC;UACF,CAAC,MAAM,IAAI5C,QAAQ,CAACmD,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC1C,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAACqD,cAAc,IAAIhB,GAAG,CAACvB,OAAO,CAAC,IAAI,CAACd,SAAS,CAACqD,cAAc,CAAC,KAAK,CAAC,EAAE;cACvHf,SAAS,GAAG,IAAI,CAACP,OAAO,CAACU,GAAG,CAAC;YAC/B,CAAC,MAAM;cACLH,SAAS,GAAG,IAAI,CAACP,OAAO,CAACM,GAAG,CAAC;cAC7BC,SAAS,CAACP,OAAO,CAACU,GAAG,CAAC;YACxB;UACF,CAAC,MAAM;YACLH,SAAS,GAAG,IAAI,CAACP,OAAO,CAACM,GAAG,EAAEI,GAAG,CAAC;UACpC;QACF;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC1C,OAAO,CAACqD,aAAa,IAAInB,IAAI,KAAK,IAAI,EAAE;QACvDK,SAAS,GAAG,IAAI,CAACa,KAAK,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAAC,IAAI,CAACpD,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAACqD,cAAc,IAAI7C,IAAI,CAACM,OAAO,CAAC,IAAI,CAACd,SAAS,CAACqD,cAAc,CAAC,KAAK,CAAC,EAAE;UACxHf,SAAS,GAAG,IAAI,CAACL,IAAI,CAACA,IAAI,CAAC;QAC7B,CAAC,MAAM,IAAI,CAAC,IAAI,CAAClC,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAACsD,eAAe,IAAI9C,IAAI,CAACM,OAAO,CAAC,IAAI,CAACd,SAAS,CAACsD,eAAe,CAAC,KAAK,CAAC,EAAE;UACjIhB,SAAS,GAAG,IAAI,CAACiB,KAAK,CAACtB,IAAI,CAAC;QAC9B,CAAC,MAAM,IAAI,CAAC,IAAI,CAAClC,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAACwD,iBAAiB,IAAIhD,IAAI,CAACM,OAAO,CAAC,IAAI,CAACd,SAAS,CAACwD,iBAAiB,CAAC,KAAK,CAAC,EAAE;UACrIlB,SAAS,GAAG,IAAI,CAACmB,OAAO,CAACxB,IAAI,CAAC;QAChC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAClC,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAAC0D,aAAa,IAAIlD,IAAI,CAACM,OAAO,CAAC,IAAI,CAACd,SAAS,CAAC0D,aAAa,CAAC,KAAK,CAAC,EAAE;UAC7HpB,SAAS,GAAG,IAAI,CAACqB,GAAG,CAAC1B,IAAI,CAAC;QAC5B,CAAC,MAAM,IAAI,CAAC,IAAI,CAAClC,OAAO,CAAC+C,gBAAgB,IAAI,IAAI,CAAC9C,SAAS,CAAC4D,YAAY,IAAIpD,IAAI,CAACM,OAAO,CAAC,IAAI,CAACd,SAAS,CAAC4D,YAAY,CAAC,KAAK,CAAC,EAAE;UAC3HtB,SAAS,GAAG,IAAI,CAACuB,WAAW,CAACrD,IAAI,CAACyC,MAAM,CAAC,IAAI,CAACjD,SAAS,CAAC4D,YAAY,CAAChD,MAAM,CAAC,EAAEqB,IAAI,CAAC;QACrF,CAAC,MAAM;UACLK,SAAS,GAAG,IAAI,CAACwB,IAAI,CAACtD,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;QAC/C;MACF;MACA,IAAIK,SAAS,IAAI,IAAI,EAAE;QACrB,MAAM,IAAIZ,KAAK,CAAC,sCAAsC,GAAGlB,IAAI,GAAG,IAAI,GAAG,IAAI,CAACmB,SAAS,CAAC,CAAC,CAAC;MAC1F;MACA,OAAOW,SAAS;IAClB,CAAC;IAEDxD,OAAO,CAACwB,SAAS,CAACyD,YAAY,GAAG,UAASvD,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MAChE,IAAIjB,KAAK,EAAEH,CAAC,EAAEmD,QAAQ,EAAEC,QAAQ,EAAEC,OAAO;MACzC,IAAI1D,IAAI,IAAI,IAAI,GAAGA,IAAI,CAACC,IAAI,GAAG,KAAK,CAAC,EAAE;QACrCuD,QAAQ,GAAGxD,IAAI;QACfyD,QAAQ,GAAGjC,UAAU;QACrBgC,QAAQ,CAACpC,SAAS,CAAC,IAAI,CAAC;QACxB,IAAIqC,QAAQ,EAAE;UACZpD,CAAC,GAAGX,QAAQ,CAACY,OAAO,CAACmD,QAAQ,CAAC;UAC9BC,OAAO,GAAGhE,QAAQ,CAACiE,MAAM,CAACtD,CAAC,CAAC;UAC5BX,QAAQ,CAAC4B,IAAI,CAACkC,QAAQ,CAAC;UACvBtB,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC1C,QAAQ,EAAEgE,OAAO,CAAC;QAC/C,CAAC,MAAM;UACLhE,QAAQ,CAAC4B,IAAI,CAACkC,QAAQ,CAAC;QACzB;QACA,OAAOA,QAAQ;MACjB,CAAC,MAAM;QACL,IAAI,IAAI,CAACI,MAAM,EAAE;UACf,MAAM,IAAI1C,KAAK,CAAC,wCAAwC,GAAG,IAAI,CAACC,SAAS,CAACnB,IAAI,CAAC,CAAC;QAClF;QACAK,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;QACtCoD,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACI,QAAQ,CAACiE,MAAM,CAACtD,CAAC,CAAC;QACxCG,KAAK,GAAG,IAAI,CAAClB,MAAM,CAACiC,OAAO,CAACvB,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;QACnDS,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAEgE,OAAO,CAAC;QACzD,OAAOlD,KAAK;MACd;IACF,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAAC+D,WAAW,GAAG,UAAS7D,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MAC/D,IAAIjB,KAAK,EAAEH,CAAC,EAAEqD,OAAO;MACrB,IAAI,IAAI,CAACE,MAAM,EAAE;QACf,MAAM,IAAI1C,KAAK,CAAC,wCAAwC,GAAG,IAAI,CAACC,SAAS,CAACnB,IAAI,CAAC,CAAC;MAClF;MACAK,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtCoD,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACI,QAAQ,CAACiE,MAAM,CAACtD,CAAC,GAAG,CAAC,CAAC;MAC5CG,KAAK,GAAG,IAAI,CAAClB,MAAM,CAACiC,OAAO,CAACvB,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;MACnDS,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAEgE,OAAO,CAAC;MACzD,OAAOlD,KAAK;IACd,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAACgE,MAAM,GAAG,YAAW;MACpC,IAAIzD,CAAC,EAAEM,IAAI;MACX,IAAI,IAAI,CAACiD,MAAM,EAAE;QACf,MAAM,IAAI1C,KAAK,CAAC,kCAAkC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MACxE;MACAd,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtC,EAAE,CAACqD,MAAM,CAACvB,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAE,CAACW,CAAC,EAAEA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,CAAC0D,MAAM,CAACpD,IAAI,GAAG,EAAE,CAAC,CAAC,EAAEA,IAAI;MAC7E,OAAO,IAAI,CAACrB,MAAM;IACpB,CAAC;IAEDhB,OAAO,CAACwB,SAAS,CAACwD,IAAI,GAAG,UAAStD,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MACxD,IAAIjB,KAAK,EAAEG,IAAI;MACf,IAAIX,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,GAAGrB,QAAQ,CAACqB,IAAI,CAAC;MACvB;MACAwB,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC;MAC/BA,UAAU,GAAG7C,QAAQ,CAAC6C,UAAU,CAAC;MACjC,IAAI,CAAC1C,QAAQ,CAAC0C,UAAU,CAAC,EAAE;QACzBb,IAAI,GAAG,CAACa,UAAU,EAAEC,IAAI,CAAC,EAAEA,IAAI,GAAGd,IAAI,CAAC,CAAC,CAAC,EAAEa,UAAU,GAAGb,IAAI,CAAC,CAAC,CAAC;MACjE;MACAH,KAAK,GAAG,IAAIpC,UAAU,CAAC,IAAI,EAAE4B,IAAI,EAAEwB,UAAU,CAAC;MAC9C,IAAIC,IAAI,IAAI,IAAI,EAAE;QAChBjB,KAAK,CAACiB,IAAI,CAACA,IAAI,CAAC;MAClB;MACA,IAAI,CAAC/B,QAAQ,CAAC4B,IAAI,CAACd,KAAK,CAAC;MACzB,OAAOA,KAAK;IACd,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAAC2B,IAAI,GAAG,UAAShC,KAAK,EAAE;MACvC,IAAIe,KAAK;MACT,IAAI1B,QAAQ,CAACW,KAAK,CAAC,EAAE;QACnB,IAAI,CAAC8B,OAAO,CAAC9B,KAAK,CAAC;MACrB;MACAe,KAAK,GAAG,IAAI9B,OAAO,CAAC,IAAI,EAAEe,KAAK,CAAC;MAChC,IAAI,CAACC,QAAQ,CAAC4B,IAAI,CAACd,KAAK,CAAC;MACzB,OAAO,IAAI;IACb,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAACiD,KAAK,GAAG,UAAStD,KAAK,EAAE;MACxC,IAAIe,KAAK;MACTA,KAAK,GAAG,IAAIzC,QAAQ,CAAC,IAAI,EAAE0B,KAAK,CAAC;MACjC,IAAI,CAACC,QAAQ,CAAC4B,IAAI,CAACd,KAAK,CAAC;MACzB,OAAO,IAAI;IACb,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAACmD,OAAO,GAAG,UAASxD,KAAK,EAAE;MAC1C,IAAIe,KAAK;MACTA,KAAK,GAAG,IAAIxC,UAAU,CAAC,IAAI,EAAEyB,KAAK,CAAC;MACnC,IAAI,CAACC,QAAQ,CAAC4B,IAAI,CAACd,KAAK,CAAC;MACzB,OAAO,IAAI;IACb,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAACkE,aAAa,GAAG,UAASvE,KAAK,EAAE;MAChD,IAAIe,KAAK,EAAEH,CAAC,EAAEqD,OAAO;MACrBrD,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtCoD,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACI,QAAQ,CAACiE,MAAM,CAACtD,CAAC,CAAC;MACxCG,KAAK,GAAG,IAAI,CAAClB,MAAM,CAAC2D,OAAO,CAACxD,KAAK,CAAC;MAClCyC,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAEgE,OAAO,CAAC;MACzD,OAAO,IAAI;IACb,CAAC;IAEDpF,OAAO,CAACwB,SAAS,CAACmE,YAAY,GAAG,UAASxE,KAAK,EAAE;MAC/C,IAAIe,KAAK,EAAEH,CAAC,EAAEqD,OAAO;MACrBrD,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtCoD,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACI,QAAQ,CAACiE,MAAM,CAACtD,CAAC,GAAG,CAAC,CAAC;MAC5CG,KAAK,GAAG,IAAI,CAAClB,MAAM,CAAC2D,OAAO,CAACxD,KAAK,CAAC;MAClCyC,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAEgE,OAAO,CAAC;MACzD,OAAO,IAAI;IACb,CAAC;IAEDpF,OAAO,CAACwB,SAAS,CAACqD,GAAG,GAAG,UAAS1D,KAAK,EAAE;MACtC,IAAIe,KAAK;MACTA,KAAK,GAAG,IAAI/B,MAAM,CAAC,IAAI,EAAEgB,KAAK,CAAC;MAC/B,IAAI,CAACC,QAAQ,CAAC4B,IAAI,CAACd,KAAK,CAAC;MACzB,OAAO,IAAI;IACb,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAAC6C,KAAK,GAAG,YAAW;MACnC,IAAInC,KAAK;MACTA,KAAK,GAAG,IAAIrC,QAAQ,CAAC,IAAI,CAAC;MAC1B,OAAOqC,KAAK;IACd,CAAC;IAEDlC,OAAO,CAACwB,SAAS,CAACuD,WAAW,GAAG,UAASa,MAAM,EAAEzE,KAAK,EAAE;MACtD,IAAI0E,SAAS,EAAEC,QAAQ,EAAEf,WAAW,EAAE5C,CAAC,EAAEC,GAAG;MAC5C,IAAIwD,MAAM,IAAI,IAAI,EAAE;QAClBA,MAAM,GAAGvF,QAAQ,CAACuF,MAAM,CAAC;MAC3B;MACA,IAAIzE,KAAK,IAAI,IAAI,EAAE;QACjBA,KAAK,GAAGd,QAAQ,CAACc,KAAK,CAAC;MACzB;MACA,IAAIyC,KAAK,CAACC,OAAO,CAAC+B,MAAM,CAAC,EAAE;QACzB,KAAKzD,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGwD,MAAM,CAAC9D,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UAC7C0D,SAAS,GAAGD,MAAM,CAACzD,CAAC,CAAC;UACrB,IAAI,CAAC4C,WAAW,CAACc,SAAS,CAAC;QAC7B;MACF,CAAC,MAAM,IAAIrF,QAAQ,CAACoF,MAAM,CAAC,EAAE;QAC3B,KAAKC,SAAS,IAAID,MAAM,EAAE;UACxB,IAAI,CAAClF,OAAO,CAACqD,IAAI,CAAC6B,MAAM,EAAEC,SAAS,CAAC,EAAE;UACtCC,QAAQ,GAAGF,MAAM,CAACC,SAAS,CAAC;UAC5B,IAAI,CAACd,WAAW,CAACc,SAAS,EAAEC,QAAQ,CAAC;QACvC;MACF,CAAC,MAAM;QACL,IAAIvF,UAAU,CAACY,KAAK,CAAC,EAAE;UACrBA,KAAK,GAAGA,KAAK,CAAC2C,KAAK,CAAC,CAAC;QACvB;QACAiB,WAAW,GAAG,IAAI7E,wBAAwB,CAAC,IAAI,EAAE0F,MAAM,EAAEzE,KAAK,CAAC;QAC/D,IAAI,CAACC,QAAQ,CAAC4B,IAAI,CAAC+B,WAAW,CAAC;MACjC;MACA,OAAO,IAAI;IACb,CAAC;IAED/E,OAAO,CAACwB,SAAS,CAACuE,iBAAiB,GAAG,UAASH,MAAM,EAAEzE,KAAK,EAAE;MAC5D,IAAIe,KAAK,EAAEH,CAAC,EAAEqD,OAAO;MACrBrD,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtCoD,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACI,QAAQ,CAACiE,MAAM,CAACtD,CAAC,CAAC;MACxCG,KAAK,GAAG,IAAI,CAAClB,MAAM,CAAC+D,WAAW,CAACa,MAAM,EAAEzE,KAAK,CAAC;MAC9CyC,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAEgE,OAAO,CAAC;MACzD,OAAO,IAAI;IACb,CAAC;IAEDpF,OAAO,CAACwB,SAAS,CAACwE,gBAAgB,GAAG,UAASJ,MAAM,EAAEzE,KAAK,EAAE;MAC3D,IAAIe,KAAK,EAAEH,CAAC,EAAEqD,OAAO;MACrBrD,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtCoD,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACI,QAAQ,CAACiE,MAAM,CAACtD,CAAC,GAAG,CAAC,CAAC;MAC5CG,KAAK,GAAG,IAAI,CAAClB,MAAM,CAAC+D,WAAW,CAACa,MAAM,EAAEzE,KAAK,CAAC;MAC9CyC,KAAK,CAACpC,SAAS,CAACwB,IAAI,CAACc,KAAK,CAAC,IAAI,CAAC9C,MAAM,CAACI,QAAQ,EAAEgE,OAAO,CAAC;MACzD,OAAO,IAAI;IACb,CAAC;IAEDpF,OAAO,CAACwB,SAAS,CAACyE,WAAW,GAAG,UAASC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;MACtE,IAAIC,GAAG,EAAEC,MAAM;MACfD,GAAG,GAAG,IAAI,CAACpE,QAAQ,CAAC,CAAC;MACrBqE,MAAM,GAAG,IAAI3G,cAAc,CAAC0G,GAAG,EAAEH,OAAO,EAAEC,QAAQ,EAAEC,UAAU,CAAC;MAC/D,IAAIC,GAAG,CAACjF,QAAQ,CAACU,MAAM,KAAK,CAAC,EAAE;QAC7BuE,GAAG,CAACjF,QAAQ,CAACmF,OAAO,CAACD,MAAM,CAAC;MAC9B,CAAC,MAAM,IAAID,GAAG,CAACjF,QAAQ,CAAC,CAAC,CAAC,CAACO,IAAI,KAAKnC,QAAQ,CAACgH,WAAW,EAAE;QACxDH,GAAG,CAACjF,QAAQ,CAAC,CAAC,CAAC,GAAGkF,MAAM;MAC1B,CAAC,MAAM;QACLD,GAAG,CAACjF,QAAQ,CAACmF,OAAO,CAACD,MAAM,CAAC;MAC9B;MACA,OAAOD,GAAG,CAACI,IAAI,CAAC,CAAC,IAAIJ,GAAG;IAC1B,CAAC;IAEDrG,OAAO,CAACwB,SAAS,CAACkF,GAAG,GAAG,UAASC,KAAK,EAAEC,KAAK,EAAE;MAC7C,IAAI1E,KAAK,EAAEmE,GAAG,EAAEQ,OAAO,EAAE9E,CAAC,EAAEI,CAAC,EAAEmB,CAAC,EAAElB,GAAG,EAAEqB,IAAI,EAAEpB,IAAI,EAAEqB,IAAI;MACvD2C,GAAG,GAAG,IAAI,CAACpE,QAAQ,CAAC,CAAC;MACrB4E,OAAO,GAAG,IAAIjH,UAAU,CAACyG,GAAG,EAAEM,KAAK,EAAEC,KAAK,CAAC;MAC3CvE,IAAI,GAAGgE,GAAG,CAACjF,QAAQ;MACnB,KAAKW,CAAC,GAAGI,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACP,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAEL,CAAC,GAAG,EAAEI,CAAC,EAAE;QACnDD,KAAK,GAAGG,IAAI,CAACN,CAAC,CAAC;QACf,IAAIG,KAAK,CAACP,IAAI,KAAKnC,QAAQ,CAACsH,OAAO,EAAE;UACnCT,GAAG,CAACjF,QAAQ,CAACW,CAAC,CAAC,GAAG8E,OAAO;UACzB,OAAOA,OAAO;QAChB;MACF;MACAnD,IAAI,GAAG2C,GAAG,CAACjF,QAAQ;MACnB,KAAKW,CAAC,GAAGuB,CAAC,GAAG,CAAC,EAAEG,IAAI,GAAGC,IAAI,CAAC5B,MAAM,EAAEwB,CAAC,GAAGG,IAAI,EAAE1B,CAAC,GAAG,EAAEuB,CAAC,EAAE;QACrDpB,KAAK,GAAGwB,IAAI,CAAC3B,CAAC,CAAC;QACf,IAAIG,KAAK,CAACoD,MAAM,EAAE;UAChBe,GAAG,CAACjF,QAAQ,CAACiE,MAAM,CAACtD,CAAC,EAAE,CAAC,EAAE8E,OAAO,CAAC;UAClC,OAAOA,OAAO;QAChB;MACF;MACAR,GAAG,CAACjF,QAAQ,CAAC4B,IAAI,CAAC6D,OAAO,CAAC;MAC1B,OAAOA,OAAO;IAChB,CAAC;IAED7G,OAAO,CAACwB,SAAS,CAACuF,EAAE,GAAG,YAAW;MAChC,IAAI,IAAI,CAACzB,MAAM,EAAE;QACf,MAAM,IAAI1C,KAAK,CAAC,gFAAgF,CAAC;MACnG;MACA,OAAO,IAAI,CAAC5B,MAAM;IACpB,CAAC;IAEDhB,OAAO,CAACwB,SAAS,CAACiF,IAAI,GAAG,YAAW;MAClC,IAAIzB,IAAI;MACRA,IAAI,GAAG,IAAI;MACX,OAAOA,IAAI,EAAE;QACX,IAAIA,IAAI,CAACrD,IAAI,KAAKnC,QAAQ,CAACwH,QAAQ,EAAE;UACnC,OAAOhC,IAAI,CAACiC,UAAU;QACxB,CAAC,MAAM,IAAIjC,IAAI,CAACM,MAAM,EAAE;UACtB,OAAON,IAAI;QACb,CAAC,MAAM;UACLA,IAAI,GAAGA,IAAI,CAAChE,MAAM;QACpB;MACF;IACF,CAAC;IAEDhB,OAAO,CAACwB,SAAS,CAACS,QAAQ,GAAG,YAAW;MACtC,IAAI+C,IAAI;MACRA,IAAI,GAAG,IAAI;MACX,OAAOA,IAAI,EAAE;QACX,IAAIA,IAAI,CAACrD,IAAI,KAAKnC,QAAQ,CAACwH,QAAQ,EAAE;UACnC,OAAOhC,IAAI;QACb,CAAC,MAAM;UACLA,IAAI,GAAGA,IAAI,CAAChE,MAAM;QACpB;MACF;IACF,CAAC;IAEDhB,OAAO,CAACwB,SAAS,CAAC0F,GAAG,GAAG,UAASjG,OAAO,EAAE;MACxC,OAAO,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAACiF,GAAG,CAACjG,OAAO,CAAC;IACrC,CAAC;IAEDjB,OAAO,CAACwB,SAAS,CAAC2F,IAAI,GAAG,YAAW;MAClC,IAAIpF,CAAC;MACLA,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtC,IAAID,CAAC,GAAG,CAAC,EAAE;QACT,MAAM,IAAIa,KAAK,CAAC,6BAA6B,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MACnE;MACA,OAAO,IAAI,CAAC7B,MAAM,CAACI,QAAQ,CAACW,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED/B,OAAO,CAACwB,SAAS,CAAC4F,IAAI,GAAG,YAAW;MAClC,IAAIrF,CAAC;MACLA,CAAC,GAAG,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACY,OAAO,CAAC,IAAI,CAAC;MACtC,IAAID,CAAC,KAAK,CAAC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAACf,MAAM,CAACI,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE;QACrD,MAAM,IAAIc,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAClE;MACA,OAAO,IAAI,CAAC7B,MAAM,CAACI,QAAQ,CAACW,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED/B,OAAO,CAACwB,SAAS,CAAC6F,cAAc,GAAG,UAAShB,GAAG,EAAE;MAC/C,IAAIiB,UAAU;MACdA,UAAU,GAAGjB,GAAG,CAACI,IAAI,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC;MAC/BD,UAAU,CAACtG,MAAM,GAAG,IAAI;MACxBsG,UAAU,CAAChC,MAAM,GAAG,KAAK;MACzB,IAAI,CAAClE,QAAQ,CAAC4B,IAAI,CAACsE,UAAU,CAAC;MAC9B,OAAO,IAAI;IACb,CAAC;IAEDtH,OAAO,CAACwB,SAAS,CAACqB,SAAS,GAAG,UAASnB,IAAI,EAAE;MAC3C,IAAIW,IAAI,EAAEqB,IAAI;MACdhC,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACA,IAAI;MACxB,IAAKA,IAAI,IAAI,IAAI,IAAK,EAAE,CAACW,IAAI,GAAG,IAAI,CAACrB,MAAM,KAAK,IAAI,GAAGqB,IAAI,CAACX,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QAC1E,OAAO,EAAE;MACX,CAAC,MAAM,IAAIA,IAAI,IAAI,IAAI,EAAE;QACvB,OAAO,WAAW,GAAG,IAAI,CAACV,MAAM,CAACU,IAAI,GAAG,GAAG;MAC7C,CAAC,MAAM,IAAI,EAAE,CAACgC,IAAI,GAAG,IAAI,CAAC1C,MAAM,KAAK,IAAI,GAAG0C,IAAI,CAAChC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QAC/D,OAAO,SAAS,GAAGA,IAAI,GAAG,GAAG;MAC/B,CAAC,MAAM;QACL,OAAO,SAAS,GAAGA,IAAI,GAAG,cAAc,GAAG,IAAI,CAACV,MAAM,CAACU,IAAI,GAAG,GAAG;MACnE;IACF,CAAC;IAED1B,OAAO,CAACwB,SAAS,CAACgG,GAAG,GAAG,UAAS9F,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MACvD,OAAO,IAAI,CAACF,OAAO,CAACvB,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;IAC7C,CAAC;IAEDnD,OAAO,CAACwB,SAAS,CAACiG,GAAG,GAAG,UAAS/F,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MACvD,OAAO,IAAI,CAAC6B,IAAI,CAACtD,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;IAC1C,CAAC;IAEDnD,OAAO,CAACwB,SAAS,CAACkG,GAAG,GAAG,UAASvG,KAAK,EAAE;MACtC,OAAO,IAAI,CAACgC,IAAI,CAAChC,KAAK,CAAC;IACzB,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAACmG,GAAG,GAAG,UAASxG,KAAK,EAAE;MACtC,OAAO,IAAI,CAACsD,KAAK,CAACtD,KAAK,CAAC;IAC1B,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAACoG,GAAG,GAAG,UAASzG,KAAK,EAAE;MACtC,OAAO,IAAI,CAACwD,OAAO,CAACxD,KAAK,CAAC;IAC5B,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAACqG,GAAG,GAAG,UAASjC,MAAM,EAAEzE,KAAK,EAAE;MAC9C,OAAO,IAAI,CAAC4D,WAAW,CAACa,MAAM,EAAEzE,KAAK,CAAC;IACxC,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAAC6E,GAAG,GAAG,YAAW;MACjC,OAAO,IAAI,CAACpE,QAAQ,CAAC,CAAC;IACxB,CAAC;IAEDjC,OAAO,CAACwB,SAAS,CAACsG,GAAG,GAAG,UAAS5B,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAE;MAC9D,OAAO,IAAI,CAACH,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,CAAC;IACxD,CAAC;IAEDpG,OAAO,CAACwB,SAAS,CAACuG,CAAC,GAAG,UAASrG,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MACrD,OAAO,IAAI,CAACF,OAAO,CAACvB,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;IAC7C,CAAC;IAEDnD,OAAO,CAACwB,SAAS,CAACwG,CAAC,GAAG,UAAStG,IAAI,EAAEwB,UAAU,EAAEC,IAAI,EAAE;MACrD,OAAO,IAAI,CAAC6B,IAAI,CAACtD,IAAI,EAAEwB,UAAU,EAAEC,IAAI,CAAC;IAC1C,CAAC;IAEDnD,OAAO,CAACwB,SAAS,CAACyG,CAAC,GAAG,UAAS9G,KAAK,EAAE;MACpC,OAAO,IAAI,CAACgC,IAAI,CAAChC,KAAK,CAAC;IACzB,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAAC0G,CAAC,GAAG,UAAS/G,KAAK,EAAE;MACpC,OAAO,IAAI,CAACsD,KAAK,CAACtD,KAAK,CAAC;IAC1B,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAAC2G,CAAC,GAAG,UAAShH,KAAK,EAAE;MACpC,OAAO,IAAI,CAACwD,OAAO,CAACxD,KAAK,CAAC;IAC5B,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAAC4G,CAAC,GAAG,UAASjH,KAAK,EAAE;MACpC,OAAO,IAAI,CAAC0D,GAAG,CAAC1D,KAAK,CAAC;IACxB,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAACO,CAAC,GAAG,UAAS6D,MAAM,EAAEzE,KAAK,EAAE;MAC5C,OAAO,IAAI,CAAC4D,WAAW,CAACa,MAAM,EAAEzE,KAAK,CAAC;IACxC,CAAC;IAEDnB,OAAO,CAACwB,SAAS,CAAC6G,CAAC,GAAG,YAAW;MAC/B,OAAO,IAAI,CAACtB,EAAE,CAAC,CAAC;IAClB,CAAC;IAED/G,OAAO,CAACwB,SAAS,CAAC8G,gBAAgB,GAAG,UAASjC,GAAG,EAAE;MACjD,OAAO,IAAI,CAACgB,cAAc,CAAChB,GAAG,CAAC;IACjC,CAAC;IAEDrG,OAAO,CAACwB,SAAS,CAAC+G,YAAY,GAAG,UAASrD,QAAQ,EAAEsD,QAAQ,EAAE;MAC5D,MAAM,IAAI5F,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACiH,WAAW,GAAG,UAASD,QAAQ,EAAE;MACjD,MAAM,IAAI5F,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACkH,WAAW,GAAG,UAASxD,QAAQ,EAAE;MACjD,MAAM,IAAItC,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACmH,aAAa,GAAG,YAAW;MAC3C,OAAO,IAAI,CAACvH,QAAQ,CAACU,MAAM,KAAK,CAAC;IACnC,CAAC;IAED9B,OAAO,CAACwB,SAAS,CAACoH,SAAS,GAAG,UAASC,IAAI,EAAE;MAC3C,MAAM,IAAIjG,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACsH,SAAS,GAAG,YAAW;MACvC,MAAM,IAAIlG,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACuH,WAAW,GAAG,UAASC,OAAO,EAAE9C,OAAO,EAAE;MACzD,OAAO,IAAI;IACb,CAAC;IAEDlG,OAAO,CAACwB,SAAS,CAACyH,aAAa,GAAG,YAAW;MAC3C,OAAO,IAAI,CAACC,OAAO,CAACpH,MAAM,KAAK,CAAC;IAClC,CAAC;IAED9B,OAAO,CAACwB,SAAS,CAAC2H,uBAAuB,GAAG,UAASC,KAAK,EAAE;MAC1D,IAAIC,GAAG,EAAEC,GAAG;MACZD,GAAG,GAAG,IAAI;MACV,IAAIA,GAAG,KAAKD,KAAK,EAAE;QACjB,OAAO,CAAC;MACV,CAAC,MAAM,IAAI,IAAI,CAACnH,QAAQ,CAAC,CAAC,KAAKmH,KAAK,CAACnH,QAAQ,CAAC,CAAC,EAAE;QAC/CqH,GAAG,GAAG/J,gBAAgB,CAACgK,YAAY,GAAGhK,gBAAgB,CAACiK,sBAAsB;QAC7E,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE;UACvBJ,GAAG,IAAI/J,gBAAgB,CAACoK,SAAS;QACnC,CAAC,MAAM;UACLL,GAAG,IAAI/J,gBAAgB,CAACqK,SAAS;QACnC;QACA,OAAON,GAAG;MACZ,CAAC,MAAM,IAAID,GAAG,CAACQ,UAAU,CAACT,KAAK,CAAC,EAAE;QAChC,OAAO7J,gBAAgB,CAACuK,QAAQ,GAAGvK,gBAAgB,CAACoK,SAAS;MAC/D,CAAC,MAAM,IAAIN,GAAG,CAACU,YAAY,CAACX,KAAK,CAAC,EAAE;QAClC,OAAO7J,gBAAgB,CAACuK,QAAQ,GAAGvK,gBAAgB,CAACqK,SAAS;MAC/D,CAAC,MAAM,IAAIP,GAAG,CAACW,WAAW,CAACZ,KAAK,CAAC,EAAE;QACjC,OAAO7J,gBAAgB,CAACoK,SAAS;MACnC,CAAC,MAAM;QACL,OAAOpK,gBAAgB,CAACqK,SAAS;MACnC;IACF,CAAC;IAED5J,OAAO,CAACwB,SAAS,CAACyI,UAAU,GAAG,UAASb,KAAK,EAAE;MAC7C,MAAM,IAAIxG,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAAC0I,YAAY,GAAG,UAASC,YAAY,EAAE;MACtD,MAAM,IAAIvH,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAAC4I,kBAAkB,GAAG,UAASD,YAAY,EAAE;MAC5D,MAAM,IAAIvH,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAAC6I,kBAAkB,GAAG,UAASC,MAAM,EAAE;MACtD,MAAM,IAAI1H,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAAC+I,WAAW,GAAG,UAASvF,IAAI,EAAE;MAC7C,IAAIjD,CAAC,EAAEI,CAAC,EAAEE,IAAI;MACd,IAAI2C,IAAI,CAACzC,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;QACnC,OAAO,KAAK;MACd;MACA,IAAIyC,IAAI,CAAC5D,QAAQ,CAACU,MAAM,KAAK,IAAI,CAACV,QAAQ,CAACU,MAAM,EAAE;QACjD,OAAO,KAAK;MACd;MACA,KAAKC,CAAC,GAAGI,CAAC,GAAG,CAAC,EAAEE,IAAI,GAAG,IAAI,CAACjB,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE,CAAC,IAAIO,IAAI,GAAGF,CAAC,IAAIE,IAAI,GAAGF,CAAC,IAAIE,IAAI,EAAEN,CAAC,GAAG,CAAC,IAAIM,IAAI,GAAG,EAAEF,CAAC,GAAG,EAAEA,CAAC,EAAE;QAC7G,IAAI,CAAC,IAAI,CAACf,QAAQ,CAACW,CAAC,CAAC,CAACwI,WAAW,CAACvF,IAAI,CAAC5D,QAAQ,CAACW,CAAC,CAAC,CAAC,EAAE;UACnD,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC;IAED/B,OAAO,CAACwB,SAAS,CAACgJ,UAAU,GAAG,UAASxB,OAAO,EAAE9C,OAAO,EAAE;MACxD,MAAM,IAAItD,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACiJ,WAAW,GAAG,UAASlH,GAAG,EAAEmH,IAAI,EAAEC,OAAO,EAAE;MAC3D,MAAM,IAAI/H,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACoJ,WAAW,GAAG,UAASrH,GAAG,EAAE;MAC5C,MAAM,IAAIX,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED7C,OAAO,CAACwB,SAAS,CAACqJ,QAAQ,GAAG,UAASzB,KAAK,EAAE;MAC3C,IAAI,CAACA,KAAK,EAAE;QACV,OAAO,KAAK;MACd;MACA,OAAOA,KAAK,KAAK,IAAI,IAAI,IAAI,CAACW,YAAY,CAACX,KAAK,CAAC;IACnD,CAAC;IAEDpJ,OAAO,CAACwB,SAAS,CAACuI,YAAY,GAAG,UAAS/E,IAAI,EAAE;MAC9C,IAAI9C,KAAK,EAAE4I,iBAAiB,EAAE3I,CAAC,EAAEC,GAAG,EAAEC,IAAI;MAC1CA,IAAI,GAAG,IAAI,CAACjB,QAAQ;MACpB,KAAKe,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACP,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC3CD,KAAK,GAAGG,IAAI,CAACF,CAAC,CAAC;QACf,IAAI6C,IAAI,KAAK9C,KAAK,EAAE;UAClB,OAAO,IAAI;QACb;QACA4I,iBAAiB,GAAG5I,KAAK,CAAC6H,YAAY,CAAC/E,IAAI,CAAC;QAC5C,IAAI8F,iBAAiB,EAAE;UACrB,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd,CAAC;IAED9K,OAAO,CAACwB,SAAS,CAACqI,UAAU,GAAG,UAAS7E,IAAI,EAAE;MAC5C,OAAOA,IAAI,CAAC+E,YAAY,CAAC,IAAI,CAAC;IAChC,CAAC;IAED/J,OAAO,CAACwB,SAAS,CAACwI,WAAW,GAAG,UAAShF,IAAI,EAAE;MAC7C,IAAI+F,OAAO,EAAEC,OAAO;MACpBD,OAAO,GAAG,IAAI,CAACE,YAAY,CAACjG,IAAI,CAAC;MACjCgG,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC;MACjC,IAAIF,OAAO,KAAK,CAAC,CAAC,IAAIC,OAAO,KAAK,CAAC,CAAC,EAAE;QACpC,OAAO,KAAK;MACd,CAAC,MAAM;QACL,OAAOD,OAAO,GAAGC,OAAO;MAC1B;IACF,CAAC;IAEDhL,OAAO,CAACwB,SAAS,CAAC0J,WAAW,GAAG,UAASlG,IAAI,EAAE;MAC7C,IAAI+F,OAAO,EAAEC,OAAO;MACpBD,OAAO,GAAG,IAAI,CAACE,YAAY,CAACjG,IAAI,CAAC;MACjCgG,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC;MACjC,IAAIF,OAAO,KAAK,CAAC,CAAC,IAAIC,OAAO,KAAK,CAAC,CAAC,EAAE;QACpC,OAAO,KAAK;MACd,CAAC,MAAM;QACL,OAAOD,OAAO,GAAGC,OAAO;MAC1B;IACF,CAAC;IAEDhL,OAAO,CAACwB,SAAS,CAACyJ,YAAY,GAAG,UAASjG,IAAI,EAAE;MAC9C,IAAImG,KAAK,EAAEC,GAAG;MACdA,GAAG,GAAG,CAAC;MACPD,KAAK,GAAG,KAAK;MACb,IAAI,CAACE,eAAe,CAAC,IAAI,CAACpJ,QAAQ,CAAC,CAAC,EAAE,UAASmB,SAAS,EAAE;QACxDgI,GAAG,EAAE;QACL,IAAI,CAACD,KAAK,IAAI/H,SAAS,KAAK4B,IAAI,EAAE;UAChC,OAAOmG,KAAK,GAAG,IAAI;QACrB;MACF,CAAC,CAAC;MACF,IAAIA,KAAK,EAAE;QACT,OAAOC,GAAG;MACZ,CAAC,MAAM;QACL,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDpL,OAAO,CAACwB,SAAS,CAAC6J,eAAe,GAAG,UAASrG,IAAI,EAAEsG,IAAI,EAAE;MACvD,IAAIpJ,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEiH,GAAG;MAC5BtE,IAAI,KAAKA,IAAI,GAAG,IAAI,CAAC/C,QAAQ,CAAC,CAAC,CAAC;MAChCI,IAAI,GAAG2C,IAAI,CAAC5D,QAAQ;MACpB,KAAKe,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACP,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC3CD,KAAK,GAAGG,IAAI,CAACF,CAAC,CAAC;QACf,IAAImH,GAAG,GAAGgC,IAAI,CAACpJ,KAAK,CAAC,EAAE;UACrB,OAAOoH,GAAG;QACZ,CAAC,MAAM;UACLA,GAAG,GAAG,IAAI,CAAC+B,eAAe,CAACnJ,KAAK,EAAEoJ,IAAI,CAAC;UACvC,IAAIhC,GAAG,EAAE;YACP,OAAOA,GAAG;UACZ;QACF;MACF;IACF,CAAC;IAED,OAAOtJ,OAAO;EAEhB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAE+D,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}