{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\brsr_reports\\\\brsr-viewer\\\\src\\\\BRSRViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BRSRViewer() {\n  _s();\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({\n    Environment: [],\n    Social: [],\n    Governance: []\n  });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = e => {\n    console.log(\"File upload triggered\");\n    const file = e.target.files[0];\n    if (!file) {\n      console.log(\"No file selected\");\n      return;\n    }\n    console.log(\"File selected:\", file.name, file.type, file.size);\n    const reader = new FileReader();\n    reader.onload = evt => {\n      try {\n        console.log(\"File read successfully, parsing...\");\n        const workbook = XLSX.read(evt.target.result, {\n          type: \"binary\"\n        });\n        console.log(\"Workbook created, sheet names:\", workbook.SheetNames);\n        const sheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[sheetName];\n        const json = XLSX.utils.sheet_to_json(worksheet);\n        console.log(\"JSON data parsed:\", json.length, \"rows\");\n        console.log(\"Sample data:\", json.slice(0, 2));\n        setCompanyData(json);\n      } catch (error) {\n        console.error(\"Error parsing Excel file:\", error);\n        alert(\"Error parsing Excel file: \" + error.message);\n      }\n    };\n    reader.onerror = error => {\n      console.error(\"Error reading file:\", error);\n      alert(\"Error reading file\");\n    };\n    reader.readAsBinaryString(file);\n  };\n\n  /** Categorize field names **/\n  const categorizeField = name => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\")) return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\")) return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = xmlText => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach(ctx => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\"\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach(u => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = {\n      Environment: [],\n      Social: [],\n      Governance: []\n    };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach(element => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: (context === null || context === void 0 ? void 0 : context.entity) || \"\"\n        });\n      }\n    });\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async row => {\n    console.log(\"Company selected:\", row);\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({\n      Environment: [],\n      Social: [],\n      Governance: []\n    });\n    if (row[\"XML Link\"]) {\n      try {\n        console.log(\"Fetching XML from:\", row[\"XML Link\"]);\n        const res = await axios.get(row[\"XML Link\"]);\n        console.log(\"XML response received, length:\", res.data.length);\n        const categories = parseBRSRXML(res.data);\n        console.log(\"Parsed categories:\", categories);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n        alert(\"Error loading XML: \" + err.message);\n      } finally {\n        setLoadingSingle(false);\n      }\n    } else {\n      console.log(\"No XML Link found for this company\");\n      setLoadingSingle(false);\n      alert(\"No XML Link found for this company\");\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach(cat => {\n            categories[cat].forEach(d => allData.push({\n              ...d,\n              category: cat,\n              companyName: row[\"Company Name\"],\n              year: row[\"Year\"]\n            }));\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(row => row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) || row.value.toLowerCase().includes(searchBRSR.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        fontSize: '2rem',\n        fontWeight: 'bold',\n        color: '#333',\n        marginBottom: '24px',\n        textAlign: 'center'\n      },\n      children: \"\\uD83D\\uDCCA BRSR Report Viewer (Excel + XML)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".xlsx,.xls\",\n        onChange: handleExcelUpload,\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), companyData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px',\n          color: '#28a745',\n          fontWeight: '500'\n        },\n        children: [\"\\u2705 \", companyData.length, \" companies loaded\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            // Add sample data for testing\n            const sampleData = [{\n              \"Company Name\": \"Sample Corp Ltd\",\n              \"Year\": \"2023\",\n              \"XML Link\": \"https://httpbin.org/xml\" // This returns sample XML for testing\n            }, {\n              \"Company Name\": \"Test Industries\",\n              \"Year\": \"2023\",\n              \"XML Link\": \"https://httpbin.org/xml\"\n            }];\n            setCompanyData(sampleData);\n            console.log(\"Sample data loaded:\", sampleData);\n          },\n          style: {\n            padding: '8px 16px',\n            backgroundColor: '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          },\n          children: \"Load Sample Data (for testing)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '16px',\n        marginBottom: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Search company...\",\n        value: searchCompany,\n        onChange: e => setSearchCompany(e.target.value),\n        style: {\n          width: '320px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchAllCompanies,\n        disabled: loadingAll || companyData.length === 0,\n        style: {\n          padding: '12px 16px',\n          borderRadius: '8px',\n          border: 'none',\n          color: 'white',\n          backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n          cursor: loadingAll ? 'not-allowed' : 'pointer',\n          opacity: loadingAll ? 0.6 : 1\n        },\n        children: loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), companyData.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '16px',\n        marginBottom: '32px'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataTable, {\n        title: `Companies (${companyData.length} loaded)`,\n        columns: [{\n          name: \"Company\",\n          selector: row => row[\"Company Name\"],\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => row[\"Year\"],\n          sortable: true\n        }, {\n          name: \"Actions\",\n          cell: row => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCompanySelect(row),\n            style: {\n              backgroundColor: '#007bff',\n              color: 'white',\n              padding: '6px 12px',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            },\n            children: \"View BRSR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 19\n          }, this)\n        }],\n        data: companyData.filter(r => {\n          var _r$CompanyName;\n          return (_r$CompanyName = r[\"Company Name\"]) === null || _r$CompanyName === void 0 ? void 0 : _r$CompanyName.toLowerCase().includes(searchCompany.toLowerCase());\n        }),\n        pagination: true,\n        highlightOnHover: true,\n        dense: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '32px',\n        marginBottom: '32px',\n        textAlign: 'center',\n        color: '#6c757d'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\uD83D\\uDCC1 No companies loaded yet. Please upload an Excel file to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '14px',\n          marginTop: '8px'\n        },\n        children: \"Expected columns: Company Name, Year, XML Link\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this), unifiedData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px',\n        marginBottom: '32px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: \"Unified BRSR Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Company\",\n          selector: row => row.companyName,\n          sortable: true\n        }, {\n          name: \"Year\",\n          selector: row => row.year,\n          sortable: true\n        }, {\n          name: \"Category\",\n          selector: row => row.category,\n          sortable: true\n        }, {\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }],\n        data: unifiedData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 9\n    }, this), selectedCompany && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n        padding: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          marginBottom: '16px'\n        },\n        children: [selectedCompany[\"Company Name\"], \" (\", selectedCompany[\"Year\"], \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '16px',\n          marginBottom: '16px'\n        },\n        children: [\"Environment\", \"Social\", \"Governance\"].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab),\n          style: {\n            padding: '8px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            fontWeight: '500',\n            cursor: 'pointer',\n            backgroundColor: activeTab === tab ? tab === \"Environment\" ? \"#28a745\" : tab === \"Social\" ? \"#6f42c1\" : \"#fd7e14\" : \"#e9ecef\",\n            color: activeTab === tab ? \"white\" : \"#495057\"\n          },\n          children: tab\n        }, tab, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: `Search in ${activeTab}...`,\n          value: searchBRSR,\n          onChange: e => setSearchBRSR(e.target.value),\n          style: {\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this), loadingSingle ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '40px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2rem',\n            color: '#007bff'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(DataTable, {\n        columns: [{\n          name: \"Metric\",\n          selector: row => row.metric,\n          sortable: true\n        }, {\n          name: \"Value\",\n          selector: row => row.value,\n          sortable: true\n        }, {\n          name: \"Unit\",\n          selector: row => row.unit || \"-\",\n          sortable: true\n        }, {\n          name: \"Period\",\n          selector: row => row.period,\n          sortable: true\n        }],\n        data: filteredTabData,\n        pagination: true,\n        dense: true,\n        highlightOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n}\n_s(BRSRViewer, \"XqWeNAlGtEHiedqF0JxGSKVABwY=\");\n_c = BRSRViewer;\nvar _c;\n$RefreshReg$(_c, \"BRSRViewer\");", "map": {"version": 3, "names": ["React", "useState", "XLSX", "axios", "DataTable", "jsxDEV", "_jsxDEV", "BRSRViewer", "_s", "companyData", "setCompanyData", "searchCompany", "setSearchCompany", "selectedCompany", "setSelectedCompany", "brsrData", "setBrsrData", "Environment", "Social", "Governance", "searchBRSR", "setSearchBRSR", "activeTab", "setActiveTab", "unifiedData", "setUnifiedData", "loadingAll", "setLoadingAll", "loadingSingle", "setLoading<PERSON>ingle", "handleExcelUpload", "e", "console", "log", "file", "target", "files", "name", "type", "size", "reader", "FileReader", "onload", "evt", "workbook", "read", "result", "SheetNames", "sheetName", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "length", "slice", "error", "alert", "message", "onerror", "readAsBinaryString", "categorizeField", "lower", "toLowerCase", "includes", "parseBRSRXML", "xmlText", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "contexts", "units", "contextElements", "getElementsByTagName", "Array", "from", "for<PERSON>ach", "ctx", "id", "getAttribute", "entityElement", "startDateElement", "endDateElement", "entity", "textContent", "startDate", "endDate", "unitElements", "u", "measureElement", "categories", "allElements", "element", "tagName", "startsWith", "contextRef", "unitRef", "context", "unit", "category", "push", "metric", "replace", "value", "period", "company", "handleCompanySelect", "row", "res", "get", "data", "err", "fetchAllCompanies", "allData", "cat", "d", "companyName", "year", "warn", "filteredTabData", "filter", "style", "padding", "backgroundColor", "minHeight", "children", "fontSize", "fontWeight", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "alignItems", "accept", "onChange", "width", "border", "borderRadius", "boxShadow", "marginTop", "onClick", "sampleData", "cursor", "justifyContent", "gap", "placeholder", "disabled", "opacity", "title", "columns", "selector", "sortable", "cell", "r", "_r$CompanyName", "pagination", "highlightOnHover", "dense", "map", "tab", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/src/BRSRViewer.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport DataTable from \"react-data-table-component\";\n\nexport default function BRSRViewer() {\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = (e) => {\n    console.log(\"File upload triggered\");\n    const file = e.target.files[0];\n    if (!file) {\n      console.log(\"No file selected\");\n      return;\n    }\n\n    console.log(\"File selected:\", file.name, file.type, file.size);\n\n    const reader = new FileReader();\n    reader.onload = (evt) => {\n      try {\n        console.log(\"File read successfully, parsing...\");\n        const workbook = XLSX.read(evt.target.result, { type: \"binary\" });\n        console.log(\"Workbook created, sheet names:\", workbook.SheetNames);\n\n        const sheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[sheetName];\n        const json = XLSX.utils.sheet_to_json(worksheet);\n\n        console.log(\"JSON data parsed:\", json.length, \"rows\");\n        console.log(\"Sample data:\", json.slice(0, 2));\n\n        setCompanyData(json);\n      } catch (error) {\n        console.error(\"Error parsing Excel file:\", error);\n        alert(\"Error parsing Excel file: \" + error.message);\n      }\n    };\n\n    reader.onerror = (error) => {\n      console.error(\"Error reading file:\", error);\n      alert(\"Error reading file\");\n    };\n\n    reader.readAsBinaryString(file);\n  };\n\n  /** Categorize field names **/\n  const categorizeField = (name) => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\"))\n      return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\"))\n      return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = (xmlText) => {\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(xmlText, \"text/xml\");\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextElements = xmlDoc.getElementsByTagName(\"xbrli:context\");\n    Array.from(contextElements).forEach((ctx) => {\n      const id = ctx.getAttribute(\"id\");\n      const entityElement = ctx.getElementsByTagName(\"xbrli:identifier\")[0];\n      const startDateElement = ctx.getElementsByTagName(\"xbrli:startDate\")[0];\n      const endDateElement = ctx.getElementsByTagName(\"xbrli:endDate\")[0];\n\n      contexts[id] = {\n        entity: entityElement ? entityElement.textContent : \"\",\n        startDate: startDateElement ? startDateElement.textContent : \"\",\n        endDate: endDateElement ? endDateElement.textContent : \"\",\n      };\n    });\n\n    // Parse units\n    const unitElements = xmlDoc.getElementsByTagName(\"xbrli:unit\");\n    Array.from(unitElements).forEach((u) => {\n      const id = u.getAttribute(\"id\");\n      const measureElement = u.getElementsByTagName(\"xbrli:measure\")[0];\n      units[id] = measureElement ? measureElement.textContent : \"\";\n    });\n\n    // Group data into categories\n    const categories = { Environment: [], Social: [], Governance: [] };\n\n    // Get all elements that start with \"in-capmkt:\"\n    const allElements = xmlDoc.getElementsByTagName(\"*\");\n    Array.from(allElements).forEach((element) => {\n      const tagName = element.tagName;\n      if (tagName && tagName.startsWith(\"in-capmkt:\")) {\n        const contextRef = element.getAttribute(\"contextRef\");\n        const unitRef = element.getAttribute(\"unitRef\");\n        const context = contexts[contextRef];\n        const unit = units[unitRef];\n        const category = categorizeField(tagName);\n\n        categories[category].push({\n          metric: tagName.replace(\"in-capmkt:\", \"\"),\n          value: element.textContent || \"\",\n          unit: unit || null,\n          period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n          company: context?.entity || \"\",\n        });\n      }\n    });\n\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async (row) => {\n    console.log(\"Company selected:\", row);\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({ Environment: [], Social: [], Governance: [] });\n\n    if (row[\"XML Link\"]) {\n      try {\n        console.log(\"Fetching XML from:\", row[\"XML Link\"]);\n        const res = await axios.get(row[\"XML Link\"]);\n        console.log(\"XML response received, length:\", res.data.length);\n\n        const categories = parseBRSRXML(res.data);\n        console.log(\"Parsed categories:\", categories);\n\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n        alert(\"Error loading XML: \" + err.message);\n      } finally {\n        setLoadingSingle(false);\n      }\n    } else {\n      console.log(\"No XML Link found for this company\");\n      setLoadingSingle(false);\n      alert(\"No XML Link found for this company\");\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach((cat) => {\n            categories[cat].forEach((d) =>\n              allData.push({\n                ...d,\n                category: cat,\n                companyName: row[\"Company Name\"],\n                year: row[\"Year\"],\n              })\n            );\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(\n    (row) =>\n      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||\n      row.value.toLowerCase().includes(searchBRSR.toLowerCase())\n  );\n\n  return (\n    <div style={{ padding: '24px', backgroundColor: '#f8f9fa', minHeight: '100vh' }}>\n      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333', marginBottom: '24px', textAlign: 'center' }}>\n        📊 BRSR Report Viewer (Excel + XML)\n      </h1>\n\n      {/* Upload Excel */}\n      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '24px' }}>\n        <input\n          type=\"file\"\n          accept=\".xlsx,.xls\"\n          onChange={handleExcelUpload}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        {companyData.length > 0 && (\n          <div style={{ marginTop: '8px', color: '#28a745', fontWeight: '500' }}>\n            ✅ {companyData.length} companies loaded\n          </div>\n        )}\n        <div style={{ marginTop: '8px' }}>\n          <button\n            onClick={() => {\n              // Add sample data for testing\n              const sampleData = [\n                {\n                  \"Company Name\": \"Sample Corp Ltd\",\n                  \"Year\": \"2023\",\n                  \"XML Link\": \"https://httpbin.org/xml\" // This returns sample XML for testing\n                },\n                {\n                  \"Company Name\": \"Test Industries\",\n                  \"Year\": \"2023\",\n                  \"XML Link\": \"https://httpbin.org/xml\"\n                }\n              ];\n              setCompanyData(sampleData);\n              console.log(\"Sample data loaded:\", sampleData);\n            }}\n            style={{\n              padding: '8px 16px',\n              backgroundColor: '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            }}\n          >\n            Load Sample Data (for testing)\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Fetch All */}\n      <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', marginBottom: '16px' }}>\n        <input\n          type=\"text\"\n          placeholder=\"Search company...\"\n          value={searchCompany}\n          onChange={(e) => setSearchCompany(e.target.value)}\n          style={{\n            width: '320px',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }}\n        />\n        <button\n          onClick={fetchAllCompanies}\n          disabled={loadingAll || companyData.length === 0}\n          style={{\n            padding: '12px 16px',\n            borderRadius: '8px',\n            border: 'none',\n            color: 'white',\n            backgroundColor: loadingAll ? '#6c757d' : '#28a745',\n            cursor: loadingAll ? 'not-allowed' : 'pointer',\n            opacity: loadingAll ? 0.6 : 1\n          }}\n        >\n          {loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"}\n        </button>\n      </div>\n\n      {/* Company Table */}\n      {companyData.length > 0 ? (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '16px',\n          marginBottom: '32px'\n        }}>\n          <DataTable\n            title={`Companies (${companyData.length} loaded)`}\n            columns={[\n              { name: \"Company\", selector: (row) => row[\"Company Name\"], sortable: true },\n              { name: \"Year\", selector: (row) => row[\"Year\"], sortable: true },\n              {\n                name: \"Actions\",\n                cell: (row) => (\n                  <button\n                    onClick={() => handleCompanySelect(row)}\n                    style={{\n                      backgroundColor: '#007bff',\n                      color: 'white',\n                      padding: '6px 12px',\n                      border: 'none',\n                      borderRadius: '4px',\n                      cursor: 'pointer',\n                      fontSize: '14px'\n                    }}\n                  >\n                    View BRSR\n                  </button>\n                ),\n              },\n            ]}\n            data={companyData.filter((r) =>\n              r[\"Company Name\"]?.toLowerCase().includes(searchCompany.toLowerCase())\n            )}\n            pagination\n            highlightOnHover\n            dense\n          />\n        </div>\n      ) : (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '32px',\n          marginBottom: '32px',\n          textAlign: 'center',\n          color: '#6c757d'\n        }}>\n          <p>📁 No companies loaded yet. Please upload an Excel file to get started.</p>\n          <p style={{ fontSize: '14px', marginTop: '8px' }}>\n            Expected columns: Company Name, Year, XML Link\n          </p>\n        </div>\n      )}\n\n      {/* Unified Data Table */}\n      {unifiedData.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px',\n          marginBottom: '32px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>Unified BRSR Data</h2>\n          <DataTable\n            columns={[\n              { name: \"Company\", selector: (row) => row.companyName, sortable: true },\n              { name: \"Year\", selector: (row) => row.year, sortable: true },\n              { name: \"Category\", selector: (row) => row.category, sortable: true },\n              { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n              { name: \"Value\", selector: (row) => row.value, sortable: true },\n            ]}\n            data={unifiedData}\n            pagination\n            dense\n            highlightOnHover\n          />\n        </div>\n      )}\n\n      {/* Tabs for single company */}\n      {selectedCompany && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          padding: '24px'\n        }}>\n          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '16px' }}>\n            {selectedCompany[\"Company Name\"]} ({selectedCompany[\"Year\"]})\n          </h2>\n\n          {/* Tabs */}\n          <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>\n            {[\"Environment\", \"Social\", \"Governance\"].map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                style={{\n                  padding: '8px 16px',\n                  borderRadius: '8px',\n                  border: 'none',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  backgroundColor: activeTab === tab\n                    ? tab === \"Environment\"\n                      ? \"#28a745\"\n                      : tab === \"Social\"\n                      ? \"#6f42c1\"\n                      : \"#fd7e14\"\n                    : \"#e9ecef\",\n                  color: activeTab === tab ? \"white\" : \"#495057\"\n                }}\n              >\n                {tab}\n              </button>\n            ))}\n          </div>\n\n          {/* Search */}\n          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>\n            <input\n              type=\"text\"\n              placeholder={`Search in ${activeTab}...`}\n              value={searchBRSR}\n              onChange={(e) => setSearchBRSR(e.target.value)}\n              style={{\n                width: '320px',\n                padding: '12px',\n                border: '1px solid #ddd',\n                borderRadius: '8px',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }}\n            />\n          </div>\n\n          {/* Table */}\n          {loadingSingle ? (\n            <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>\n              <span style={{ fontSize: '2rem', color: '#007bff' }}>⏳</span>\n            </div>\n          ) : (\n            <DataTable\n              columns={[\n                { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n                { name: \"Value\", selector: (row) => row.value, sortable: true },\n                { name: \"Unit\", selector: (row) => row.unit || \"-\", sortable: true },\n                { name: \"Period\", selector: (row) => row.period, sortable: true },\n              ]}\n              data={filteredTabData}\n              pagination\n              dense\n              highlightOnHover\n            />\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IAAEgB,WAAW,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC;EACzF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC/BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,MAAMC,IAAI,GAAGH,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;MACTF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,IAAI,CAACG,IAAI,EAAEH,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,IAAI,CAAC;IAE9D,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,GAAG,IAAK;MACvB,IAAI;QACFX,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAMW,QAAQ,GAAG1C,IAAI,CAAC2C,IAAI,CAACF,GAAG,CAACR,MAAM,CAACW,MAAM,EAAE;UAAER,IAAI,EAAE;QAAS,CAAC,CAAC;QACjEN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEW,QAAQ,CAACG,UAAU,CAAC;QAElE,MAAMC,SAAS,GAAGJ,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;QACxC,MAAME,SAAS,GAAGL,QAAQ,CAACM,MAAM,CAACF,SAAS,CAAC;QAC5C,MAAMG,IAAI,GAAGjD,IAAI,CAACkD,KAAK,CAACC,aAAa,CAACJ,SAAS,CAAC;QAEhDjB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkB,IAAI,CAACG,MAAM,EAAE,MAAM,CAAC;QACrDtB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkB,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C7C,cAAc,CAACyC,IAAI,CAAC;MACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdxB,OAAO,CAACwB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDC,KAAK,CAAC,4BAA4B,GAAGD,KAAK,CAACE,OAAO,CAAC;MACrD;IACF,CAAC;IAEDlB,MAAM,CAACmB,OAAO,GAAIH,KAAK,IAAK;MAC1BxB,OAAO,CAACwB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,KAAK,CAAC,oBAAoB,CAAC;IAC7B,CAAC;IAEDjB,MAAM,CAACoB,kBAAkB,CAAC1B,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAM2B,eAAe,GAAIxB,IAAI,IAAK;IAChC,MAAMyB,KAAK,GAAGzB,IAAI,CAAC0B,WAAW,CAAC,CAAC;IAChC,IAAID,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,EAC9G,OAAO,aAAa;IACtB,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAC,EAChH,OAAO,QAAQ;IACjB,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;IAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,OAAO,EAAE,UAAU,CAAC;IAC1D,MAAMK,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,MAAMC,eAAe,GAAGJ,MAAM,CAACK,oBAAoB,CAAC,eAAe,CAAC;IACpEC,KAAK,CAACC,IAAI,CAACH,eAAe,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;MAC3C,MAAMC,EAAE,GAAGD,GAAG,CAACE,YAAY,CAAC,IAAI,CAAC;MACjC,MAAMC,aAAa,GAAGH,GAAG,CAACJ,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACrE,MAAMQ,gBAAgB,GAAGJ,GAAG,CAACJ,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMS,cAAc,GAAGL,GAAG,CAACJ,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MAEnEH,QAAQ,CAACQ,EAAE,CAAC,GAAG;QACbK,MAAM,EAAEH,aAAa,GAAGA,aAAa,CAACI,WAAW,GAAG,EAAE;QACtDC,SAAS,EAAEJ,gBAAgB,GAAGA,gBAAgB,CAACG,WAAW,GAAG,EAAE;QAC/DE,OAAO,EAAEJ,cAAc,GAAGA,cAAc,CAACE,WAAW,GAAG;MACzD,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMG,YAAY,GAAGnB,MAAM,CAACK,oBAAoB,CAAC,YAAY,CAAC;IAC9DC,KAAK,CAACC,IAAI,CAACY,YAAY,CAAC,CAACX,OAAO,CAAEY,CAAC,IAAK;MACtC,MAAMV,EAAE,GAAGU,CAAC,CAACT,YAAY,CAAC,IAAI,CAAC;MAC/B,MAAMU,cAAc,GAAGD,CAAC,CAACf,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACjEF,KAAK,CAACO,EAAE,CAAC,GAAGW,cAAc,GAAGA,cAAc,CAACL,WAAW,GAAG,EAAE;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,UAAU,GAAG;MAAE1E,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;;IAElE;IACA,MAAMyE,WAAW,GAAGvB,MAAM,CAACK,oBAAoB,CAAC,GAAG,CAAC;IACpDC,KAAK,CAACC,IAAI,CAACgB,WAAW,CAAC,CAACf,OAAO,CAAEgB,OAAO,IAAK;MAC3C,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO;MAC/B,IAAIA,OAAO,IAAIA,OAAO,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC/C,MAAMC,UAAU,GAAGH,OAAO,CAACb,YAAY,CAAC,YAAY,CAAC;QACrD,MAAMiB,OAAO,GAAGJ,OAAO,CAACb,YAAY,CAAC,SAAS,CAAC;QAC/C,MAAMkB,OAAO,GAAG3B,QAAQ,CAACyB,UAAU,CAAC;QACpC,MAAMG,IAAI,GAAG3B,KAAK,CAACyB,OAAO,CAAC;QAC3B,MAAMG,QAAQ,GAAGvC,eAAe,CAACiC,OAAO,CAAC;QAEzCH,UAAU,CAACS,QAAQ,CAAC,CAACC,IAAI,CAAC;UACxBC,MAAM,EAAER,OAAO,CAACS,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;UACzCC,KAAK,EAAEX,OAAO,CAACR,WAAW,IAAI,EAAE;UAChCc,IAAI,EAAEA,IAAI,IAAI,IAAI;UAClBM,MAAM,EAAEP,OAAO,GAAG,GAAGA,OAAO,CAACZ,SAAS,MAAMY,OAAO,CAACX,OAAO,EAAE,GAAG,EAAE;UAClEmB,OAAO,EAAE,CAAAR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEd,MAAM,KAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMgB,mBAAmB,GAAG,MAAOC,GAAG,IAAK;IACzC5E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2E,GAAG,CAAC;IACrC9F,kBAAkB,CAAC8F,GAAG,CAAC;IACvB/E,gBAAgB,CAAC,IAAI,CAAC;IACtBb,WAAW,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC,CAAC;IAE5D,IAAIyF,GAAG,CAAC,UAAU,CAAC,EAAE;MACnB,IAAI;QACF5E,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2E,GAAG,CAAC,UAAU,CAAC,CAAC;QAClD,MAAMC,GAAG,GAAG,MAAM1G,KAAK,CAAC2G,GAAG,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C5E,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4E,GAAG,CAACE,IAAI,CAACzD,MAAM,CAAC;QAE9D,MAAMqC,UAAU,GAAG1B,YAAY,CAAC4C,GAAG,CAACE,IAAI,CAAC;QACzC/E,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0D,UAAU,CAAC;QAE7C3E,WAAW,CAAC2E,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOqB,GAAG,EAAE;QACZhF,OAAO,CAACwB,KAAK,CAAC,oBAAoB,EAAEwD,GAAG,CAAC;QACxCvD,KAAK,CAAC,qBAAqB,GAAGuD,GAAG,CAACtD,OAAO,CAAC;MAC5C,CAAC,SAAS;QACR7B,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDJ,gBAAgB,CAAC,KAAK,CAAC;MACvB4B,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMwD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCtF,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMuF,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMN,GAAG,IAAInG,WAAW,EAAE;MAC7B,IAAImG,GAAG,CAAC,UAAU,CAAC,EAAE;QACnB,IAAI;UACF,MAAMC,GAAG,GAAG,MAAM1G,KAAK,CAAC2G,GAAG,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;UAC5C,MAAMjB,UAAU,GAAG1B,YAAY,CAAC4C,GAAG,CAACE,IAAI,CAAC;UACzC,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAClC,OAAO,CAAEsC,GAAG,IAAK;YACvDxB,UAAU,CAACwB,GAAG,CAAC,CAACtC,OAAO,CAAEuC,CAAC,IACxBF,OAAO,CAACb,IAAI,CAAC;cACX,GAAGe,CAAC;cACJhB,QAAQ,EAAEe,GAAG;cACbE,WAAW,EAAET,GAAG,CAAC,cAAc,CAAC;cAChCU,IAAI,EAAEV,GAAG,CAAC,MAAM;YAClB,CAAC,CACH,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZhF,OAAO,CAACuF,IAAI,CAAC,kBAAkB,EAAEX,GAAG,CAAC,cAAc,CAAC,CAAC;QACvD;MACF;IACF;IACAnF,cAAc,CAACyF,OAAO,CAAC;IACvBvF,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAM6F,eAAe,GAAGzG,QAAQ,CAACO,SAAS,CAAC,CAACmG,MAAM,CAC/Cb,GAAG,IACFA,GAAG,CAACN,MAAM,CAACvC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5C,UAAU,CAAC2C,WAAW,CAAC,CAAC,CAAC,IAC3D6C,GAAG,CAACJ,KAAK,CAACzC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5C,UAAU,CAAC2C,WAAW,CAAC,CAAC,CAC7D,CAAC;EAED,oBACEzD,OAAA;IAAKoH,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC9ExH,OAAA;MAAIoH,KAAK,EAAE;QAAEK,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,YAAY,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAE/G;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLjI,OAAA;MAAKoH,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAER,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACnGxH,OAAA;QACEgC,IAAI,EAAC,MAAM;QACXqG,MAAM,EAAC,YAAY;QACnBC,QAAQ,EAAE9G,iBAAkB;QAC5B4F,KAAK,EAAE;UACLmB,KAAK,EAAE,OAAO;UACdlB,OAAO,EAAE,MAAM;UACfmB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD9H,WAAW,CAAC6C,MAAM,GAAG,CAAC,iBACrBhD,OAAA;QAAKoH,KAAK,EAAE;UAAEuB,SAAS,EAAE,KAAK;UAAEhB,KAAK,EAAE,SAAS;UAAED,UAAU,EAAE;QAAM,CAAE;QAAAF,QAAA,GAAC,SACnE,EAACrH,WAAW,CAAC6C,MAAM,EAAC,mBACxB;MAAA;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eACDjI,OAAA;QAAKoH,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,eAC/BxH,OAAA;UACE4I,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,MAAMC,UAAU,GAAG,CACjB;cACE,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,MAAM;cACd,UAAU,EAAE,yBAAyB,CAAC;YACxC,CAAC,EACD;cACE,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,MAAM;cACd,UAAU,EAAE;YACd,CAAC,CACF;YACDzI,cAAc,CAACyI,UAAU,CAAC;YAC1BnH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkH,UAAU,CAAC;UAChD,CAAE;UACFzB,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBC,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACda,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBK,MAAM,EAAE,SAAS;YACjBrB,QAAQ,EAAE;UACZ,CAAE;UAAAD,QAAA,EACH;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjI,OAAA;MAAKoH,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEa,cAAc,EAAE,QAAQ;QAAEC,GAAG,EAAE,MAAM;QAAEpB,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAC3FxH,OAAA;QACEgC,IAAI,EAAC,MAAM;QACXiH,WAAW,EAAC,mBAAmB;QAC/B/C,KAAK,EAAE7F,aAAc;QACrBiI,QAAQ,EAAG7G,CAAC,IAAKnB,gBAAgB,CAACmB,CAAC,CAACI,MAAM,CAACqE,KAAK,CAAE;QAClDkB,KAAK,EAAE;UACLmB,KAAK,EAAE,OAAO;UACdlB,OAAO,EAAE,MAAM;UACfmB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFjI,OAAA;QACE4I,OAAO,EAAEjC,iBAAkB;QAC3BuC,QAAQ,EAAE9H,UAAU,IAAIjB,WAAW,CAAC6C,MAAM,KAAK,CAAE;QACjDoE,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBoB,YAAY,EAAE,KAAK;UACnBD,MAAM,EAAE,MAAM;UACdb,KAAK,EAAE,OAAO;UACdL,eAAe,EAAElG,UAAU,GAAG,SAAS,GAAG,SAAS;UACnD0H,MAAM,EAAE1H,UAAU,GAAG,aAAa,GAAG,SAAS;UAC9C+H,OAAO,EAAE/H,UAAU,GAAG,GAAG,GAAG;QAC9B,CAAE;QAAAoG,QAAA,EAEDpG,UAAU,GAAG,iBAAiB,GAAG;MAAqB;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL9H,WAAW,CAAC6C,MAAM,GAAG,CAAC,gBACrBhD,OAAA;MAAKoH,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,eACAxH,OAAA,CAACF,SAAS;QACRsJ,KAAK,EAAE,cAAcjJ,WAAW,CAAC6C,MAAM,UAAW;QAClDqG,OAAO,EAAE,CACP;UAAEtH,IAAI,EAAE,SAAS;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAAC,cAAc,CAAC;UAAEiD,QAAQ,EAAE;QAAK,CAAC,EAC3E;UAAExH,IAAI,EAAE,MAAM;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAAC,MAAM,CAAC;UAAEiD,QAAQ,EAAE;QAAK,CAAC,EAChE;UACExH,IAAI,EAAE,SAAS;UACfyH,IAAI,EAAGlD,GAAG,iBACRtG,OAAA;YACE4I,OAAO,EAAEA,CAAA,KAAMvC,mBAAmB,CAACC,GAAG,CAAE;YACxCc,KAAK,EAAE;cACLE,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,OAAO;cACdN,OAAO,EAAE,UAAU;cACnBmB,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE,SAAS;cACjBrB,QAAQ,EAAE;YACZ,CAAE;YAAAD,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAEZ,CAAC,CACD;QACFxB,IAAI,EAAEtG,WAAW,CAACgH,MAAM,CAAEsC,CAAC;UAAA,IAAAC,cAAA;UAAA,QAAAA,cAAA,GACzBD,CAAC,CAAC,cAAc,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmBjG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrD,aAAa,CAACoD,WAAW,CAAC,CAAC,CAAC;QAAA,CACxE,CAAE;QACFkG,UAAU;QACVC,gBAAgB;QAChBC,KAAK;MAAA;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENjI,OAAA;MAAKoH,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE;MACT,CAAE;MAAAH,QAAA,gBACAxH,OAAA;QAAAwH,QAAA,EAAG;MAAuE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9EjI,OAAA;QAAGoH,KAAK,EAAE;UAAEK,QAAQ,EAAE,MAAM;UAAEkB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,EAAC;MAElD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGA/G,WAAW,CAAC8B,MAAM,GAAG,CAAC,iBACrBhD,OAAA;MAAKoH,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,gBACAxH,OAAA;QAAIoH,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClGjI,OAAA,CAACF,SAAS;QACRuJ,OAAO,EAAE,CACP;UAAEtH,IAAI,EAAE,SAAS;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACS,WAAW;UAAEwC,QAAQ,EAAE;QAAK,CAAC,EACvE;UAAExH,IAAI,EAAE,MAAM;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACU,IAAI;UAAEuC,QAAQ,EAAE;QAAK,CAAC,EAC7D;UAAExH,IAAI,EAAE,UAAU;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACR,QAAQ;UAAEyD,QAAQ,EAAE;QAAK,CAAC,EACrE;UAAExH,IAAI,EAAE,QAAQ;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACN,MAAM;UAAEuD,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAExH,IAAI,EAAE,OAAO;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACJ,KAAK;UAAEqD,QAAQ,EAAE;QAAK,CAAC,CAC/D;QACF9C,IAAI,EAAEvF,WAAY;QAClByI,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGA1H,eAAe,iBACdP,OAAA;MAAKoH,KAAK,EAAE;QACVE,eAAe,EAAE,OAAO;QACxBmB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,2BAA2B;QACtCrB,OAAO,EAAE;MACX,CAAE;MAAAG,QAAA,gBACAxH,OAAA;QAAIoH,KAAK,EAAE;UAAEK,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,KAAK;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,GACxEjH,eAAe,CAAC,cAAc,CAAC,EAAC,IAAE,EAACA,eAAe,CAAC,MAAM,CAAC,EAAC,GAC9D;MAAA;QAAAuH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGLjI,OAAA;QAAKoH,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEc,GAAG,EAAE,MAAM;UAAEpB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAChE,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACsC,GAAG,CAAEC,GAAG,iBAC/C/J,OAAA;UAEE4I,OAAO,EAAEA,CAAA,KAAM3H,YAAY,CAAC8I,GAAG,CAAE;UACjC3C,KAAK,EAAE;YACLC,OAAO,EAAE,UAAU;YACnBoB,YAAY,EAAE,KAAK;YACnBD,MAAM,EAAE,MAAM;YACdd,UAAU,EAAE,KAAK;YACjBoB,MAAM,EAAE,SAAS;YACjBxB,eAAe,EAAEtG,SAAS,KAAK+I,GAAG,GAC9BA,GAAG,KAAK,aAAa,GACnB,SAAS,GACTA,GAAG,KAAK,QAAQ,GAChB,SAAS,GACT,SAAS,GACX,SAAS;YACbpC,KAAK,EAAE3G,SAAS,KAAK+I,GAAG,GAAG,OAAO,GAAG;UACvC,CAAE;UAAAvC,QAAA,EAEDuC;QAAG,GAlBCA,GAAG;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNjI,OAAA;QAAKoH,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEa,cAAc,EAAE,QAAQ;UAAEnB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC9ExH,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXiH,WAAW,EAAE,aAAajI,SAAS,KAAM;UACzCkF,KAAK,EAAEpF,UAAW;UAClBwH,QAAQ,EAAG7G,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACI,MAAM,CAACqE,KAAK,CAAE;UAC/CkB,KAAK,EAAE;YACLmB,KAAK,EAAE,OAAO;YACdlB,OAAO,EAAE,MAAM;YACfmB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACb;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL3G,aAAa,gBACZtB,OAAA;QAAKoH,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEa,cAAc,EAAE,QAAQ;UAAE1B,OAAO,EAAE;QAAS,CAAE;QAAAG,QAAA,eAC3ExH,OAAA;UAAMoH,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAENjI,OAAA,CAACF,SAAS;QACRuJ,OAAO,EAAE,CACP;UAAEtH,IAAI,EAAE,QAAQ;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACN,MAAM;UAAEuD,QAAQ,EAAE;QAAK,CAAC,EACjE;UAAExH,IAAI,EAAE,OAAO;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACJ,KAAK;UAAEqD,QAAQ,EAAE;QAAK,CAAC,EAC/D;UAAExH,IAAI,EAAE,MAAM;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACT,IAAI,IAAI,GAAG;UAAE0D,QAAQ,EAAE;QAAK,CAAC,EACpE;UAAExH,IAAI,EAAE,QAAQ;UAAEuH,QAAQ,EAAGhD,GAAG,IAAKA,GAAG,CAACH,MAAM;UAAEoD,QAAQ,EAAE;QAAK,CAAC,CACjE;QACF9C,IAAI,EAAES,eAAgB;QACtByC,UAAU;QACVE,KAAK;QACLD,gBAAgB;MAAA;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC/H,EAAA,CAtbuBD,UAAU;AAAA+J,EAAA,GAAV/J,UAAU;AAAA,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}