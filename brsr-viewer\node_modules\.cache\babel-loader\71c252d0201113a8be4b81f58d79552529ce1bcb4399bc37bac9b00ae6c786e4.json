{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLDTDNotation,\n    XMLNode,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  XMLNode = require('./XMLNode');\n  NodeType = require('./NodeType');\n  module.exports = XMLDTDNotation = function (superClass) {\n    extend(XMLDTDNotation, superClass);\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.NotationDeclaration;\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n    Object.defineProperty(XMLDTDNotation.prototype, 'publicId', {\n      get: function () {\n        return this.pubID;\n      }\n    });\n    Object.defineProperty(XMLDTDNotation.prototype, 'systemId', {\n      get: function () {\n        return this.sysID;\n      }\n    });\n    XMLDTDNotation.prototype.toString = function (options) {\n      return this.options.writer.dtdNotation(this, this.options.writer.filterOptions(options));\n    };\n    return XMLDTDNotation;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLDTDNotation", "XMLNode", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "name", "value", "Error", "debugInfo", "pubID", "sysID", "stringify", "type", "NotationDeclaration", "dtdPubID", "dtdSysID", "Object", "defineProperty", "get", "toString", "options", "writer", "dtdNotation", "filterOptions"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDTDNotation.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.NotationDeclaration;\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.dtdNotation(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,cAAc;IAAEC,OAAO;IACnCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,OAAO,GAAGY,OAAO,CAAC,WAAW,CAAC;EAE9Bd,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCC,MAAM,CAACC,OAAO,GAAGf,cAAc,GAAI,UAASgB,UAAU,EAAE;IACtDd,MAAM,CAACF,cAAc,EAAEgB,UAAU,CAAC;IAElC,SAAShB,cAAcA,CAACI,MAAM,EAAEa,IAAI,EAAEC,KAAK,EAAE;MAC3ClB,cAAc,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACvD,IAAIa,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIE,KAAK,CAAC,6BAA6B,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;MACvE;MACA,IAAI,CAACC,KAAK,CAACG,KAAK,IAAI,CAACH,KAAK,CAACI,KAAK,EAAE;QAChC,MAAM,IAAIH,KAAK,CAAC,oEAAoE,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;MAC9G;MACA,IAAI,CAACA,IAAI,GAAG,IAAI,CAACM,SAAS,CAACN,IAAI,CAACA,IAAI,CAAC;MACrC,IAAI,CAACO,IAAI,GAAGzB,QAAQ,CAAC0B,mBAAmB;MACxC,IAAIP,KAAK,CAACG,KAAK,IAAI,IAAI,EAAE;QACvB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACE,SAAS,CAACG,QAAQ,CAACR,KAAK,CAACG,KAAK,CAAC;MACnD;MACA,IAAIH,KAAK,CAACI,KAAK,IAAI,IAAI,EAAE;QACvB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,SAAS,CAACI,QAAQ,CAACT,KAAK,CAACI,KAAK,CAAC;MACnD;IACF;IAEAM,MAAM,CAACC,cAAc,CAAC7B,cAAc,CAACU,SAAS,EAAE,UAAU,EAAE;MAC1DoB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACT,KAAK;MACnB;IACF,CAAC,CAAC;IAEFO,MAAM,CAACC,cAAc,CAAC7B,cAAc,CAACU,SAAS,EAAE,UAAU,EAAE;MAC1DoB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACR,KAAK;MACnB;IACF,CAAC,CAAC;IAEFtB,cAAc,CAACU,SAAS,CAACqB,QAAQ,GAAG,UAASC,OAAO,EAAE;MACpD,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC,IAAI,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM,CAACE,aAAa,CAACH,OAAO,CAAC,CAAC;IAC1F,CAAC;IAED,OAAOhC,cAAc;EAEvB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}