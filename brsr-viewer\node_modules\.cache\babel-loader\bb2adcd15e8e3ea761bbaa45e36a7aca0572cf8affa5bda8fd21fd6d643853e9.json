{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLDTDAttList,\n    XMLNode,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  XMLNode = require('./XMLNode');\n  NodeType = require('./NodeType');\n  module.exports = XMLDTDAttList = function (superClass) {\n    extend(XMLDTDAttList, superClass);\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.name(elementName);\n      this.type = NodeType.AttributeDeclaration;\n      this.attributeName = this.stringify.name(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      if (defaultValue) {\n        this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      }\n      this.defaultValueType = defaultValueType;\n    }\n    XMLDTDAttList.prototype.toString = function (options) {\n      return this.options.writer.dtdAttList(this, this.options.writer.filterOptions(options));\n    };\n    return XMLDTDAttList;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLDTDAttList", "XMLNode", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "elementName", "attributeName", "attributeType", "defaultValueType", "defaultValue", "Error", "debugInfo", "indexOf", "match", "stringify", "name", "type", "AttributeDeclaration", "dtdAttType", "dtdAttDefault", "toString", "options", "writer", "dtdAttList", "filterOptions"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDTDAttList.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.name(elementName);\n      this.type = NodeType.AttributeDeclaration;\n      this.attributeName = this.stringify.name(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      if (defaultValue) {\n        this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      }\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.dtdAttList(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,aAAa;IAAEC,OAAO;IAClCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,OAAO,GAAGY,OAAO,CAAC,WAAW,CAAC;EAE9Bd,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCC,MAAM,CAACC,OAAO,GAAGf,aAAa,GAAI,UAASgB,UAAU,EAAE;IACrDd,MAAM,CAACF,aAAa,EAAEgB,UAAU,CAAC;IAEjC,SAAShB,aAAaA,CAACI,MAAM,EAAEa,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,EAAE;MACxGrB,aAAa,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACtD,IAAIa,WAAW,IAAI,IAAI,EAAE;QACvB,MAAM,IAAIK,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAClE;MACA,IAAIL,aAAa,IAAI,IAAI,EAAE;QACzB,MAAM,IAAII,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC,CAAC;MAC/E;MACA,IAAI,CAACE,aAAa,EAAE;QAClB,MAAM,IAAIG,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC,CAAC;MAC/E;MACA,IAAI,CAACG,gBAAgB,EAAE;QACrB,MAAM,IAAIE,KAAK,CAAC,iCAAiC,GAAG,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC,CAAC;MAClF;MACA,IAAIG,gBAAgB,CAACI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACvCJ,gBAAgB,GAAG,GAAG,GAAGA,gBAAgB;MAC3C;MACA,IAAI,CAACA,gBAAgB,CAACK,KAAK,CAAC,wCAAwC,CAAC,EAAE;QACrE,MAAM,IAAIH,KAAK,CAAC,iFAAiF,GAAG,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC,CAAC;MAClI;MACA,IAAII,YAAY,IAAI,CAACD,gBAAgB,CAACK,KAAK,CAAC,qBAAqB,CAAC,EAAE;QAClE,MAAM,IAAIH,KAAK,CAAC,oDAAoD,GAAG,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC,CAAC;MACrG;MACA,IAAI,CAACA,WAAW,GAAG,IAAI,CAACS,SAAS,CAACC,IAAI,CAACV,WAAW,CAAC;MACnD,IAAI,CAACW,IAAI,GAAG7B,QAAQ,CAAC8B,oBAAoB;MACzC,IAAI,CAACX,aAAa,GAAG,IAAI,CAACQ,SAAS,CAACC,IAAI,CAACT,aAAa,CAAC;MACvD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACO,SAAS,CAACI,UAAU,CAACX,aAAa,CAAC;MAC7D,IAAIE,YAAY,EAAE;QAChB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACK,SAAS,CAACK,aAAa,CAACV,YAAY,CAAC;MAChE;MACA,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IAC1C;IAEApB,aAAa,CAACU,SAAS,CAACsB,QAAQ,GAAG,UAASC,OAAO,EAAE;MACnD,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACC,UAAU,CAAC,IAAI,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM,CAACE,aAAa,CAACH,OAAO,CAAC,CAAC;IACzF,CAAC;IAED,OAAOjC,aAAa;EAEtB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}