/// <reference types="react" />
export declare const seedNextClassnames: (names: string[]) => string[];
export declare const resetStyled: (isServer?: boolean) => (<Target extends import("..").WebTarget, InjectedProps extends object = import("../types").BaseObject>(tag: Target) => import("../constructors/constructWithOptions").Styled<"web", Target, Target extends import("../types").KnownTarget ? import("react").ComponentPropsWithRef<Target> & InjectedProps : InjectedProps, import("../types").BaseObject>) & {
    object: import("../constructors/constructWithOptions").Styled<"web", "object", import("react").DetailedHTMLProps<import("react").ObjectHTMLAttributes<HTMLObjectElement>, HTMLObjectElement>, import("../types").BaseObject>;
    g: import("../constructors/constructWithOptions").Styled<"web", "g", import("react").SVGProps<SVGGElement>, import("../types").BaseObject>;
    map: import("../constructors/constructWithOptions").Styled<"web", "map", import("react").DetailedHTMLProps<import("react").MapHTMLAttributes<HTMLMapElement>, HTMLMapElement>, import("../types").BaseObject>;
    big: import("../constructors/constructWithOptions").Styled<"web", "big", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    link: import("../constructors/constructWithOptions").Styled<"web", "link", import("react").DetailedHTMLProps<import("react").LinkHTMLAttributes<HTMLLinkElement>, HTMLLinkElement>, import("../types").BaseObject>;
    small: import("../constructors/constructWithOptions").Styled<"web", "small", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    sub: import("../constructors/constructWithOptions").Styled<"web", "sub", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    sup: import("../constructors/constructWithOptions").Styled<"web", "sup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    a: import("../constructors/constructWithOptions").Styled<"web", "a", import("react").DetailedHTMLProps<import("react").AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, import("../types").BaseObject>;
    abbr: import("../constructors/constructWithOptions").Styled<"web", "abbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    address: import("../constructors/constructWithOptions").Styled<"web", "address", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    area: import("../constructors/constructWithOptions").Styled<"web", "area", import("react").DetailedHTMLProps<import("react").AreaHTMLAttributes<HTMLAreaElement>, HTMLAreaElement>, import("../types").BaseObject>;
    article: import("../constructors/constructWithOptions").Styled<"web", "article", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    aside: import("../constructors/constructWithOptions").Styled<"web", "aside", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    audio: import("../constructors/constructWithOptions").Styled<"web", "audio", import("react").DetailedHTMLProps<import("react").AudioHTMLAttributes<HTMLAudioElement>, HTMLAudioElement>, import("../types").BaseObject>;
    b: import("../constructors/constructWithOptions").Styled<"web", "b", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    base: import("../constructors/constructWithOptions").Styled<"web", "base", import("react").DetailedHTMLProps<import("react").BaseHTMLAttributes<HTMLBaseElement>, HTMLBaseElement>, import("../types").BaseObject>;
    bdi: import("../constructors/constructWithOptions").Styled<"web", "bdi", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    bdo: import("../constructors/constructWithOptions").Styled<"web", "bdo", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    blockquote: import("../constructors/constructWithOptions").Styled<"web", "blockquote", import("react").DetailedHTMLProps<import("react").BlockquoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, import("../types").BaseObject>;
    body: import("../constructors/constructWithOptions").Styled<"web", "body", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBodyElement>, HTMLBodyElement>, import("../types").BaseObject>;
    br: import("../constructors/constructWithOptions").Styled<"web", "br", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBRElement>, HTMLBRElement>, import("../types").BaseObject>;
    button: import("../constructors/constructWithOptions").Styled<"web", "button", import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, import("../types").BaseObject>;
    canvas: import("../constructors/constructWithOptions").Styled<"web", "canvas", import("react").DetailedHTMLProps<import("react").CanvasHTMLAttributes<HTMLCanvasElement>, HTMLCanvasElement>, import("../types").BaseObject>;
    caption: import("../constructors/constructWithOptions").Styled<"web", "caption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    cite: import("../constructors/constructWithOptions").Styled<"web", "cite", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    code: import("../constructors/constructWithOptions").Styled<"web", "code", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    col: import("../constructors/constructWithOptions").Styled<"web", "col", import("react").DetailedHTMLProps<import("react").ColHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, import("../types").BaseObject>;
    colgroup: import("../constructors/constructWithOptions").Styled<"web", "colgroup", import("react").DetailedHTMLProps<import("react").ColgroupHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, import("../types").BaseObject>;
    data: import("../constructors/constructWithOptions").Styled<"web", "data", import("react").DetailedHTMLProps<import("react").DataHTMLAttributes<HTMLDataElement>, HTMLDataElement>, import("../types").BaseObject>;
    datalist: import("../constructors/constructWithOptions").Styled<"web", "datalist", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDataListElement>, HTMLDataListElement>, import("../types").BaseObject>;
    dd: import("../constructors/constructWithOptions").Styled<"web", "dd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    del: import("../constructors/constructWithOptions").Styled<"web", "del", import("react").DetailedHTMLProps<import("react").DelHTMLAttributes<HTMLModElement>, HTMLModElement>, import("../types").BaseObject>;
    details: import("../constructors/constructWithOptions").Styled<"web", "details", import("react").DetailedHTMLProps<import("react").DetailsHTMLAttributes<HTMLDetailsElement>, HTMLDetailsElement>, import("../types").BaseObject>;
    dfn: import("../constructors/constructWithOptions").Styled<"web", "dfn", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    dialog: import("../constructors/constructWithOptions").Styled<"web", "dialog", import("react").DetailedHTMLProps<import("react").DialogHTMLAttributes<HTMLDialogElement>, HTMLDialogElement>, import("../types").BaseObject>;
    div: import("../constructors/constructWithOptions").Styled<"web", "div", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, import("../types").BaseObject>;
    dl: import("../constructors/constructWithOptions").Styled<"web", "dl", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDListElement>, HTMLDListElement>, import("../types").BaseObject>;
    dt: import("../constructors/constructWithOptions").Styled<"web", "dt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    em: import("../constructors/constructWithOptions").Styled<"web", "em", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    embed: import("../constructors/constructWithOptions").Styled<"web", "embed", import("react").DetailedHTMLProps<import("react").EmbedHTMLAttributes<HTMLEmbedElement>, HTMLEmbedElement>, import("../types").BaseObject>;
    fieldset: import("../constructors/constructWithOptions").Styled<"web", "fieldset", import("react").DetailedHTMLProps<import("react").FieldsetHTMLAttributes<HTMLFieldSetElement>, HTMLFieldSetElement>, import("../types").BaseObject>;
    figcaption: import("../constructors/constructWithOptions").Styled<"web", "figcaption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    figure: import("../constructors/constructWithOptions").Styled<"web", "figure", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    footer: import("../constructors/constructWithOptions").Styled<"web", "footer", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    form: import("../constructors/constructWithOptions").Styled<"web", "form", import("react").DetailedHTMLProps<import("react").FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>, import("../types").BaseObject>;
    h1: import("../constructors/constructWithOptions").Styled<"web", "h1", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, import("../types").BaseObject>;
    h2: import("../constructors/constructWithOptions").Styled<"web", "h2", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, import("../types").BaseObject>;
    h3: import("../constructors/constructWithOptions").Styled<"web", "h3", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, import("../types").BaseObject>;
    h4: import("../constructors/constructWithOptions").Styled<"web", "h4", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, import("../types").BaseObject>;
    h5: import("../constructors/constructWithOptions").Styled<"web", "h5", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, import("../types").BaseObject>;
    h6: import("../constructors/constructWithOptions").Styled<"web", "h6", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, import("../types").BaseObject>;
    header: import("../constructors/constructWithOptions").Styled<"web", "header", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    hgroup: import("../constructors/constructWithOptions").Styled<"web", "hgroup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    hr: import("../constructors/constructWithOptions").Styled<"web", "hr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHRElement>, HTMLHRElement>, import("../types").BaseObject>;
    html: import("../constructors/constructWithOptions").Styled<"web", "html", import("react").DetailedHTMLProps<import("react").HtmlHTMLAttributes<HTMLHtmlElement>, HTMLHtmlElement>, import("../types").BaseObject>;
    i: import("../constructors/constructWithOptions").Styled<"web", "i", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    iframe: import("../constructors/constructWithOptions").Styled<"web", "iframe", import("react").DetailedHTMLProps<import("react").IframeHTMLAttributes<HTMLIFrameElement>, HTMLIFrameElement>, import("../types").BaseObject>;
    img: import("../constructors/constructWithOptions").Styled<"web", "img", import("react").DetailedHTMLProps<import("react").ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, import("../types").BaseObject>;
    input: import("../constructors/constructWithOptions").Styled<"web", "input", import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, import("../types").BaseObject>;
    ins: import("../constructors/constructWithOptions").Styled<"web", "ins", import("react").DetailedHTMLProps<import("react").InsHTMLAttributes<HTMLModElement>, HTMLModElement>, import("../types").BaseObject>;
    kbd: import("../constructors/constructWithOptions").Styled<"web", "kbd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    keygen: import("../constructors/constructWithOptions").Styled<"web", "keygen", import("react").DetailedHTMLProps<import("react").KeygenHTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    label: import("../constructors/constructWithOptions").Styled<"web", "label", import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, import("../types").BaseObject>;
    legend: import("../constructors/constructWithOptions").Styled<"web", "legend", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLLegendElement>, HTMLLegendElement>, import("../types").BaseObject>;
    li: import("../constructors/constructWithOptions").Styled<"web", "li", import("react").DetailedHTMLProps<import("react").LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, import("../types").BaseObject>;
    main: import("../constructors/constructWithOptions").Styled<"web", "main", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    mark: import("../constructors/constructWithOptions").Styled<"web", "mark", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    menu: import("../constructors/constructWithOptions").Styled<"web", "menu", import("react").DetailedHTMLProps<import("react").MenuHTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    menuitem: import("../constructors/constructWithOptions").Styled<"web", "menuitem", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    meta: import("../constructors/constructWithOptions").Styled<"web", "meta", import("react").DetailedHTMLProps<import("react").MetaHTMLAttributes<HTMLMetaElement>, HTMLMetaElement>, import("../types").BaseObject>;
    meter: import("../constructors/constructWithOptions").Styled<"web", "meter", import("react").DetailedHTMLProps<import("react").MeterHTMLAttributes<HTMLMeterElement>, HTMLMeterElement>, import("../types").BaseObject>;
    nav: import("../constructors/constructWithOptions").Styled<"web", "nav", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    noscript: import("../constructors/constructWithOptions").Styled<"web", "noscript", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    ol: import("../constructors/constructWithOptions").Styled<"web", "ol", import("react").DetailedHTMLProps<import("react").OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, import("../types").BaseObject>;
    optgroup: import("../constructors/constructWithOptions").Styled<"web", "optgroup", import("react").DetailedHTMLProps<import("react").OptgroupHTMLAttributes<HTMLOptGroupElement>, HTMLOptGroupElement>, import("../types").BaseObject>;
    option: import("../constructors/constructWithOptions").Styled<"web", "option", import("react").DetailedHTMLProps<import("react").OptionHTMLAttributes<HTMLOptionElement>, HTMLOptionElement>, import("../types").BaseObject>;
    output: import("../constructors/constructWithOptions").Styled<"web", "output", import("react").DetailedHTMLProps<import("react").OutputHTMLAttributes<HTMLOutputElement>, HTMLOutputElement>, import("../types").BaseObject>;
    p: import("../constructors/constructWithOptions").Styled<"web", "p", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>, import("../types").BaseObject>;
    param: import("../constructors/constructWithOptions").Styled<"web", "param", import("react").DetailedHTMLProps<import("react").ParamHTMLAttributes<HTMLParamElement>, HTMLParamElement>, import("../types").BaseObject>;
    picture: import("../constructors/constructWithOptions").Styled<"web", "picture", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    pre: import("../constructors/constructWithOptions").Styled<"web", "pre", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLPreElement>, HTMLPreElement>, import("../types").BaseObject>;
    progress: import("../constructors/constructWithOptions").Styled<"web", "progress", import("react").DetailedHTMLProps<import("react").ProgressHTMLAttributes<HTMLProgressElement>, HTMLProgressElement>, import("../types").BaseObject>;
    q: import("../constructors/constructWithOptions").Styled<"web", "q", import("react").DetailedHTMLProps<import("react").QuoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, import("../types").BaseObject>;
    rp: import("../constructors/constructWithOptions").Styled<"web", "rp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    rt: import("../constructors/constructWithOptions").Styled<"web", "rt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    ruby: import("../constructors/constructWithOptions").Styled<"web", "ruby", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    s: import("../constructors/constructWithOptions").Styled<"web", "s", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    samp: import("../constructors/constructWithOptions").Styled<"web", "samp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    script: import("../constructors/constructWithOptions").Styled<"web", "script", import("react").DetailedHTMLProps<import("react").ScriptHTMLAttributes<HTMLScriptElement>, HTMLScriptElement>, import("../types").BaseObject>;
    section: import("../constructors/constructWithOptions").Styled<"web", "section", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    select: import("../constructors/constructWithOptions").Styled<"web", "select", import("react").DetailedHTMLProps<import("react").SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement>, import("../types").BaseObject>;
    source: import("../constructors/constructWithOptions").Styled<"web", "source", import("react").DetailedHTMLProps<import("react").SourceHTMLAttributes<HTMLSourceElement>, HTMLSourceElement>, import("../types").BaseObject>;
    span: import("../constructors/constructWithOptions").Styled<"web", "span", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, import("../types").BaseObject>;
    strong: import("../constructors/constructWithOptions").Styled<"web", "strong", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    style: import("../constructors/constructWithOptions").Styled<"web", "style", import("react").DetailedHTMLProps<import("react").StyleHTMLAttributes<HTMLStyleElement>, HTMLStyleElement>, import("../types").BaseObject>;
    summary: import("../constructors/constructWithOptions").Styled<"web", "summary", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    table: import("../constructors/constructWithOptions").Styled<"web", "table", import("react").DetailedHTMLProps<import("react").TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, import("../types").BaseObject>;
    tbody: import("../constructors/constructWithOptions").Styled<"web", "tbody", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, import("../types").BaseObject>;
    td: import("../constructors/constructWithOptions").Styled<"web", "td", import("react").DetailedHTMLProps<import("react").TdHTMLAttributes<HTMLTableDataCellElement>, HTMLTableDataCellElement>, import("../types").BaseObject>;
    textarea: import("../constructors/constructWithOptions").Styled<"web", "textarea", import("react").DetailedHTMLProps<import("react").TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, import("../types").BaseObject>;
    tfoot: import("../constructors/constructWithOptions").Styled<"web", "tfoot", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, import("../types").BaseObject>;
    th: import("../constructors/constructWithOptions").Styled<"web", "th", import("react").DetailedHTMLProps<import("react").ThHTMLAttributes<HTMLTableHeaderCellElement>, HTMLTableHeaderCellElement>, import("../types").BaseObject>;
    thead: import("../constructors/constructWithOptions").Styled<"web", "thead", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, import("../types").BaseObject>;
    time: import("../constructors/constructWithOptions").Styled<"web", "time", import("react").DetailedHTMLProps<import("react").TimeHTMLAttributes<HTMLTimeElement>, HTMLTimeElement>, import("../types").BaseObject>;
    tr: import("../constructors/constructWithOptions").Styled<"web", "tr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableRowElement>, HTMLTableRowElement>, import("../types").BaseObject>;
    track: import("../constructors/constructWithOptions").Styled<"web", "track", import("react").DetailedHTMLProps<import("react").TrackHTMLAttributes<HTMLTrackElement>, HTMLTrackElement>, import("../types").BaseObject>;
    u: import("../constructors/constructWithOptions").Styled<"web", "u", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    ul: import("../constructors/constructWithOptions").Styled<"web", "ul", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLUListElement>, HTMLUListElement>, import("../types").BaseObject>;
    var: import("../constructors/constructWithOptions").Styled<"web", "var", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    video: import("../constructors/constructWithOptions").Styled<"web", "video", import("react").DetailedHTMLProps<import("react").VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>, import("../types").BaseObject>;
    wbr: import("../constructors/constructWithOptions").Styled<"web", "wbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, import("../types").BaseObject>;
    svg: import("../constructors/constructWithOptions").Styled<"web", "svg", import("react").SVGProps<SVGSVGElement>, import("../types").BaseObject>;
    circle: import("../constructors/constructWithOptions").Styled<"web", "circle", import("react").SVGProps<SVGCircleElement>, import("../types").BaseObject>;
    clipPath: import("../constructors/constructWithOptions").Styled<"web", "clipPath", import("react").SVGProps<SVGClipPathElement>, import("../types").BaseObject>;
    defs: import("../constructors/constructWithOptions").Styled<"web", "defs", import("react").SVGProps<SVGDefsElement>, import("../types").BaseObject>;
    ellipse: import("../constructors/constructWithOptions").Styled<"web", "ellipse", import("react").SVGProps<SVGEllipseElement>, import("../types").BaseObject>;
    foreignObject: import("../constructors/constructWithOptions").Styled<"web", "foreignObject", import("react").SVGProps<SVGForeignObjectElement>, import("../types").BaseObject>;
    image: import("../constructors/constructWithOptions").Styled<"web", "image", import("react").SVGProps<SVGImageElement>, import("../types").BaseObject>;
    line: import("../constructors/constructWithOptions").Styled<"web", "line", import("react").SVGLineElementAttributes<SVGLineElement>, import("../types").BaseObject>;
    linearGradient: import("../constructors/constructWithOptions").Styled<"web", "linearGradient", import("react").SVGProps<SVGLinearGradientElement>, import("../types").BaseObject>;
    marker: import("../constructors/constructWithOptions").Styled<"web", "marker", import("react").SVGProps<SVGMarkerElement>, import("../types").BaseObject>;
    mask: import("../constructors/constructWithOptions").Styled<"web", "mask", import("react").SVGProps<SVGMaskElement>, import("../types").BaseObject>;
    path: import("../constructors/constructWithOptions").Styled<"web", "path", import("react").SVGProps<SVGPathElement>, import("../types").BaseObject>;
    pattern: import("../constructors/constructWithOptions").Styled<"web", "pattern", import("react").SVGProps<SVGPatternElement>, import("../types").BaseObject>;
    polygon: import("../constructors/constructWithOptions").Styled<"web", "polygon", import("react").SVGProps<SVGPolygonElement>, import("../types").BaseObject>;
    polyline: import("../constructors/constructWithOptions").Styled<"web", "polyline", import("react").SVGProps<SVGPolylineElement>, import("../types").BaseObject>;
    radialGradient: import("../constructors/constructWithOptions").Styled<"web", "radialGradient", import("react").SVGProps<SVGRadialGradientElement>, import("../types").BaseObject>;
    rect: import("../constructors/constructWithOptions").Styled<"web", "rect", import("react").SVGProps<SVGRectElement>, import("../types").BaseObject>;
    stop: import("../constructors/constructWithOptions").Styled<"web", "stop", import("react").SVGProps<SVGStopElement>, import("../types").BaseObject>;
    text: import("../constructors/constructWithOptions").Styled<"web", "text", import("react").SVGTextElementAttributes<SVGTextElement>, import("../types").BaseObject>;
    tspan: import("../constructors/constructWithOptions").Styled<"web", "tspan", import("react").SVGProps<SVGTSpanElement>, import("../types").BaseObject>;
    use: import("../constructors/constructWithOptions").Styled<"web", "use", import("react").SVGProps<SVGUseElement>, import("../types").BaseObject>;
};
export declare const rehydrateTestStyles: () => void;
export declare const stripComments: (str: string) => string;
export declare const stripWhitespace: (str: string) => string;
export declare const getCSS: (scope: Document | HTMLElement) => string;
export declare const expectCSSMatches: (_expectation: string, opts?: {
    ignoreWhitespace: boolean;
}) => string;
export declare const getRenderedCSS: () => string;
