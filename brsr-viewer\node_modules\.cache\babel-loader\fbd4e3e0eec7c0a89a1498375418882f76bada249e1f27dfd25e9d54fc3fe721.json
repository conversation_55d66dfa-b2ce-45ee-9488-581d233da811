{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  \"use strict\";\n\n  var builder,\n    defaults,\n    escapeCDATA,\n    requiresCDATA,\n    wrapCDATA,\n    hasProp = {}.hasOwnProperty;\n  builder = require('xmlbuilder');\n  defaults = require('./defaults').defaults;\n  requiresCDATA = function (entry) {\n    return typeof entry === \"string\" && (entry.indexOf('&') >= 0 || entry.indexOf('>') >= 0 || entry.indexOf('<') >= 0);\n  };\n  wrapCDATA = function (entry) {\n    return \"<![CDATA[\" + escapeCDATA(entry) + \"]]>\";\n  };\n  escapeCDATA = function (entry) {\n    return entry.replace(']]>', ']]]]><![CDATA[>');\n  };\n  exports.Builder = function () {\n    function Builder(opts) {\n      var key, ref, value;\n      this.options = {};\n      ref = defaults[\"0.2\"];\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this.options[key] = value;\n      }\n      for (key in opts) {\n        if (!hasProp.call(opts, key)) continue;\n        value = opts[key];\n        this.options[key] = value;\n      }\n    }\n    Builder.prototype.buildObject = function (rootObj) {\n      var attrkey, charkey, render, rootElement, rootName;\n      attrkey = this.options.attrkey;\n      charkey = this.options.charkey;\n      if (Object.keys(rootObj).length === 1 && this.options.rootName === defaults['0.2'].rootName) {\n        rootName = Object.keys(rootObj)[0];\n        rootObj = rootObj[rootName];\n      } else {\n        rootName = this.options.rootName;\n      }\n      render = function (_this) {\n        return function (element, obj) {\n          var attr, child, entry, index, key, value;\n          if (typeof obj !== 'object') {\n            if (_this.options.cdata && requiresCDATA(obj)) {\n              element.raw(wrapCDATA(obj));\n            } else {\n              element.txt(obj);\n            }\n          } else if (Array.isArray(obj)) {\n            for (index in obj) {\n              if (!hasProp.call(obj, index)) continue;\n              child = obj[index];\n              for (key in child) {\n                entry = child[key];\n                element = render(element.ele(key), entry).up();\n              }\n            }\n          } else {\n            for (key in obj) {\n              if (!hasProp.call(obj, key)) continue;\n              child = obj[key];\n              if (key === attrkey) {\n                if (typeof child === \"object\") {\n                  for (attr in child) {\n                    value = child[attr];\n                    element = element.att(attr, value);\n                  }\n                }\n              } else if (key === charkey) {\n                if (_this.options.cdata && requiresCDATA(child)) {\n                  element = element.raw(wrapCDATA(child));\n                } else {\n                  element = element.txt(child);\n                }\n              } else if (Array.isArray(child)) {\n                for (index in child) {\n                  if (!hasProp.call(child, index)) continue;\n                  entry = child[index];\n                  if (typeof entry === 'string') {\n                    if (_this.options.cdata && requiresCDATA(entry)) {\n                      element = element.ele(key).raw(wrapCDATA(entry)).up();\n                    } else {\n                      element = element.ele(key, entry).up();\n                    }\n                  } else {\n                    element = render(element.ele(key), entry).up();\n                  }\n                }\n              } else if (typeof child === \"object\") {\n                element = render(element.ele(key), child).up();\n              } else {\n                if (typeof child === 'string' && _this.options.cdata && requiresCDATA(child)) {\n                  element = element.ele(key).raw(wrapCDATA(child)).up();\n                } else {\n                  if (child == null) {\n                    child = '';\n                  }\n                  element = element.ele(key, child.toString()).up();\n                }\n              }\n            }\n          }\n          return element;\n        };\n      }(this);\n      rootElement = builder.create(rootName, this.options.xmldec, this.options.doctype, {\n        headless: this.options.headless,\n        allowSurrogateChars: this.options.allowSurrogateChars\n      });\n      return render(rootElement, rootObj).end(this.options.renderOpts);\n    };\n    return Builder;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["builder", "defaults", "escapeCDATA", "requiresCDATA", "wrapCDATA", "hasProp", "hasOwnProperty", "require", "entry", "indexOf", "replace", "exports", "Builder", "opts", "key", "ref", "value", "options", "call", "prototype", "buildObject", "rootObj", "attrkey", "charkey", "render", "rootElement", "rootName", "Object", "keys", "length", "_this", "element", "obj", "attr", "child", "index", "cdata", "raw", "txt", "Array", "isArray", "ele", "up", "att", "toString", "create", "xmldec", "doctype", "headless", "allowSurrogateChars", "end", "renderOpts"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xml2js/lib/builder.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  \"use strict\";\n  var builder, defaults, escapeCDATA, requiresCDATA, wrapCDATA,\n    hasProp = {}.hasOwnProperty;\n\n  builder = require('xmlbuilder');\n\n  defaults = require('./defaults').defaults;\n\n  requiresCDATA = function(entry) {\n    return typeof entry === \"string\" && (entry.indexOf('&') >= 0 || entry.indexOf('>') >= 0 || entry.indexOf('<') >= 0);\n  };\n\n  wrapCDATA = function(entry) {\n    return \"<![CDATA[\" + (escapeCDATA(entry)) + \"]]>\";\n  };\n\n  escapeCDATA = function(entry) {\n    return entry.replace(']]>', ']]]]><![CDATA[>');\n  };\n\n  exports.Builder = (function() {\n    function Builder(opts) {\n      var key, ref, value;\n      this.options = {};\n      ref = defaults[\"0.2\"];\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this.options[key] = value;\n      }\n      for (key in opts) {\n        if (!hasProp.call(opts, key)) continue;\n        value = opts[key];\n        this.options[key] = value;\n      }\n    }\n\n    Builder.prototype.buildObject = function(rootObj) {\n      var attrkey, charkey, render, rootElement, rootName;\n      attrkey = this.options.attrkey;\n      charkey = this.options.charkey;\n      if ((Object.keys(rootObj).length === 1) && (this.options.rootName === defaults['0.2'].rootName)) {\n        rootName = Object.keys(rootObj)[0];\n        rootObj = rootObj[rootName];\n      } else {\n        rootName = this.options.rootName;\n      }\n      render = (function(_this) {\n        return function(element, obj) {\n          var attr, child, entry, index, key, value;\n          if (typeof obj !== 'object') {\n            if (_this.options.cdata && requiresCDATA(obj)) {\n              element.raw(wrapCDATA(obj));\n            } else {\n              element.txt(obj);\n            }\n          } else if (Array.isArray(obj)) {\n            for (index in obj) {\n              if (!hasProp.call(obj, index)) continue;\n              child = obj[index];\n              for (key in child) {\n                entry = child[key];\n                element = render(element.ele(key), entry).up();\n              }\n            }\n          } else {\n            for (key in obj) {\n              if (!hasProp.call(obj, key)) continue;\n              child = obj[key];\n              if (key === attrkey) {\n                if (typeof child === \"object\") {\n                  for (attr in child) {\n                    value = child[attr];\n                    element = element.att(attr, value);\n                  }\n                }\n              } else if (key === charkey) {\n                if (_this.options.cdata && requiresCDATA(child)) {\n                  element = element.raw(wrapCDATA(child));\n                } else {\n                  element = element.txt(child);\n                }\n              } else if (Array.isArray(child)) {\n                for (index in child) {\n                  if (!hasProp.call(child, index)) continue;\n                  entry = child[index];\n                  if (typeof entry === 'string') {\n                    if (_this.options.cdata && requiresCDATA(entry)) {\n                      element = element.ele(key).raw(wrapCDATA(entry)).up();\n                    } else {\n                      element = element.ele(key, entry).up();\n                    }\n                  } else {\n                    element = render(element.ele(key), entry).up();\n                  }\n                }\n              } else if (typeof child === \"object\") {\n                element = render(element.ele(key), child).up();\n              } else {\n                if (typeof child === 'string' && _this.options.cdata && requiresCDATA(child)) {\n                  element = element.ele(key).raw(wrapCDATA(child)).up();\n                } else {\n                  if (child == null) {\n                    child = '';\n                  }\n                  element = element.ele(key, child.toString()).up();\n                }\n              }\n            }\n          }\n          return element;\n        };\n      })(this);\n      rootElement = builder.create(rootName, this.options.xmldec, this.options.doctype, {\n        headless: this.options.headless,\n        allowSurrogateChars: this.options.allowSurrogateChars\n      });\n      return render(rootElement, rootObj).end(this.options.renderOpts);\n    };\n\n    return Builder;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,YAAY;;EACZ,IAAIA,OAAO;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC,SAAS;IAC1DC,OAAO,GAAG,CAAC,CAAC,CAACC,cAAc;EAE7BN,OAAO,GAAGO,OAAO,CAAC,YAAY,CAAC;EAE/BN,QAAQ,GAAGM,OAAO,CAAC,YAAY,CAAC,CAACN,QAAQ;EAEzCE,aAAa,GAAG,SAAAA,CAASK,KAAK,EAAE;IAC9B,OAAO,OAAOA,KAAK,KAAK,QAAQ,KAAKA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAID,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAID,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EACrH,CAAC;EAEDL,SAAS,GAAG,SAAAA,CAASI,KAAK,EAAE;IAC1B,OAAO,WAAW,GAAIN,WAAW,CAACM,KAAK,CAAE,GAAG,KAAK;EACnD,CAAC;EAEDN,WAAW,GAAG,SAAAA,CAASM,KAAK,EAAE;IAC5B,OAAOA,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAChD,CAAC;EAEDC,OAAO,CAACC,OAAO,GAAI,YAAW;IAC5B,SAASA,OAAOA,CAACC,IAAI,EAAE;MACrB,IAAIC,GAAG,EAAEC,GAAG,EAAEC,KAAK;MACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MACjBF,GAAG,GAAGd,QAAQ,CAAC,KAAK,CAAC;MACrB,KAAKa,GAAG,IAAIC,GAAG,EAAE;QACf,IAAI,CAACV,OAAO,CAACa,IAAI,CAACH,GAAG,EAAED,GAAG,CAAC,EAAE;QAC7BE,KAAK,GAAGD,GAAG,CAACD,GAAG,CAAC;QAChB,IAAI,CAACG,OAAO,CAACH,GAAG,CAAC,GAAGE,KAAK;MAC3B;MACA,KAAKF,GAAG,IAAID,IAAI,EAAE;QAChB,IAAI,CAACR,OAAO,CAACa,IAAI,CAACL,IAAI,EAAEC,GAAG,CAAC,EAAE;QAC9BE,KAAK,GAAGH,IAAI,CAACC,GAAG,CAAC;QACjB,IAAI,CAACG,OAAO,CAACH,GAAG,CAAC,GAAGE,KAAK;MAC3B;IACF;IAEAJ,OAAO,CAACO,SAAS,CAACC,WAAW,GAAG,UAASC,OAAO,EAAE;MAChD,IAAIC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEC,QAAQ;MACnDJ,OAAO,GAAG,IAAI,CAACL,OAAO,CAACK,OAAO;MAC9BC,OAAO,GAAG,IAAI,CAACN,OAAO,CAACM,OAAO;MAC9B,IAAKI,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAACQ,MAAM,KAAK,CAAC,IAAM,IAAI,CAACZ,OAAO,CAACS,QAAQ,KAAKzB,QAAQ,CAAC,KAAK,CAAC,CAACyB,QAAS,EAAE;QAC/FA,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC;QAClCA,OAAO,GAAGA,OAAO,CAACK,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACLA,QAAQ,GAAG,IAAI,CAACT,OAAO,CAACS,QAAQ;MAClC;MACAF,MAAM,GAAI,UAASM,KAAK,EAAE;QACxB,OAAO,UAASC,OAAO,EAAEC,GAAG,EAAE;UAC5B,IAAIC,IAAI,EAAEC,KAAK,EAAE1B,KAAK,EAAE2B,KAAK,EAAErB,GAAG,EAAEE,KAAK;UACzC,IAAI,OAAOgB,GAAG,KAAK,QAAQ,EAAE;YAC3B,IAAIF,KAAK,CAACb,OAAO,CAACmB,KAAK,IAAIjC,aAAa,CAAC6B,GAAG,CAAC,EAAE;cAC7CD,OAAO,CAACM,GAAG,CAACjC,SAAS,CAAC4B,GAAG,CAAC,CAAC;YAC7B,CAAC,MAAM;cACLD,OAAO,CAACO,GAAG,CAACN,GAAG,CAAC;YAClB;UACF,CAAC,MAAM,IAAIO,KAAK,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;YAC7B,KAAKG,KAAK,IAAIH,GAAG,EAAE;cACjB,IAAI,CAAC3B,OAAO,CAACa,IAAI,CAACc,GAAG,EAAEG,KAAK,CAAC,EAAE;cAC/BD,KAAK,GAAGF,GAAG,CAACG,KAAK,CAAC;cAClB,KAAKrB,GAAG,IAAIoB,KAAK,EAAE;gBACjB1B,KAAK,GAAG0B,KAAK,CAACpB,GAAG,CAAC;gBAClBiB,OAAO,GAAGP,MAAM,CAACO,OAAO,CAACU,GAAG,CAAC3B,GAAG,CAAC,EAAEN,KAAK,CAAC,CAACkC,EAAE,CAAC,CAAC;cAChD;YACF;UACF,CAAC,MAAM;YACL,KAAK5B,GAAG,IAAIkB,GAAG,EAAE;cACf,IAAI,CAAC3B,OAAO,CAACa,IAAI,CAACc,GAAG,EAAElB,GAAG,CAAC,EAAE;cAC7BoB,KAAK,GAAGF,GAAG,CAAClB,GAAG,CAAC;cAChB,IAAIA,GAAG,KAAKQ,OAAO,EAAE;gBACnB,IAAI,OAAOY,KAAK,KAAK,QAAQ,EAAE;kBAC7B,KAAKD,IAAI,IAAIC,KAAK,EAAE;oBAClBlB,KAAK,GAAGkB,KAAK,CAACD,IAAI,CAAC;oBACnBF,OAAO,GAAGA,OAAO,CAACY,GAAG,CAACV,IAAI,EAAEjB,KAAK,CAAC;kBACpC;gBACF;cACF,CAAC,MAAM,IAAIF,GAAG,KAAKS,OAAO,EAAE;gBAC1B,IAAIO,KAAK,CAACb,OAAO,CAACmB,KAAK,IAAIjC,aAAa,CAAC+B,KAAK,CAAC,EAAE;kBAC/CH,OAAO,GAAGA,OAAO,CAACM,GAAG,CAACjC,SAAS,CAAC8B,KAAK,CAAC,CAAC;gBACzC,CAAC,MAAM;kBACLH,OAAO,GAAGA,OAAO,CAACO,GAAG,CAACJ,KAAK,CAAC;gBAC9B;cACF,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;gBAC/B,KAAKC,KAAK,IAAID,KAAK,EAAE;kBACnB,IAAI,CAAC7B,OAAO,CAACa,IAAI,CAACgB,KAAK,EAAEC,KAAK,CAAC,EAAE;kBACjC3B,KAAK,GAAG0B,KAAK,CAACC,KAAK,CAAC;kBACpB,IAAI,OAAO3B,KAAK,KAAK,QAAQ,EAAE;oBAC7B,IAAIsB,KAAK,CAACb,OAAO,CAACmB,KAAK,IAAIjC,aAAa,CAACK,KAAK,CAAC,EAAE;sBAC/CuB,OAAO,GAAGA,OAAO,CAACU,GAAG,CAAC3B,GAAG,CAAC,CAACuB,GAAG,CAACjC,SAAS,CAACI,KAAK,CAAC,CAAC,CAACkC,EAAE,CAAC,CAAC;oBACvD,CAAC,MAAM;sBACLX,OAAO,GAAGA,OAAO,CAACU,GAAG,CAAC3B,GAAG,EAAEN,KAAK,CAAC,CAACkC,EAAE,CAAC,CAAC;oBACxC;kBACF,CAAC,MAAM;oBACLX,OAAO,GAAGP,MAAM,CAACO,OAAO,CAACU,GAAG,CAAC3B,GAAG,CAAC,EAAEN,KAAK,CAAC,CAACkC,EAAE,CAAC,CAAC;kBAChD;gBACF;cACF,CAAC,MAAM,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;gBACpCH,OAAO,GAAGP,MAAM,CAACO,OAAO,CAACU,GAAG,CAAC3B,GAAG,CAAC,EAAEoB,KAAK,CAAC,CAACQ,EAAE,CAAC,CAAC;cAChD,CAAC,MAAM;gBACL,IAAI,OAAOR,KAAK,KAAK,QAAQ,IAAIJ,KAAK,CAACb,OAAO,CAACmB,KAAK,IAAIjC,aAAa,CAAC+B,KAAK,CAAC,EAAE;kBAC5EH,OAAO,GAAGA,OAAO,CAACU,GAAG,CAAC3B,GAAG,CAAC,CAACuB,GAAG,CAACjC,SAAS,CAAC8B,KAAK,CAAC,CAAC,CAACQ,EAAE,CAAC,CAAC;gBACvD,CAAC,MAAM;kBACL,IAAIR,KAAK,IAAI,IAAI,EAAE;oBACjBA,KAAK,GAAG,EAAE;kBACZ;kBACAH,OAAO,GAAGA,OAAO,CAACU,GAAG,CAAC3B,GAAG,EAAEoB,KAAK,CAACU,QAAQ,CAAC,CAAC,CAAC,CAACF,EAAE,CAAC,CAAC;gBACnD;cACF;YACF;UACF;UACA,OAAOX,OAAO;QAChB,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;MACRN,WAAW,GAAGzB,OAAO,CAAC6C,MAAM,CAACnB,QAAQ,EAAE,IAAI,CAACT,OAAO,CAAC6B,MAAM,EAAE,IAAI,CAAC7B,OAAO,CAAC8B,OAAO,EAAE;QAChFC,QAAQ,EAAE,IAAI,CAAC/B,OAAO,CAAC+B,QAAQ;QAC/BC,mBAAmB,EAAE,IAAI,CAAChC,OAAO,CAACgC;MACpC,CAAC,CAAC;MACF,OAAOzB,MAAM,CAACC,WAAW,EAAEJ,OAAO,CAAC,CAAC6B,GAAG,CAAC,IAAI,CAACjC,OAAO,CAACkC,UAAU,CAAC;IAClE,CAAC;IAED,OAAOvC,OAAO;EAEhB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}