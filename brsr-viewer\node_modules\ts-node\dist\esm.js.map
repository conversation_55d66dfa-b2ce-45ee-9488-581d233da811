{"version": 3, "file": "esm.js", "sourceRoot": "", "sources": ["../src/esm.ts"], "names": [], "mappings": ";;;AAAA,mCAA6D;AAC7D,6BAMa;AACb,+BAA+B;AAC/B,iCAAiC;AACjC,iCAAwD;AACxD,mCAAuC;AAsFvC,2FAA2F;AAC3F,MAAM,WAAW,GAAG,IAAA,mBAAY,EAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAEnE,gBAAgB;AAChB,SAAgB,uBAAuB,CACrC,KAAgD;IAEhD,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,KAAK,CAAC;IAC5D,6DAA6D;IAC7D,MAAM,QAAQ,GAA8C,WAAW;QACrE,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE;QACrE,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAC7D,OAAO,QAAQ,CAAC;AAClB,CAAC;AATD,0DASC;AAED,gBAAgB;AAChB,SAAgB,yBAAyB,CAAC,IAAsB;IAC9D,sEAAsE;IACtE,MAAM,cAAc,GAAG,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC;IAEtC,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC;AACxC,CAAC;AALD,8DAKC;AAED,SAAgB,cAAc,CAAC,aAAsB;IACnD,aAAa,CAAC,kCAAkC,EAAE,CAAC;IAEnD,yGAAyG;IACzG,MAAM,yBAAyB,GAAG,aAAa,CAAC,kBAAkB,EAAE,CAAC;IACrE,MAAM,2BAA2B,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC;IACxE,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;IAE5C,MAAM,QAAQ,GAAG,uBAAuB,CAAC;QACvC,OAAO;QACP,IAAI;QACJ,SAAS;QACT,eAAe;KAChB,CAAC,CAAC;IAEH,SAAS,6BAA6B,CAAC,MAA0B;QAC/D,mGAAmG;QACnG,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAC5B,OAAO,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,OAAO,CAAC;IACnD,CAAC;IAED;;;OAGG;IACH,SAAS,oBAAoB,CAAC,SAAiB,EAAE,SAAiB;QAChE,OAAO,SAAS,KAAK,SAAS,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IACpE,CAAC;IACD,sDAAsD;IACtD,MAAM,4BAA4B,GAAG,IAAI,GAAG,EAAE,CAAC;IAC/C,MAAM,mCAAmC,GAAG,IAAI,GAAG,EAAE,CAAC;IAEtD,KAAK,UAAU,OAAO,CACpB,SAAiB,EACjB,OAA8B,EAC9B,cAA8B;QAE9B,MAAM,KAAK,GAAG,KAAK,IAAI,EAAE;YACvB,MAAM,CAAC,GAAG,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;YACnE,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,wDAAwD;QACxD,+GAA+G;QAC/G,KAAK,UAAU,kBAAkB,CAC/B,EAA0E;YAE1E,IAAI;gBACF,MAAM,UAAU,GAAG,MAAM,EAAE,EAAE,CAAC;gBAC9B,IACE,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,GAAG;oBACf,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;oBAElD,4BAA4B,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBACnD,OAAO,UAAU,CAAC;aACnB;YAAC,OAAO,gBAAgB,EAAE;gBACzB,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;oBACrD,MAAM,gBAAgB,CAAC;gBACzB,IAAI;oBACF,IAAI,YAAY,GAAG,SAAS,CAAC;oBAC7B,uDAAuD;oBACvD,IAAI;wBACF,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC;4BACjC,YAAY,GAAG,IAAA,mBAAa,EAAC,SAAS,CAAC,CAAC;qBAC3C;oBAAC,MAAM,GAAE;oBACV,MAAM,UAAU,GAAG,IAAA,mBAAa,EAC9B,IAAA,sBAAa,EAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CACnD,CAAC,QAAQ,EAAE,CAAC;oBACb,4BAA4B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAC7C,mCAAmC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACpD,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;iBAChD;gBAAC,OAAO,qBAAqB,EAAE;oBAC9B,MAAM,gBAAgB,CAAC;iBACxB;aACF;QACH,CAAC;QAED,OAAO,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACpC,MAAM,MAAM,GAAG,IAAA,WAAQ,EAAC,SAAS,CAAC,CAAC;YACnC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAEhD,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;gBAC1C,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAClC;YAED,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,OAAO,EAAE;gBAC7C,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAClC;YAED,8DAA8D;YAC9D,IAAI,QAAQ,EAAE;gBACZ,oFAAoF;gBACpF,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAClC;YAED,sCAAsC;YAEtC,OAAO,kBAAkB,CAAC,GAAG,EAAE,CAC7B,yBAAyB,CAAC,cAAc,CACtC,SAAS,EACT,OAAO,EACP,cAAc,CACf,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,KAAK,UAAU,IAAI,CACjB,GAAW,EACX,OAGC,EACD,WAAwB;QAKxB,OAAO,mBAAmB,CAAC,KAAK,IAAI,EAAE;;YACpC,oEAAoE;YACpE,+GAA+G;YAC/G,MAAM,MAAM,GACV,MAAA,OAAO,CAAC,MAAM,mCACd,CACE,MAAM,SAAS,CACb,GAAG,EACH,OAAO,EACP,2BAA2B,CAAC,gBAAgB,CAC7C,CACF,CAAC,MAAM,CAAC;YAEX,IAAI,MAAM,GAAG,SAAS,CAAC;YACvB,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,UAAU,EAAE;gBACjD,+CAA+C;gBAC/C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,WAAW,CAC7C,GAAG,EACH;oBACE,GAAG,OAAO;oBACV,MAAM;iBACP,EACD,WAAW,CACZ,CAAC;gBAEF,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE;oBACjD,MAAM,IAAI,KAAK,CACb,0CAA0C,MAAM,kBAAkB,GAAG,KAAK,CAC3E,CAAC;iBACH;gBAED,uGAAuG;gBACvG,MAAM,sBAAsB,GAA2B,KAAK,EAC1D,MAAM,EACN,QAAQ,EACR,uBAAuB,EACvB,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;gBAElB,oBAAoB;gBACpB,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,eAAe,CACzD,SAAS,EACT,EAAE,GAAG,EAAE,MAAM,EAAE,EACf,sBAAsB,CACvB,CAAC;gBACF,MAAM,GAAG,iBAAiB,CAAC;aAC5B;YAED,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,UAAU,SAAS,CACtB,GAAW,EACX,OAAW,EACX,gBAAkC;QAElC,MAAM,KAAK,GAAG,CAAC,cAAsB,GAAG,EAAE,EAAE,CAC1C,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAE3D,wDAAwD;QACxD,+GAA+G;QAC/G,KAAK,UAAU,kBAAkB,CAC/B,EAAsC;YAEtC,IAAI;gBACF,OAAO,MAAM,EAAE,EAAE,CAAC;aACnB;YAAC,OAAO,cAAc,EAAE;gBACvB,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,CAAC;oBAAE,MAAM,cAAc,CAAC;gBACjE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;aAC/B;QACH,CAAC;QAED,MAAM,MAAM,GAAG,IAAA,WAAQ,EAAC,GAAG,CAAC,CAAC;QAE7B,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAClC;QAED,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAC5B,MAAM,CACJ,QAAQ,KAAK,IAAI,EACjB,2DAA2D,CAC5D,CAAC;QAEF,MAAM,UAAU,GAAG,IAAA,mBAAa,EAAC,GAAG,CAAC,CAAC;QAEtC,IAAI,QAA2C,CAAC;QAEhD,wGAAwG;QACxG,wEAAwE;QACxE,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,UAAU,CAAC,CAAC;QAChC,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACxD,MAAM,iBAAiB,GAAG,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9D,IAAI,iBAAiB,IAAI,CAAC,aAAa,EAAE;YACvC,QAAQ,GAAG,MAAM,kBAAkB,CAAC,GAAG,EAAE,CACvC,KAAK,CAAC,IAAA,YAAS,EAAC,IAAA,mBAAa,EAAC,UAAU,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAChE,CAAC;SACH;aAAM;YACL,IAAI;gBACF,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAC5C;YAAC,OAAO,CAAC,EAAE;gBACV,IACE,CAAC,YAAY,KAAK;oBAClB,aAAa;oBACb,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC9C;oBACA,CAAC,CAAC,OAAO;wBACP,MAAM;4BACN,SAAS;4BACT,8CAA8C;4BAC9C,2HAA2H;4BAC3H,6CAA6C,CAAC;iBACjD;gBACD,MAAM,CAAC,CAAC;aACT;SACF;QACD,0HAA0H;QAC1H,IACE,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC;YAClC,CAAC,QAAQ,CAAC,MAAM,KAAK,UAAU,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,EAChE;YACA,MAAM,EAAE,UAAU,EAAE,GAClB,aAAa,CAAC,oBAAoB,CAAC,mCAAmC,CACpE,IAAA,uBAAgB,EAAC,UAAU,CAAC,CAC7B,CAAC;YACJ,IAAI,UAAU,KAAK,KAAK,EAAE;gBACxB,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;aAC/B;iBAAM,IAAI,UAAU,KAAK,KAAK,EAAE;gBAC/B,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;aAC7B;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,UAAU,eAAe,CAC5B,MAAuB,EACvB,OAAuD,EACvD,sBAA8C;QAE9C,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;SAC9B;QAED,MAAM,KAAK,GAAG,GAAG,EAAE,CACjB,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;QAElE,MAAM,cAAc,GAClB,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEhE,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;QACxB,MAAM,MAAM,GAAG,IAAA,WAAQ,EAAC,GAAG,CAAC,CAAC;QAE7B,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO,KAAK,EAAE,CAAC;SAChB;QACD,MAAM,UAAU,GAAG,IAAA,mBAAa,EAAC,GAAG,CAAC,CAAC;QAEtC,IAAI,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACrC,OAAO,KAAK,EAAE,CAAC;SAChB;QAED,MAAM,SAAS,GAAG,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAEpE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AA7RD,wCA6RC;AAED,KAAK,UAAU,mBAAmB,CAAI,EAAoB;IACxD,MAAM,GAAG,GAAG,MAAM,EAAE,EAAE,CAAC;IACvB,yEAAyE;IACzE,IAAI,GAAG,IAAI,IAAI;QAAE,OAAO,GAAG,CAAC;IAC5B,OAAO;QACL,GAAG,GAAG;QACN,YAAY,EAAE,IAAI;KACnB,CAAC;AACJ,CAAC", "sourcesContent": ["import { register, RegisterOptions, Service } from './index';\nimport {\n  parse as parseUrl,\n  format as formatUrl,\n  UrlWithStringQuery,\n  fileURLToPath,\n  pathToFileURL,\n} from 'url';\nimport { extname } from 'path';\nimport * as assert from 'assert';\nimport { normalizeSlashes, versionGteLt } from './util';\nimport { createRequire } from 'module';\n\n// Note: On Windows, URLs look like this: file:///D:/dev/@TypeStrong/ts-node-examples/foo.ts\n\n// NOTE ABOUT MULTIPLE EXPERIMENTAL LOADER APIS\n//\n// At the time of writing, this file implements 2x different loader APIs.\n// <PERSON>de made a breaking change to the loader API in https://github.com/nodejs/node/pull/37468\n//\n// We check the node version number and export either the *old* or the *new* API.\n//\n// Today, we are implementing the *new* API on top of our implementation of the *old* API,\n// which relies on copy-pasted code from the *old* hooks implementation in node.\n//\n// In the future, we will likely invert this: we will copy-paste the *new* API implementation\n// from node, build our implementation of the *new* API on top of it, and implement the *old*\n// hooks API as a shim to the *new* API.\n\nexport interface NodeLoaderHooksAPI1 {\n  resolve: NodeLoaderHooksAPI1.ResolveHook;\n  getFormat: NodeLoaderHooksAPI1.GetFormatHook;\n  transformSource: NodeLoaderHooksAPI1.TransformSourceHook;\n}\nexport namespace NodeLoaderHooksAPI1 {\n  export type ResolveHook = NodeLoaderHooksAPI2.ResolveHook;\n  export type GetFormatHook = (\n    url: string,\n    context: {},\n    defaultGetFormat: GetFormatHook\n  ) => Promise<{ format: NodeLoaderHooksFormat }>;\n  export type TransformSourceHook = (\n    source: string | Buffer,\n    context: { url: string; format: NodeLoaderHooksFormat },\n    defaultTransformSource: NodeLoaderHooksAPI1.TransformSourceHook\n  ) => Promise<{ source: string | Buffer }>;\n}\n\nexport interface NodeLoaderHooksAPI2 {\n  resolve: NodeLoaderHooksAPI2.ResolveHook;\n  load: NodeLoaderHooksAPI2.LoadHook;\n}\nexport namespace NodeLoaderHooksAPI2 {\n  export type ResolveHook = (\n    specifier: string,\n    context: {\n      conditions?: NodeImportConditions;\n      importAssertions?: NodeImportAssertions;\n      parentURL: string;\n    },\n    defaultResolve: ResolveHook\n  ) => Promise<{\n    url: string;\n    format?: NodeLoaderHooksFormat;\n    shortCircuit?: boolean;\n  }>;\n  export type LoadHook = (\n    url: string,\n    context: {\n      format: NodeLoaderHooksFormat | null | undefined;\n      importAssertions?: NodeImportAssertions;\n    },\n    defaultLoad: NodeLoaderHooksAPI2['load']\n  ) => Promise<{\n    format: NodeLoaderHooksFormat;\n    source: string | Buffer | undefined;\n    shortCircuit?: boolean;\n  }>;\n  export type NodeImportConditions = unknown;\n  export interface NodeImportAssertions {\n    type?: 'json';\n  }\n}\n\nexport type NodeLoaderHooksFormat =\n  | 'builtin'\n  | 'commonjs'\n  | 'dynamic'\n  | 'json'\n  | 'module'\n  | 'wasm';\n\nexport type NodeImportConditions = unknown;\nexport interface NodeImportAssertions {\n  type?: 'json';\n}\n\n// The hooks API changed in node version X so we need to check for backwards compatibility.\nconst newHooksAPI = versionGteLt(process.versions.node, '16.12.0');\n\n/** @internal */\nexport function filterHooksByAPIVersion(\n  hooks: NodeLoaderHooksAPI1 & NodeLoaderHooksAPI2\n): NodeLoaderHooksAPI1 | NodeLoaderHooksAPI2 {\n  const { getFormat, load, resolve, transformSource } = hooks;\n  // Explicit return type to avoid TS's non-ideal inferred type\n  const hooksAPI: NodeLoaderHooksAPI1 | NodeLoaderHooksAPI2 = newHooksAPI\n    ? { resolve, load, getFormat: undefined, transformSource: undefined }\n    : { resolve, getFormat, transformSource, load: undefined };\n  return hooksAPI;\n}\n\n/** @internal */\nexport function registerAndCreateEsmHooks(opts?: RegisterOptions) {\n  // Automatically performs registration just like `-r ts-node/register`\n  const tsNodeInstance = register(opts);\n\n  return createEsmHooks(tsNodeInstance);\n}\n\nexport function createEsmHooks(tsNodeService: Service) {\n  tsNodeService.enableExperimentalEsmLoaderInterop();\n\n  // Custom implementation that considers additional file extensions and automatically adds file extensions\n  const nodeResolveImplementation = tsNodeService.getNodeEsmResolver();\n  const nodeGetFormatImplementation = tsNodeService.getNodeEsmGetFormat();\n  const extensions = tsNodeService.extensions;\n\n  const hooksAPI = filterHooksByAPIVersion({\n    resolve,\n    load,\n    getFormat,\n    transformSource,\n  });\n\n  function isFileUrlOrNodeStyleSpecifier(parsed: UrlWithStringQuery) {\n    // We only understand file:// URLs, but in node, the specifier can be a node-style `./foo` or `foo`\n    const { protocol } = parsed;\n    return protocol === null || protocol === 'file:';\n  }\n\n  /**\n   * Named \"probably\" as a reminder that this is a guess.\n   * node does not explicitly tell us if we're resolving the entrypoint or not.\n   */\n  function isProbablyEntrypoint(specifier: string, parentURL: string) {\n    return parentURL === undefined && specifier.startsWith('file://');\n  }\n  // Side-channel between `resolve()` and `load()` hooks\n  const rememberIsProbablyEntrypoint = new Set();\n  const rememberResolvedViaCommonjsFallback = new Set();\n\n  async function resolve(\n    specifier: string,\n    context: { parentURL: string },\n    defaultResolve: typeof resolve\n  ): Promise<{ url: string; format?: NodeLoaderHooksFormat }> {\n    const defer = async () => {\n      const r = await defaultResolve(specifier, context, defaultResolve);\n      return r;\n    };\n    // See: https://github.com/nodejs/node/discussions/41711\n    // nodejs will likely implement a similar fallback.  Till then, we can do our users a favor and fallback today.\n    async function entrypointFallback(\n      cb: () => ReturnType<typeof resolve> | Awaited<ReturnType<typeof resolve>>\n    ): ReturnType<typeof resolve> {\n      try {\n        const resolution = await cb();\n        if (\n          resolution?.url &&\n          isProbablyEntrypoint(specifier, context.parentURL)\n        )\n          rememberIsProbablyEntrypoint.add(resolution.url);\n        return resolution;\n      } catch (esmResolverError) {\n        if (!isProbablyEntrypoint(specifier, context.parentURL))\n          throw esmResolverError;\n        try {\n          let cjsSpecifier = specifier;\n          // Attempt to convert from ESM file:// to CommonJS path\n          try {\n            if (specifier.startsWith('file://'))\n              cjsSpecifier = fileURLToPath(specifier);\n          } catch {}\n          const resolution = pathToFileURL(\n            createRequire(process.cwd()).resolve(cjsSpecifier)\n          ).toString();\n          rememberIsProbablyEntrypoint.add(resolution);\n          rememberResolvedViaCommonjsFallback.add(resolution);\n          return { url: resolution, format: 'commonjs' };\n        } catch (commonjsResolverError) {\n          throw esmResolverError;\n        }\n      }\n    }\n\n    return addShortCircuitFlag(async () => {\n      const parsed = parseUrl(specifier);\n      const { pathname, protocol, hostname } = parsed;\n\n      if (!isFileUrlOrNodeStyleSpecifier(parsed)) {\n        return entrypointFallback(defer);\n      }\n\n      if (protocol !== null && protocol !== 'file:') {\n        return entrypointFallback(defer);\n      }\n\n      // Malformed file:// URL?  We should always see `null` or `''`\n      if (hostname) {\n        // TODO file://./foo sets `hostname` to `'.'`.  Perhaps we should special-case this.\n        return entrypointFallback(defer);\n      }\n\n      // pathname is the path to be resolved\n\n      return entrypointFallback(() =>\n        nodeResolveImplementation.defaultResolve(\n          specifier,\n          context,\n          defaultResolve\n        )\n      );\n    });\n  }\n\n  // `load` from new loader hook API (See description at the top of this file)\n  async function load(\n    url: string,\n    context: {\n      format: NodeLoaderHooksFormat | null | undefined;\n      importAssertions?: NodeLoaderHooksAPI2.NodeImportAssertions;\n    },\n    defaultLoad: typeof load\n  ): Promise<{\n    format: NodeLoaderHooksFormat;\n    source: string | Buffer | undefined;\n  }> {\n    return addShortCircuitFlag(async () => {\n      // If we get a format hint from resolve() on the context then use it\n      // otherwise call the old getFormat() hook using node's old built-in defaultGetFormat() that ships with ts-node\n      const format =\n        context.format ??\n        (\n          await getFormat(\n            url,\n            context,\n            nodeGetFormatImplementation.defaultGetFormat\n          )\n        ).format;\n\n      let source = undefined;\n      if (format !== 'builtin' && format !== 'commonjs') {\n        // Call the new defaultLoad() to get the source\n        const { source: rawSource } = await defaultLoad(\n          url,\n          {\n            ...context,\n            format,\n          },\n          defaultLoad\n        );\n\n        if (rawSource === undefined || rawSource === null) {\n          throw new Error(\n            `Failed to load raw source: Format was '${format}' and url was '${url}''.`\n          );\n        }\n\n        // Emulate node's built-in old defaultTransformSource() so we can re-use the old transformSource() hook\n        const defaultTransformSource: typeof transformSource = async (\n          source,\n          _context,\n          _defaultTransformSource\n        ) => ({ source });\n\n        // Call the old hook\n        const { source: transformedSource } = await transformSource(\n          rawSource,\n          { url, format },\n          defaultTransformSource\n        );\n        source = transformedSource;\n      }\n\n      return { format, source };\n    });\n  }\n\n  async function getFormat(\n    url: string,\n    context: {},\n    defaultGetFormat: typeof getFormat\n  ): Promise<{ format: NodeLoaderHooksFormat }> {\n    const defer = (overrideUrl: string = url) =>\n      defaultGetFormat(overrideUrl, context, defaultGetFormat);\n\n    // See: https://github.com/nodejs/node/discussions/41711\n    // nodejs will likely implement a similar fallback.  Till then, we can do our users a favor and fallback today.\n    async function entrypointFallback(\n      cb: () => ReturnType<typeof getFormat>\n    ): ReturnType<typeof getFormat> {\n      try {\n        return await cb();\n      } catch (getFormatError) {\n        if (!rememberIsProbablyEntrypoint.has(url)) throw getFormatError;\n        return { format: 'commonjs' };\n      }\n    }\n\n    const parsed = parseUrl(url);\n\n    if (!isFileUrlOrNodeStyleSpecifier(parsed)) {\n      return entrypointFallback(defer);\n    }\n\n    const { pathname } = parsed;\n    assert(\n      pathname !== null,\n      'ESM getFormat() hook: URL should never have null pathname'\n    );\n\n    const nativePath = fileURLToPath(url);\n\n    let nodeSays: { format: NodeLoaderHooksFormat };\n\n    // If file has extension not understood by node, then ask node how it would treat the emitted extension.\n    // E.g. .mts compiles to .mjs, so ask node how to classify an .mjs file.\n    const ext = extname(nativePath);\n    const tsNodeIgnored = tsNodeService.ignored(nativePath);\n    const nodeEquivalentExt = extensions.nodeEquivalents.get(ext);\n    if (nodeEquivalentExt && !tsNodeIgnored) {\n      nodeSays = await entrypointFallback(() =>\n        defer(formatUrl(pathToFileURL(nativePath + nodeEquivalentExt)))\n      );\n    } else {\n      try {\n        nodeSays = await entrypointFallback(defer);\n      } catch (e) {\n        if (\n          e instanceof Error &&\n          tsNodeIgnored &&\n          extensions.nodeDoesNotUnderstand.includes(ext)\n        ) {\n          e.message +=\n            `\\n\\n` +\n            `Hint:\\n` +\n            `ts-node is configured to ignore this file.\\n` +\n            `If you want ts-node to handle this file, consider enabling the \"skipIgnore\" option or adjusting your \"ignore\" patterns.\\n` +\n            `https://typestrong.org/ts-node/docs/scope\\n`;\n        }\n        throw e;\n      }\n    }\n    // For files compiled by ts-node that node believes are either CJS or ESM, check if we should override that classification\n    if (\n      !tsNodeService.ignored(nativePath) &&\n      (nodeSays.format === 'commonjs' || nodeSays.format === 'module')\n    ) {\n      const { moduleType } =\n        tsNodeService.moduleTypeClassifier.classifyModuleByModuleTypeOverrides(\n          normalizeSlashes(nativePath)\n        );\n      if (moduleType === 'cjs') {\n        return { format: 'commonjs' };\n      } else if (moduleType === 'esm') {\n        return { format: 'module' };\n      }\n    }\n    return nodeSays;\n  }\n\n  async function transformSource(\n    source: string | Buffer,\n    context: { url: string; format: NodeLoaderHooksFormat },\n    defaultTransformSource: typeof transformSource\n  ): Promise<{ source: string | Buffer }> {\n    if (source === null || source === undefined) {\n      throw new Error('No source');\n    }\n\n    const defer = () =>\n      defaultTransformSource(source, context, defaultTransformSource);\n\n    const sourceAsString =\n      typeof source === 'string' ? source : source.toString('utf8');\n\n    const { url } = context;\n    const parsed = parseUrl(url);\n\n    if (!isFileUrlOrNodeStyleSpecifier(parsed)) {\n      return defer();\n    }\n    const nativePath = fileURLToPath(url);\n\n    if (tsNodeService.ignored(nativePath)) {\n      return defer();\n    }\n\n    const emittedJs = tsNodeService.compile(sourceAsString, nativePath);\n\n    return { source: emittedJs };\n  }\n\n  return hooksAPI;\n}\n\nasync function addShortCircuitFlag<T>(fn: () => Promise<T>) {\n  const ret = await fn();\n  // Not sure if this is necessary; being lazy.  Can revisit in the future.\n  if (ret == null) return ret;\n  return {\n    ...ret,\n    shortCircuit: true,\n  };\n}\n"]}