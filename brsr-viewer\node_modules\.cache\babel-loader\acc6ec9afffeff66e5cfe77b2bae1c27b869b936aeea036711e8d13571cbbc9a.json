{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLNode,\n    XMLRaw,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  NodeType = require('./NodeType');\n  XMLNode = require('./XMLNode');\n  module.exports = XMLRaw = function (superClass) {\n    extend(XMLRaw, superClass);\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.type = NodeType.Raw;\n      this.value = this.stringify.raw(text);\n    }\n    XMLRaw.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLRaw.prototype.toString = function (options) {\n      return this.options.writer.raw(this, this.options.writer.filterOptions(options));\n    };\n    return XMLRaw;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLNode", "XMLRaw", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "text", "Error", "debugInfo", "type", "Raw", "value", "stringify", "raw", "clone", "Object", "create", "toString", "options", "writer", "filterOptions"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLRaw.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.type = NodeType.Raw;\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.raw(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,OAAO;IAAEC,MAAM;IAC3BC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7Bb,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCb,OAAO,GAAGa,OAAO,CAAC,WAAW,CAAC;EAE9BC,MAAM,CAACC,OAAO,GAAGd,MAAM,GAAI,UAASe,UAAU,EAAE;IAC9Cd,MAAM,CAACD,MAAM,EAAEe,UAAU,CAAC;IAE1B,SAASf,MAAMA,CAACG,MAAM,EAAEa,IAAI,EAAE;MAC5BhB,MAAM,CAACU,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MAC/C,IAAIa,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAC1D;MACA,IAAI,CAACC,IAAI,GAAGrB,QAAQ,CAACsB,GAAG;MACxB,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAACC,GAAG,CAACP,IAAI,CAAC;IACvC;IAEAhB,MAAM,CAACS,SAAS,CAACe,KAAK,GAAG,YAAW;MAClC,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED1B,MAAM,CAACS,SAAS,CAACkB,QAAQ,GAAG,UAASC,OAAO,EAAE;MAC5C,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACN,GAAG,CAAC,IAAI,EAAE,IAAI,CAACK,OAAO,CAACC,MAAM,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC;IAClF,CAAC;IAED,OAAO5B,MAAM;EAEf,CAAC,CAAED,OAAO,CAAC;AAEb,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}