{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\nvar Buffer = require('safe-buffer').Buffer;\n/*</replacement>*/\n\nvar isEncoding = Buffer.isEncoding || function (encoding) {\n  encoding = '' + encoding;\n  switch (encoding && encoding.toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n    case 'raw':\n      return true;\n    default:\n      return false;\n  }\n};\nfunction _normalizeEncoding(enc) {\n  if (!enc) return 'utf8';\n  var retried;\n  while (true) {\n    switch (enc) {\n      case 'utf8':\n      case 'utf-8':\n        return 'utf8';\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return 'utf16le';\n      case 'latin1':\n      case 'binary':\n        return 'latin1';\n      case 'base64':\n      case 'ascii':\n      case 'hex':\n        return enc;\n      default:\n        if (retried) return; // undefined\n        enc = ('' + enc).toLowerCase();\n        retried = true;\n    }\n  }\n}\n;\n\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n  var nenc = _normalizeEncoding(enc);\n  if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);\n  return nenc || enc;\n}\n\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n  this.encoding = normalizeEncoding(encoding);\n  var nb;\n  switch (this.encoding) {\n    case 'utf16le':\n      this.text = utf16Text;\n      this.end = utf16End;\n      nb = 4;\n      break;\n    case 'utf8':\n      this.fillLast = utf8FillLast;\n      nb = 4;\n      break;\n    case 'base64':\n      this.text = base64Text;\n      this.end = base64End;\n      nb = 3;\n      break;\n    default:\n      this.write = simpleWrite;\n      this.end = simpleEnd;\n      return;\n  }\n  this.lastNeed = 0;\n  this.lastTotal = 0;\n  this.lastChar = Buffer.allocUnsafe(nb);\n}\nStringDecoder.prototype.write = function (buf) {\n  if (buf.length === 0) return '';\n  var r;\n  var i;\n  if (this.lastNeed) {\n    r = this.fillLast(buf);\n    if (r === undefined) return '';\n    i = this.lastNeed;\n    this.lastNeed = 0;\n  } else {\n    i = 0;\n  }\n  if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n  return r || '';\n};\nStringDecoder.prototype.end = utf8End;\n\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function (buf) {\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n  this.lastNeed -= buf.length;\n};\n\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n  if (byte <= 0x7F) return 0;else if (byte >> 5 === 0x06) return 2;else if (byte >> 4 === 0x0E) return 3;else if (byte >> 3 === 0x1E) return 4;\n  return byte >> 6 === 0x02 ? -1 : -2;\n}\n\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n  var j = buf.length - 1;\n  if (j < i) return 0;\n  var nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 1;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 2;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) {\n      if (nb === 2) nb = 0;else self.lastNeed = nb - 3;\n    }\n    return nb;\n  }\n  return 0;\n}\n\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n  if ((buf[0] & 0xC0) !== 0x80) {\n    self.lastNeed = 0;\n    return '\\ufffd';\n  }\n  if (self.lastNeed > 1 && buf.length > 1) {\n    if ((buf[1] & 0xC0) !== 0x80) {\n      self.lastNeed = 1;\n      return '\\ufffd';\n    }\n    if (self.lastNeed > 2 && buf.length > 2) {\n      if ((buf[2] & 0xC0) !== 0x80) {\n        self.lastNeed = 2;\n        return '\\ufffd';\n      }\n    }\n  }\n}\n\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n  var p = this.lastTotal - this.lastNeed;\n  var r = utf8CheckExtraBytes(this, buf, p);\n  if (r !== undefined) return r;\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, p, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, p, 0, buf.length);\n  this.lastNeed -= buf.length;\n}\n\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n  var total = utf8CheckIncomplete(this, buf, i);\n  if (!this.lastNeed) return buf.toString('utf8', i);\n  this.lastTotal = total;\n  var end = buf.length - (total - this.lastNeed);\n  buf.copy(this.lastChar, 0, end);\n  return buf.toString('utf8', i, end);\n}\n\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + '\\ufffd';\n  return r;\n}\n\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n  if ((buf.length - i) % 2 === 0) {\n    var r = buf.toString('utf16le', i);\n    if (r) {\n      var c = r.charCodeAt(r.length - 1);\n      if (c >= 0xD800 && c <= 0xDBFF) {\n        this.lastNeed = 2;\n        this.lastTotal = 4;\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n        return r.slice(0, -1);\n      }\n    }\n    return r;\n  }\n  this.lastNeed = 1;\n  this.lastTotal = 2;\n  this.lastChar[0] = buf[buf.length - 1];\n  return buf.toString('utf16le', i, buf.length - 1);\n}\n\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) {\n    var end = this.lastTotal - this.lastNeed;\n    return r + this.lastChar.toString('utf16le', 0, end);\n  }\n  return r;\n}\nfunction base64Text(buf, i) {\n  var n = (buf.length - i) % 3;\n  if (n === 0) return buf.toString('base64', i);\n  this.lastNeed = 3 - n;\n  this.lastTotal = 3;\n  if (n === 1) {\n    this.lastChar[0] = buf[buf.length - 1];\n  } else {\n    this.lastChar[0] = buf[buf.length - 2];\n    this.lastChar[1] = buf[buf.length - 1];\n  }\n  return buf.toString('base64', i, buf.length - n);\n}\nfunction base64End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);\n  return r;\n}\n\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n  return buf.toString(this.encoding);\n}\nfunction simpleEnd(buf) {\n  return buf && buf.length ? this.write(buf) : '';\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "isEncoding", "encoding", "toLowerCase", "_normalizeEncoding", "enc", "retried", "normalizeEncoding", "nenc", "Error", "exports", "StringDecoder", "nb", "text", "utf16Text", "end", "utf16End", "fillLast", "utf8FillLast", "base64Text", "base64End", "write", "simpleWrite", "simpleEnd", "lastNeed", "lastTotal", "lastChar", "allocUnsafe", "prototype", "buf", "length", "r", "i", "undefined", "utf8End", "utf8Text", "copy", "toString", "utf8CheckByte", "byte", "utf8CheckIncomplete", "self", "j", "utf8CheckExtraBytes", "p", "total", "c", "charCodeAt", "slice", "n"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/string_decoder/lib/string_decoder.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\n/*</replacement>*/\n\nvar isEncoding = Buffer.isEncoding || function (encoding) {\n  encoding = '' + encoding;\n  switch (encoding && encoding.toLowerCase()) {\n    case 'hex':case 'utf8':case 'utf-8':case 'ascii':case 'binary':case 'base64':case 'ucs2':case 'ucs-2':case 'utf16le':case 'utf-16le':case 'raw':\n      return true;\n    default:\n      return false;\n  }\n};\n\nfunction _normalizeEncoding(enc) {\n  if (!enc) return 'utf8';\n  var retried;\n  while (true) {\n    switch (enc) {\n      case 'utf8':\n      case 'utf-8':\n        return 'utf8';\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return 'utf16le';\n      case 'latin1':\n      case 'binary':\n        return 'latin1';\n      case 'base64':\n      case 'ascii':\n      case 'hex':\n        return enc;\n      default:\n        if (retried) return; // undefined\n        enc = ('' + enc).toLowerCase();\n        retried = true;\n    }\n  }\n};\n\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n  var nenc = _normalizeEncoding(enc);\n  if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);\n  return nenc || enc;\n}\n\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n  this.encoding = normalizeEncoding(encoding);\n  var nb;\n  switch (this.encoding) {\n    case 'utf16le':\n      this.text = utf16Text;\n      this.end = utf16End;\n      nb = 4;\n      break;\n    case 'utf8':\n      this.fillLast = utf8FillLast;\n      nb = 4;\n      break;\n    case 'base64':\n      this.text = base64Text;\n      this.end = base64End;\n      nb = 3;\n      break;\n    default:\n      this.write = simpleWrite;\n      this.end = simpleEnd;\n      return;\n  }\n  this.lastNeed = 0;\n  this.lastTotal = 0;\n  this.lastChar = Buffer.allocUnsafe(nb);\n}\n\nStringDecoder.prototype.write = function (buf) {\n  if (buf.length === 0) return '';\n  var r;\n  var i;\n  if (this.lastNeed) {\n    r = this.fillLast(buf);\n    if (r === undefined) return '';\n    i = this.lastNeed;\n    this.lastNeed = 0;\n  } else {\n    i = 0;\n  }\n  if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n  return r || '';\n};\n\nStringDecoder.prototype.end = utf8End;\n\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function (buf) {\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n  this.lastNeed -= buf.length;\n};\n\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n  if (byte <= 0x7F) return 0;else if (byte >> 5 === 0x06) return 2;else if (byte >> 4 === 0x0E) return 3;else if (byte >> 3 === 0x1E) return 4;\n  return byte >> 6 === 0x02 ? -1 : -2;\n}\n\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n  var j = buf.length - 1;\n  if (j < i) return 0;\n  var nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 1;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 2;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) {\n      if (nb === 2) nb = 0;else self.lastNeed = nb - 3;\n    }\n    return nb;\n  }\n  return 0;\n}\n\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n  if ((buf[0] & 0xC0) !== 0x80) {\n    self.lastNeed = 0;\n    return '\\ufffd';\n  }\n  if (self.lastNeed > 1 && buf.length > 1) {\n    if ((buf[1] & 0xC0) !== 0x80) {\n      self.lastNeed = 1;\n      return '\\ufffd';\n    }\n    if (self.lastNeed > 2 && buf.length > 2) {\n      if ((buf[2] & 0xC0) !== 0x80) {\n        self.lastNeed = 2;\n        return '\\ufffd';\n      }\n    }\n  }\n}\n\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n  var p = this.lastTotal - this.lastNeed;\n  var r = utf8CheckExtraBytes(this, buf, p);\n  if (r !== undefined) return r;\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, p, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, p, 0, buf.length);\n  this.lastNeed -= buf.length;\n}\n\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n  var total = utf8CheckIncomplete(this, buf, i);\n  if (!this.lastNeed) return buf.toString('utf8', i);\n  this.lastTotal = total;\n  var end = buf.length - (total - this.lastNeed);\n  buf.copy(this.lastChar, 0, end);\n  return buf.toString('utf8', i, end);\n}\n\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + '\\ufffd';\n  return r;\n}\n\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n  if ((buf.length - i) % 2 === 0) {\n    var r = buf.toString('utf16le', i);\n    if (r) {\n      var c = r.charCodeAt(r.length - 1);\n      if (c >= 0xD800 && c <= 0xDBFF) {\n        this.lastNeed = 2;\n        this.lastTotal = 4;\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n        return r.slice(0, -1);\n      }\n    }\n    return r;\n  }\n  this.lastNeed = 1;\n  this.lastTotal = 2;\n  this.lastChar[0] = buf[buf.length - 1];\n  return buf.toString('utf16le', i, buf.length - 1);\n}\n\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) {\n    var end = this.lastTotal - this.lastNeed;\n    return r + this.lastChar.toString('utf16le', 0, end);\n  }\n  return r;\n}\n\nfunction base64Text(buf, i) {\n  var n = (buf.length - i) % 3;\n  if (n === 0) return buf.toString('base64', i);\n  this.lastNeed = 3 - n;\n  this.lastTotal = 3;\n  if (n === 1) {\n    this.lastChar[0] = buf[buf.length - 1];\n  } else {\n    this.lastChar[0] = buf[buf.length - 2];\n    this.lastChar[1] = buf[buf.length - 1];\n  }\n  return buf.toString('base64', i, buf.length - n);\n}\n\nfunction base64End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);\n  return r;\n}\n\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n  return buf.toString(this.encoding);\n}\n\nfunction simpleEnd(buf) {\n  return buf && buf.length ? this.write(buf) : '';\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AAEA,IAAIA,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC,CAACD,MAAM;AAC1C;;AAEA,IAAIE,UAAU,GAAGF,MAAM,CAACE,UAAU,IAAI,UAAUC,QAAQ,EAAE;EACxDA,QAAQ,GAAG,EAAE,GAAGA,QAAQ;EACxB,QAAQA,QAAQ,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC;IACxC,KAAK,KAAK;IAAC,KAAK,MAAM;IAAC,KAAK,OAAO;IAAC,KAAK,OAAO;IAAC,KAAK,QAAQ;IAAC,KAAK,QAAQ;IAAC,KAAK,MAAM;IAAC,KAAK,OAAO;IAAC,KAAK,SAAS;IAAC,KAAK,UAAU;IAAC,KAAK,KAAK;MAC7I,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF,CAAC;AAED,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,IAAI,CAACA,GAAG,EAAE,OAAO,MAAM;EACvB,IAAIC,OAAO;EACX,OAAO,IAAI,EAAE;IACX,QAAQD,GAAG;MACT,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAO,MAAM;MACf,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,QAAQ;MACb,KAAK,OAAO;MACZ,KAAK,KAAK;QACR,OAAOA,GAAG;MACZ;QACE,IAAIC,OAAO,EAAE,OAAO,CAAC;QACrBD,GAAG,GAAG,CAAC,EAAE,GAAGA,GAAG,EAAEF,WAAW,CAAC,CAAC;QAC9BG,OAAO,GAAG,IAAI;IAClB;EACF;AACF;AAAC;;AAED;AACA;AACA,SAASC,iBAAiBA,CAACF,GAAG,EAAE;EAC9B,IAAIG,IAAI,GAAGJ,kBAAkB,CAACC,GAAG,CAAC;EAClC,IAAI,OAAOG,IAAI,KAAK,QAAQ,KAAKT,MAAM,CAACE,UAAU,KAAKA,UAAU,IAAI,CAACA,UAAU,CAACI,GAAG,CAAC,CAAC,EAAE,MAAM,IAAII,KAAK,CAAC,oBAAoB,GAAGJ,GAAG,CAAC;EACnI,OAAOG,IAAI,IAAIH,GAAG;AACpB;;AAEA;AACA;AACA;AACAK,OAAO,CAACC,aAAa,GAAGA,aAAa;AACrC,SAASA,aAAaA,CAACT,QAAQ,EAAE;EAC/B,IAAI,CAACA,QAAQ,GAAGK,iBAAiB,CAACL,QAAQ,CAAC;EAC3C,IAAIU,EAAE;EACN,QAAQ,IAAI,CAACV,QAAQ;IACnB,KAAK,SAAS;MACZ,IAAI,CAACW,IAAI,GAAGC,SAAS;MACrB,IAAI,CAACC,GAAG,GAAGC,QAAQ;MACnBJ,EAAE,GAAG,CAAC;MACN;IACF,KAAK,MAAM;MACT,IAAI,CAACK,QAAQ,GAAGC,YAAY;MAC5BN,EAAE,GAAG,CAAC;MACN;IACF,KAAK,QAAQ;MACX,IAAI,CAACC,IAAI,GAAGM,UAAU;MACtB,IAAI,CAACJ,GAAG,GAAGK,SAAS;MACpBR,EAAE,GAAG,CAAC;MACN;IACF;MACE,IAAI,CAACS,KAAK,GAAGC,WAAW;MACxB,IAAI,CAACP,GAAG,GAAGQ,SAAS;MACpB;EACJ;EACA,IAAI,CAACC,QAAQ,GAAG,CAAC;EACjB,IAAI,CAACC,SAAS,GAAG,CAAC;EAClB,IAAI,CAACC,QAAQ,GAAG3B,MAAM,CAAC4B,WAAW,CAACf,EAAE,CAAC;AACxC;AAEAD,aAAa,CAACiB,SAAS,CAACP,KAAK,GAAG,UAAUQ,GAAG,EAAE;EAC7C,IAAIA,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAC/B,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,IAAI,IAAI,CAACR,QAAQ,EAAE;IACjBO,CAAC,GAAG,IAAI,CAACd,QAAQ,CAACY,GAAG,CAAC;IACtB,IAAIE,CAAC,KAAKE,SAAS,EAAE,OAAO,EAAE;IAC9BD,CAAC,GAAG,IAAI,CAACR,QAAQ;IACjB,IAAI,CAACA,QAAQ,GAAG,CAAC;EACnB,CAAC,MAAM;IACLQ,CAAC,GAAG,CAAC;EACP;EACA,IAAIA,CAAC,GAAGH,GAAG,CAACC,MAAM,EAAE,OAAOC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAClB,IAAI,CAACgB,GAAG,EAAEG,CAAC,CAAC,GAAG,IAAI,CAACnB,IAAI,CAACgB,GAAG,EAAEG,CAAC,CAAC;EACxE,OAAOD,CAAC,IAAI,EAAE;AAChB,CAAC;AAEDpB,aAAa,CAACiB,SAAS,CAACb,GAAG,GAAGmB,OAAO;;AAErC;AACAvB,aAAa,CAACiB,SAAS,CAACf,IAAI,GAAGsB,QAAQ;;AAEvC;AACAxB,aAAa,CAACiB,SAAS,CAACX,QAAQ,GAAG,UAAUY,GAAG,EAAE;EAChD,IAAI,IAAI,CAACL,QAAQ,IAAIK,GAAG,CAACC,MAAM,EAAE;IAC/BD,GAAG,CAACO,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAQ,EAAE,CAAC,EAAE,IAAI,CAACA,QAAQ,CAAC;IACzE,OAAO,IAAI,CAACE,QAAQ,CAACW,QAAQ,CAAC,IAAI,CAACnC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAACuB,SAAS,CAAC;EACjE;EACAI,GAAG,CAACO,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAQ,EAAE,CAAC,EAAEK,GAAG,CAACC,MAAM,CAAC;EACtE,IAAI,CAACN,QAAQ,IAAIK,GAAG,CAACC,MAAM;AAC7B,CAAC;;AAED;AACA;AACA,SAASQ,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,IAAIA,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,IAAIA,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,IAAIA,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC;EAC5I,OAAOA,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACrC;;AAEA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,IAAI,EAAEZ,GAAG,EAAEG,CAAC,EAAE;EACzC,IAAIU,CAAC,GAAGb,GAAG,CAACC,MAAM,GAAG,CAAC;EACtB,IAAIY,CAAC,GAAGV,CAAC,EAAE,OAAO,CAAC;EACnB,IAAIpB,EAAE,GAAG0B,aAAa,CAACT,GAAG,CAACa,CAAC,CAAC,CAAC;EAC9B,IAAI9B,EAAE,IAAI,CAAC,EAAE;IACX,IAAIA,EAAE,GAAG,CAAC,EAAE6B,IAAI,CAACjB,QAAQ,GAAGZ,EAAE,GAAG,CAAC;IAClC,OAAOA,EAAE;EACX;EACA,IAAI,EAAE8B,CAAC,GAAGV,CAAC,IAAIpB,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;EAClCA,EAAE,GAAG0B,aAAa,CAACT,GAAG,CAACa,CAAC,CAAC,CAAC;EAC1B,IAAI9B,EAAE,IAAI,CAAC,EAAE;IACX,IAAIA,EAAE,GAAG,CAAC,EAAE6B,IAAI,CAACjB,QAAQ,GAAGZ,EAAE,GAAG,CAAC;IAClC,OAAOA,EAAE;EACX;EACA,IAAI,EAAE8B,CAAC,GAAGV,CAAC,IAAIpB,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;EAClCA,EAAE,GAAG0B,aAAa,CAACT,GAAG,CAACa,CAAC,CAAC,CAAC;EAC1B,IAAI9B,EAAE,IAAI,CAAC,EAAE;IACX,IAAIA,EAAE,GAAG,CAAC,EAAE;MACV,IAAIA,EAAE,KAAK,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC,KAAK6B,IAAI,CAACjB,QAAQ,GAAGZ,EAAE,GAAG,CAAC;IAClD;IACA,OAAOA,EAAE;EACX;EACA,OAAO,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+B,mBAAmBA,CAACF,IAAI,EAAEZ,GAAG,EAAEe,CAAC,EAAE;EACzC,IAAI,CAACf,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;IAC5BY,IAAI,CAACjB,QAAQ,GAAG,CAAC;IACjB,OAAO,QAAQ;EACjB;EACA,IAAIiB,IAAI,CAACjB,QAAQ,GAAG,CAAC,IAAIK,GAAG,CAACC,MAAM,GAAG,CAAC,EAAE;IACvC,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;MAC5BY,IAAI,CAACjB,QAAQ,GAAG,CAAC;MACjB,OAAO,QAAQ;IACjB;IACA,IAAIiB,IAAI,CAACjB,QAAQ,GAAG,CAAC,IAAIK,GAAG,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;QAC5BY,IAAI,CAACjB,QAAQ,GAAG,CAAC;QACjB,OAAO,QAAQ;MACjB;IACF;EACF;AACF;;AAEA;AACA,SAASN,YAAYA,CAACW,GAAG,EAAE;EACzB,IAAIe,CAAC,GAAG,IAAI,CAACnB,SAAS,GAAG,IAAI,CAACD,QAAQ;EACtC,IAAIO,CAAC,GAAGY,mBAAmB,CAAC,IAAI,EAAEd,GAAG,EAAEe,CAAC,CAAC;EACzC,IAAIb,CAAC,KAAKE,SAAS,EAAE,OAAOF,CAAC;EAC7B,IAAI,IAAI,CAACP,QAAQ,IAAIK,GAAG,CAACC,MAAM,EAAE;IAC/BD,GAAG,CAACO,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAEkB,CAAC,EAAE,CAAC,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAC5C,OAAO,IAAI,CAACE,QAAQ,CAACW,QAAQ,CAAC,IAAI,CAACnC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAACuB,SAAS,CAAC;EACjE;EACAI,GAAG,CAACO,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAEkB,CAAC,EAAE,CAAC,EAAEf,GAAG,CAACC,MAAM,CAAC;EACzC,IAAI,CAACN,QAAQ,IAAIK,GAAG,CAACC,MAAM;AAC7B;;AAEA;AACA;AACA;AACA,SAASK,QAAQA,CAACN,GAAG,EAAEG,CAAC,EAAE;EACxB,IAAIa,KAAK,GAAGL,mBAAmB,CAAC,IAAI,EAAEX,GAAG,EAAEG,CAAC,CAAC;EAC7C,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE,OAAOK,GAAG,CAACQ,QAAQ,CAAC,MAAM,EAAEL,CAAC,CAAC;EAClD,IAAI,CAACP,SAAS,GAAGoB,KAAK;EACtB,IAAI9B,GAAG,GAAGc,GAAG,CAACC,MAAM,IAAIe,KAAK,GAAG,IAAI,CAACrB,QAAQ,CAAC;EAC9CK,GAAG,CAACO,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE,CAAC,EAAEX,GAAG,CAAC;EAC/B,OAAOc,GAAG,CAACQ,QAAQ,CAAC,MAAM,EAAEL,CAAC,EAAEjB,GAAG,CAAC;AACrC;;AAEA;AACA;AACA,SAASmB,OAAOA,CAACL,GAAG,EAAE;EACpB,IAAIE,CAAC,GAAGF,GAAG,IAAIA,GAAG,CAACC,MAAM,GAAG,IAAI,CAACT,KAAK,CAACQ,GAAG,CAAC,GAAG,EAAE;EAChD,IAAI,IAAI,CAACL,QAAQ,EAAE,OAAOO,CAAC,GAAG,QAAQ;EACtC,OAAOA,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA,SAASjB,SAASA,CAACe,GAAG,EAAEG,CAAC,EAAE;EACzB,IAAI,CAACH,GAAG,CAACC,MAAM,GAAGE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC9B,IAAID,CAAC,GAAGF,GAAG,CAACQ,QAAQ,CAAC,SAAS,EAAEL,CAAC,CAAC;IAClC,IAAID,CAAC,EAAE;MACL,IAAIe,CAAC,GAAGf,CAAC,CAACgB,UAAU,CAAChB,CAAC,CAACD,MAAM,GAAG,CAAC,CAAC;MAClC,IAAIgB,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;QAC9B,IAAI,CAACtB,QAAQ,GAAG,CAAC;QACjB,IAAI,CAACC,SAAS,GAAG,CAAC;QAClB,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAGG,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;QACtC,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC,GAAGG,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;QACtC,OAAOC,CAAC,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAOjB,CAAC;EACV;EACA,IAAI,CAACP,QAAQ,GAAG,CAAC;EACjB,IAAI,CAACC,SAAS,GAAG,CAAC;EAClB,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAGG,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;EACtC,OAAOD,GAAG,CAACQ,QAAQ,CAAC,SAAS,EAAEL,CAAC,EAAEH,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;AACnD;;AAEA;AACA;AACA,SAASd,QAAQA,CAACa,GAAG,EAAE;EACrB,IAAIE,CAAC,GAAGF,GAAG,IAAIA,GAAG,CAACC,MAAM,GAAG,IAAI,CAACT,KAAK,CAACQ,GAAG,CAAC,GAAG,EAAE;EAChD,IAAI,IAAI,CAACL,QAAQ,EAAE;IACjB,IAAIT,GAAG,GAAG,IAAI,CAACU,SAAS,GAAG,IAAI,CAACD,QAAQ;IACxC,OAAOO,CAAC,GAAG,IAAI,CAACL,QAAQ,CAACW,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAEtB,GAAG,CAAC;EACtD;EACA,OAAOgB,CAAC;AACV;AAEA,SAASZ,UAAUA,CAACU,GAAG,EAAEG,CAAC,EAAE;EAC1B,IAAIiB,CAAC,GAAG,CAACpB,GAAG,CAACC,MAAM,GAAGE,CAAC,IAAI,CAAC;EAC5B,IAAIiB,CAAC,KAAK,CAAC,EAAE,OAAOpB,GAAG,CAACQ,QAAQ,CAAC,QAAQ,EAAEL,CAAC,CAAC;EAC7C,IAAI,CAACR,QAAQ,GAAG,CAAC,GAAGyB,CAAC;EACrB,IAAI,CAACxB,SAAS,GAAG,CAAC;EAClB,IAAIwB,CAAC,KAAK,CAAC,EAAE;IACX,IAAI,CAACvB,QAAQ,CAAC,CAAC,CAAC,GAAGG,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;EACxC,CAAC,MAAM;IACL,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC,GAAGG,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;IACtC,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC,GAAGG,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;EACxC;EACA,OAAOD,GAAG,CAACQ,QAAQ,CAAC,QAAQ,EAAEL,CAAC,EAAEH,GAAG,CAACC,MAAM,GAAGmB,CAAC,CAAC;AAClD;AAEA,SAAS7B,SAASA,CAACS,GAAG,EAAE;EACtB,IAAIE,CAAC,GAAGF,GAAG,IAAIA,GAAG,CAACC,MAAM,GAAG,IAAI,CAACT,KAAK,CAACQ,GAAG,CAAC,GAAG,EAAE;EAChD,IAAI,IAAI,CAACL,QAAQ,EAAE,OAAOO,CAAC,GAAG,IAAI,CAACL,QAAQ,CAACW,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAACb,QAAQ,CAAC;EACpF,OAAOO,CAAC;AACV;;AAEA;AACA,SAAST,WAAWA,CAACO,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACQ,QAAQ,CAAC,IAAI,CAACnC,QAAQ,CAAC;AACpC;AAEA,SAASqB,SAASA,CAACM,GAAG,EAAE;EACtB,OAAOA,GAAG,IAAIA,GAAG,CAACC,MAAM,GAAG,IAAI,CAACT,KAAK,CAACQ,GAAG,CAAC,GAAG,EAAE;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}