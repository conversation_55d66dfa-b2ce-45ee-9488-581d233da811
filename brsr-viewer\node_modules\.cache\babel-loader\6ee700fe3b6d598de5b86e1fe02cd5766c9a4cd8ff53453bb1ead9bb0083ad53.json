{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLDOMStringList;\n  module.exports = XMLDOMStringList = function () {\n    function XMLDOMStringList(arr) {\n      this.arr = arr || [];\n    }\n    Object.defineProperty(XMLDOMStringList.prototype, 'length', {\n      get: function () {\n        return this.arr.length;\n      }\n    });\n    XMLDOMStringList.prototype.item = function (index) {\n      return this.arr[index] || null;\n    };\n    XMLDOMStringList.prototype.contains = function (str) {\n      return this.arr.indexOf(str) !== -1;\n    };\n    return XMLDOMStringList;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["XMLDOMStringList", "module", "exports", "arr", "Object", "defineProperty", "prototype", "get", "length", "item", "index", "contains", "str", "indexOf", "call"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/XMLDOMStringList.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMStringList;\n\n  module.exports = XMLDOMStringList = (function() {\n    function XMLDOMStringList(arr) {\n      this.arr = arr || [];\n    }\n\n    Object.defineProperty(XMLDOMStringList.prototype, 'length', {\n      get: function() {\n        return this.arr.length;\n      }\n    });\n\n    XMLDOMStringList.prototype.item = function(index) {\n      return this.arr[index] || null;\n    };\n\n    XMLDOMStringList.prototype.contains = function(str) {\n      return this.arr.indexOf(str) !== -1;\n    };\n\n    return XMLDOMStringList;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,gBAAgB;EAEpBC,MAAM,CAACC,OAAO,GAAGF,gBAAgB,GAAI,YAAW;IAC9C,SAASA,gBAAgBA,CAACG,GAAG,EAAE;MAC7B,IAAI,CAACA,GAAG,GAAGA,GAAG,IAAI,EAAE;IACtB;IAEAC,MAAM,CAACC,cAAc,CAACL,gBAAgB,CAACM,SAAS,EAAE,QAAQ,EAAE;MAC1DC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACJ,GAAG,CAACK,MAAM;MACxB;IACF,CAAC,CAAC;IAEFR,gBAAgB,CAACM,SAAS,CAACG,IAAI,GAAG,UAASC,KAAK,EAAE;MAChD,OAAO,IAAI,CAACP,GAAG,CAACO,KAAK,CAAC,IAAI,IAAI;IAChC,CAAC;IAEDV,gBAAgB,CAACM,SAAS,CAACK,QAAQ,GAAG,UAASC,GAAG,EAAE;MAClD,OAAO,IAAI,CAACT,GAAG,CAACU,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,OAAOZ,gBAAgB;EAEzB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEc,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}