{"version": 3, "file": "styled-components.native.cjs.js", "sources": ["../../../src/utils/empties.ts", "../../../src/utils/error.ts", "../../../src/utils/errors.ts", "../../../src/utils/setToString.ts", "../../../src/constants.ts", "../../../src/sheet/GroupedTag.ts", "../../../src/sheet/GroupIDAllocator.ts", "../../../src/sheet/Rehydration.ts", "../../../src/sheet/dom.ts", "../../../src/utils/nonce.ts", "../../../src/sheet/Tag.ts", "../../../src/sheet/Sheet.ts", "../../../src/utils/hash.ts", "../../../src/utils/stylis.ts", "../../../src/models/StyleSheetManager.tsx", "../../../src/models/Keyframes.ts", "../../../src/utils/getComponentName.ts", "../../../src/utils/hyphenateStyleName.ts", "../../../src/utils/isFunction.ts", "../../../src/utils/isPlainObject.ts", "../../../src/utils/isStyledComponent.ts", "../../../src/utils/flatten.ts", "../../../src/utils/addUnitIfNeeded.ts", "../../../src/utils/isStatelessFunction.ts", "../../../src/utils/interleave.ts", "../../../src/constructors/css.ts", "../../../src/constructors/constructWithOptions.ts", "../../../src/models/ThemeProvider.tsx", "../../../src/utils/determineTheme.ts", "../../../src/utils/hoist.ts", "../../../src/utils/generateAlphabeticName.ts", "../../../src/utils/joinStrings.ts", "../../../src/models/InlineStyle.ts", "../../../src/utils/mixinDeep.ts", "../../../src/models/StyledNativeComponent.ts", "../../../src/native/index.ts", "../../../src/utils/generateComponentId.ts", "../../../src/utils/generateDisplayName.ts", "../../../src/utils/isTag.ts", "../../../src/hoc/withTheme.tsx"], "sourcesContent": ["import { Dict } from '../types';\n\nexport const EMPTY_ARRAY = Object.freeze([]) as Readonly<any[]>;\nexport const EMPTY_OBJECT = Object.freeze({}) as Readonly<Dict<any>>;\n", "import { Dict } from '../types';\nimport errorMap from './errors';\n\nconst ERRORS: Dict<any> = process.env.NODE_ENV !== 'production' ? errorMap : {};\n\n/**\n * super basic version of sprintf\n */\nfunction format(...args: [string, ...any]) {\n  let a = args[0];\n  const b = [];\n\n  for (let c = 1, len = args.length; c < len; c += 1) {\n    b.push(args[c]);\n  }\n\n  b.forEach(d => {\n    a = a.replace(/%[a-z]/, d);\n  });\n\n  return a;\n}\n\n/**\n * Create an error file out of errors.md for development and a simple web link to the full errors\n * in production mode.\n */\nexport default function throwStyledComponentsError(\n  code: string | number,\n  ...interpolations: any[]\n) {\n  if (process.env.NODE_ENV === 'production') {\n    return new Error(\n      `An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#${code} for more information.${\n        interpolations.length > 0 ? ` Args: ${interpolations.join(', ')}` : ''\n      }`\n    );\n  } else {\n    return new Error(format(ERRORS[code], ...interpolations).trim());\n  }\n}\n", "export default {\n  '1': 'Cannot create styled-component for component: %s.\\n\\n',\n  '2': \"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\\n\\n- Are you trying to reuse it across renders?\\n- Are you accidentally calling collectStyles twice?\\n\\n\",\n  '3': 'Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\\n\\n',\n  '4': 'The `StyleSheetManager` expects a valid target or sheet prop!\\n\\n- Does this error occur on the client and is your target falsy?\\n- Does this error occur on the server and is the sheet falsy?\\n\\n',\n  '5': 'The clone method cannot be used on the client!\\n\\n- Are you running in a client-like environment on the server?\\n- Are you trying to run SSR on the client?\\n\\n',\n  '6': \"Trying to insert a new style tag, but the given Node is unmounted!\\n\\n- Are you using a custom target that isn't mounted?\\n- Does your document not have a valid head element?\\n- Have you accidentally removed a style tag manually?\\n\\n\",\n  '7': 'ThemeProvider: Please return an object from your \"theme\" prop function, e.g.\\n\\n```js\\ntheme={() => ({})}\\n```\\n\\n',\n  '8': 'ThemeProvider: Please make your \"theme\" prop an object.\\n\\n',\n  '9': 'Missing document `<head>`\\n\\n',\n  '10': 'Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\\n\\n',\n  '11': '_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\\n\\n',\n  '12': 'It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\\\`\\\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\\n\\n',\n  '13': '%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\\n\\n',\n  '14': 'ThemeProvider: \"theme\" prop is required.\\n\\n',\n  '15': \"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\\n\\n```js\\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\\n```\\n\\n\",\n  '16': \"Reached the limit of how many styled components may be created at group %s.\\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\\nas for instance in your render method then you may be running into this limitation.\\n\\n\",\n  '17': \"CSSStyleSheet could not be found on HTMLStyleElement.\\nHas styled-components' style tag been unmounted or altered by another script?\\n\",\n  '18': 'ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`',\n};\n", "/**\n * If the Object prototype is frozen, the \"toString\" property is non-writable. This means that any objects which inherit this property\n * cannot have the property changed using a \"=\" assignment operator. If using strict mode, attempting that will cause an error. If not using\n * strict mode, attempting that will be silently ignored.\n *\n * If the Object prototype is frozen, inherited non-writable properties can still be shadowed using one of two mechanisms:\n *\n *  1. ES6 class methods: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes#methods\n *  2. Using the `Object.defineProperty()` static method:\n *     https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty\n *\n * However, this project uses Babel to transpile ES6 classes, and transforms ES6 class methods to use the assignment operator instead:\n * https://babeljs.io/docs/babel-plugin-transform-class-properties#options\n *\n * Therefore, the most compatible way to shadow the prototype's \"toString\" property is to define a new \"toString\" property on this object.\n */\nexport function setToString(object: object, toStringFn: () => string) {\n  Object.defineProperty(object, 'toString', { value: toStringFn });\n}\n", "declare let SC_DISABLE_SPEEDY: boolean | null | undefined;\ndeclare let __VERSION__: string;\n\nexport const SC_ATTR: string =\n  (typeof process !== 'undefined' &&\n    typeof process.env !== 'undefined' &&\n    (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR)) ||\n  'data-styled';\n\nexport const SC_ATTR_ACTIVE = 'active';\nexport const SC_ATTR_VERSION = 'data-styled-version';\nexport const SC_VERSION = __VERSION__;\nexport const SPLITTER = '/*!sc*/\\n';\n\nexport const IS_BROWSER = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const DISABLE_SPEEDY = Boolean(\n  typeof SC_DISABLE_SPEEDY === 'boolean'\n    ? SC_DISABLE_SPEEDY\n    : typeof process !== 'undefined' &&\n        typeof process.env !== 'undefined' &&\n        typeof process.env.REACT_APP_SC_DISABLE_SPEEDY !== 'undefined' &&\n        process.env.REACT_APP_SC_DISABLE_SPEEDY !== ''\n      ? process.env.REACT_APP_SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.REACT_APP_SC_DISABLE_SPEEDY\n      : typeof process !== 'undefined' &&\n          typeof process.env !== 'undefined' &&\n          typeof process.env.SC_DISABLE_SPEEDY !== 'undefined' &&\n          process.env.SC_DISABLE_SPEEDY !== ''\n        ? process.env.SC_DISABLE_SPEEDY === 'false'\n          ? false\n          : process.env.SC_DISABLE_SPEEDY\n        : process.env.NODE_ENV !== 'production'\n);\n\n// Shared empty execution context when generating static styles\nexport const STATIC_EXECUTION_CONTEXT = {};\n", "import { SPLITTER } from '../constants';\nimport styledError from '../utils/error';\nimport { GroupedTag, Tag } from './types';\n\n/** Create a GroupedTag with an underlying Tag implementation */\nexport const makeGroupedTag = (tag: Tag) => {\n  return new DefaultGroupedTag(tag);\n};\n\nconst BASE_SIZE = 1 << 9;\n\nconst DefaultGroupedTag = class DefaultGroupedTag implements GroupedTag {\n  groupSizes: Uint32Array;\n  length: number;\n  tag: Tag;\n\n  constructor(tag: Tag) {\n    this.groupSizes = new Uint32Array(BASE_SIZE);\n    this.length = BASE_SIZE;\n    this.tag = tag;\n  }\n\n  indexOfGroup(group: number) {\n    let index = 0;\n    for (let i = 0; i < group; i++) {\n      index += this.groupSizes[i];\n    }\n\n    return index;\n  }\n\n  insertRules(group: number, rules: string[]) {\n    if (group >= this.groupSizes.length) {\n      const oldBuffer = this.groupSizes;\n      const oldSize = oldBuffer.length;\n\n      let newSize = oldSize;\n      while (group >= newSize) {\n        newSize <<= 1;\n        if (newSize < 0) {\n          throw styledError(16, `${group}`);\n        }\n      }\n\n      this.groupSizes = new Uint32Array(newSize);\n      this.groupSizes.set(oldBuffer);\n      this.length = newSize;\n\n      for (let i = oldSize; i < newSize; i++) {\n        this.groupSizes[i] = 0;\n      }\n    }\n\n    let ruleIndex = this.indexOfGroup(group + 1);\n\n    for (let i = 0, l = rules.length; i < l; i++) {\n      if (this.tag.insertRule(ruleIndex, rules[i])) {\n        this.groupSizes[group]++;\n        ruleIndex++;\n      }\n    }\n  }\n\n  clearGroup(group: number) {\n    if (group < this.length) {\n      const length = this.groupSizes[group];\n      const startIndex = this.indexOfGroup(group);\n      const endIndex = startIndex + length;\n\n      this.groupSizes[group] = 0;\n\n      for (let i = startIndex; i < endIndex; i++) {\n        this.tag.deleteRule(startIndex);\n      }\n    }\n  }\n\n  getGroup(group: number) {\n    let css = '';\n    if (group >= this.length || this.groupSizes[group] === 0) {\n      return css;\n    }\n\n    const length = this.groupSizes[group];\n    const startIndex = this.indexOfGroup(group);\n    const endIndex = startIndex + length;\n\n    for (let i = startIndex; i < endIndex; i++) {\n      css += `${this.tag.getRule(i)}${SPLITTER}`;\n    }\n\n    return css;\n  }\n};\n", "import styledError from '../utils/error';\n\nconst MAX_SMI = 1 << (31 - 1);\n\nlet groupIDRegister: Map<string, number> = new Map();\nlet reverseRegister: Map<number, string> = new Map();\nlet nextFreeGroup = 1;\n\nexport const resetGroupIds = () => {\n  groupIDRegister = new Map();\n  reverseRegister = new Map();\n  nextFreeGroup = 1;\n};\n\nexport const getGroupForId = (id: string): number => {\n  if (groupIDRegister.has(id)) {\n    return groupIDRegister.get(id) as any;\n  }\n\n  while (reverseRegister.has(nextFreeGroup)) {\n    nextFreeGroup++;\n  }\n\n  const group = nextFreeGroup++;\n\n  if (process.env.NODE_ENV !== 'production' && ((group | 0) < 0 || group > MAX_SMI)) {\n    throw styledError(16, `${group}`);\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n  return group;\n};\n\nexport const getIdForGroup = (group: number): void | string => {\n  return reverseRegister.get(group);\n};\n\nexport const setGroupForId = (id: string, group: number) => {\n  // move pointer\n  nextFreeGroup = group + 1;\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n};\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION, SPLITTER } from '../constants';\nimport { getIdForGroup, setGroupForId } from './GroupIDAllocator';\nimport { Sheet } from './types';\n\nconst SELECTOR = `style[${SC_ATTR}][${SC_ATTR_VERSION}=\"${SC_VERSION}\"]`;\nconst MARKER_RE = new RegExp(`^${SC_ATTR}\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)`);\n\nexport const outputSheet = (sheet: Sheet) => {\n  const tag = sheet.getTag();\n  const { length } = tag;\n\n  let css = '';\n  for (let group = 0; group < length; group++) {\n    const id = getIdForGroup(group);\n    if (id === undefined) continue;\n\n    const names = sheet.names.get(id);\n    const rules = tag.getGroup(group);\n    if (names === undefined || !names.size || rules.length === 0) continue;\n\n    const selector = `${SC_ATTR}.g${group}[id=\"${id}\"]`;\n\n    let content = '';\n    if (names !== undefined) {\n      names.forEach(name => {\n        if (name.length > 0) {\n          content += `${name},`;\n        }\n      });\n    }\n\n    // NOTE: It's easier to collect rules and have the marker\n    // after the actual rules to simplify the rehydration\n    css += `${rules}${selector}{content:\"${content}\"}${SPLITTER}`;\n  }\n\n  return css;\n};\n\nconst rehydrateNamesFromContent = (sheet: Sheet, id: string, content: string) => {\n  const names = content.split(',');\n  let name;\n\n  for (let i = 0, l = names.length; i < l; i++) {\n    if ((name = names[i])) {\n      sheet.registerName(id, name);\n    }\n  }\n};\n\nconst rehydrateSheetFromTag = (sheet: Sheet, style: HTMLStyleElement) => {\n  const parts = (style.textContent ?? '').split(SPLITTER);\n  const rules: string[] = [];\n\n  for (let i = 0, l = parts.length; i < l; i++) {\n    const part = parts[i].trim();\n    if (!part) continue;\n\n    const marker = part.match(MARKER_RE);\n\n    if (marker) {\n      const group = parseInt(marker[1], 10) | 0;\n      const id = marker[2];\n\n      if (group !== 0) {\n        // Rehydrate componentId to group index mapping\n        setGroupForId(id, group);\n        // Rehydrate names and rules\n        // looks like: data-styled.g11[id=\"idA\"]{content:\"nameA,\"}\n        rehydrateNamesFromContent(sheet, id, marker[3]);\n        sheet.getTag().insertRules(group, rules);\n      }\n\n      rules.length = 0;\n    } else {\n      rules.push(part);\n    }\n  }\n};\n\nexport const rehydrateSheet = (sheet: Sheet) => {\n  const nodes = document.querySelectorAll(SELECTOR);\n\n  for (let i = 0, l = nodes.length; i < l; i++) {\n    const node = nodes[i] as any as HTMLStyleElement;\n    if (node && node.getAttribute(SC_ATTR) !== SC_ATTR_ACTIVE) {\n      rehydrateSheetFromTag(sheet, node);\n\n      if (node.parentNode) {\n        node.parentNode.removeChild(node);\n      }\n    }\n  }\n};\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport { InsertionTarget } from '../types';\nimport styledError from '../utils/error';\nimport getNonce from '../utils/nonce';\n\n/** Find last style element if any inside target */\nconst findLastStyleTag = (target: InsertionTarget): void | HTMLStyleElement => {\n  const arr = Array.from(target.querySelectorAll<HTMLStyleElement>(`style[${SC_ATTR}]`));\n\n  return arr[arr.length - 1];\n};\n\n/** Create a style element inside `target` or <head> after the last */\nexport const makeStyleTag = (target?: InsertionTarget | undefined): HTMLStyleElement => {\n  const head = document.head;\n  const parent = target || head;\n  const style = document.createElement('style');\n  const prevStyle = findLastStyleTag(parent);\n  const nextSibling = prevStyle !== undefined ? prevStyle.nextSibling : null;\n\n  style.setAttribute(SC_ATTR, SC_ATTR_ACTIVE);\n  style.setAttribute(SC_ATTR_VERSION, SC_VERSION);\n\n  const nonce = getNonce();\n\n  if (nonce) style.setAttribute('nonce', nonce);\n\n  parent.insertBefore(style, nextSibling);\n\n  return style;\n};\n\n/** Get the CSSStyleSheet instance for a given style element */\nexport const getSheet = (tag: HTMLStyleElement): CSSStyleSheet => {\n  if (tag.sheet) {\n    return tag.sheet as any as CSSStyleSheet;\n  }\n\n  // Avoid Firefox quirk where the style element might not have a sheet property\n  const { styleSheets } = document;\n  for (let i = 0, l = styleSheets.length; i < l; i++) {\n    const sheet = styleSheets[i];\n    if (sheet.ownerNode === tag) {\n      return sheet as any as CSSStyleSheet;\n    }\n  }\n\n  throw styledError(17);\n};\n", "declare let __webpack_nonce__: string;\n\nexport default function getNonce() {\n  return typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null;\n}\n", "import { InsertionTarget } from '../types';\nimport { getSheet, makeStyleTag } from './dom';\nimport { SheetOptions, Tag } from './types';\n\n/** Create a CSSStyleSheet-like tag depending on the environment */\nexport const makeTag = ({ isServer, useCSSOMInjection, target }: SheetOptions) => {\n  if (isServer) {\n    return new VirtualTag(target);\n  } else if (useCSSOMInjection) {\n    return new CSSOMTag(target);\n  } else {\n    return new TextTag(target);\n  }\n};\n\nexport const CSSOMTag = class CSSOMTag implements Tag {\n  element: HTMLStyleElement;\n\n  sheet: CSSStyleSheet;\n\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n\n    // Avoid Edge bug where empty style elements don't create sheets\n    this.element.appendChild(document.createTextNode(''));\n\n    this.sheet = getSheet(this.element);\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    try {\n      this.sheet.insertRule(rule, index);\n      this.length++;\n      return true;\n    } catch (_error) {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.sheet.deleteRule(index);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    const rule = this.sheet.cssRules[index];\n\n    // Avoid IE11 quirk where cssText is inaccessible on some invalid rules\n    if (rule && rule.cssText) {\n      return rule.cssText;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A Tag that emulates the CSSStyleSheet API but uses text nodes */\nexport const TextTag = class TextTag implements Tag {\n  element: HTMLStyleElement;\n  nodes: NodeListOf<Node>;\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n    this.nodes = this.element.childNodes;\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length && index >= 0) {\n      const node = document.createTextNode(rule);\n      const refNode = this.nodes[index];\n      this.element.insertBefore(node, refNode || null);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.element.removeChild(this.nodes[index]);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.nodes[index].textContent as string;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A completely virtual (server-side) Tag that doesn't manipulate the DOM */\nexport const VirtualTag = class VirtualTag implements Tag {\n  rules: string[];\n\n  length: number;\n\n  constructor(_target?: InsertionTarget | undefined) {\n    this.rules = [];\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length) {\n      this.rules.splice(index, 0, rule);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.rules.splice(index, 1);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.rules[index];\n    } else {\n      return '';\n    }\n  }\n};\n", "import { DISABLE_SPEEDY, IS_BROWSER } from '../constants';\nimport { InsertionTarget } from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport { setToString } from '../utils/setToString';\nimport { makeGroupedTag } from './GroupedTag';\nimport { getGroupForId } from './GroupIDAllocator';\nimport { outputSheet, rehydrateSheet } from './Rehydration';\nimport { makeTag } from './Tag';\nimport { GroupedTag, Sheet, SheetOptions } from './types';\n\nlet SHOULD_REHYDRATE = IS_BROWSER;\n\ntype SheetConstructorArgs = {\n  isServer?: boolean;\n  useCSSOMInjection?: boolean;\n  target?: InsertionTarget | undefined;\n};\n\ntype GlobalStylesAllocationMap = {\n  [key: string]: number;\n};\ntype NamesAllocationMap = Map<string, Set<string>>;\n\nconst defaultOptions: SheetOptions = {\n  isServer: !IS_BROWSER,\n  useCSSOMInjection: !DISABLE_SPEEDY,\n};\n\n/** Contains the main stylesheet logic for stringification and caching */\nexport default class StyleSheet implements Sheet {\n  gs: GlobalStylesAllocationMap;\n  names: NamesAllocationMap;\n  options: SheetOptions;\n  server: boolean;\n  tag?: GroupedTag | undefined;\n\n  /** Register a group ID to give it an index */\n  static registerId(id: string): number {\n    return getGroupForId(id);\n  }\n\n  constructor(\n    options: SheetConstructorArgs = EMPTY_OBJECT as Object,\n    globalStyles: GlobalStylesAllocationMap = {},\n    names?: NamesAllocationMap | undefined\n  ) {\n    this.options = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    this.gs = globalStyles;\n    this.names = new Map(names as NamesAllocationMap);\n    this.server = !!options.isServer;\n\n    // We rehydrate only once and use the sheet that is created first\n    if (!this.server && IS_BROWSER && SHOULD_REHYDRATE) {\n      SHOULD_REHYDRATE = false;\n      rehydrateSheet(this);\n    }\n\n    setToString(this, () => outputSheet(this));\n  }\n\n  rehydrate(): void {\n    if (!this.server && IS_BROWSER) {\n      rehydrateSheet(this);\n    }\n  }\n\n  reconstructWithOptions(options: SheetConstructorArgs, withNames = true) {\n    return new StyleSheet(\n      { ...this.options, ...options },\n      this.gs,\n      (withNames && this.names) || undefined\n    );\n  }\n\n  allocateGSInstance(id: string) {\n    return (this.gs[id] = (this.gs[id] || 0) + 1);\n  }\n\n  /** Lazily initialises a GroupedTag for when it's actually needed */\n  getTag() {\n    return this.tag || (this.tag = makeGroupedTag(makeTag(this.options)));\n  }\n\n  /** Check whether a name is known for caching */\n  hasNameForId(id: string, name: string): boolean {\n    return this.names.has(id) && (this.names.get(id) as any).has(name);\n  }\n\n  /** Mark a group's name as known for caching */\n  registerName(id: string, name: string) {\n    getGroupForId(id);\n\n    if (!this.names.has(id)) {\n      const groupNames = new Set<string>();\n      groupNames.add(name);\n      this.names.set(id, groupNames);\n    } else {\n      (this.names.get(id) as any).add(name);\n    }\n  }\n\n  /** Insert new rules which also marks the name as known */\n  insertRules(id: string, name: string, rules: string | string[]) {\n    this.registerName(id, name);\n    this.getTag().insertRules(getGroupForId(id), rules);\n  }\n\n  /** Clears all cached names for a given group ID */\n  clearNames(id: string) {\n    if (this.names.has(id)) {\n      (this.names.get(id) as any).clear();\n    }\n  }\n\n  /** Clears all rules for a given group ID */\n  clearRules(id: string) {\n    this.getTag().clearGroup(getGroupForId(id));\n    this.clearNames(id);\n  }\n\n  /** Clears the entire tag which deletes all rules but not its names */\n  clearTag() {\n    // NOTE: This does not clear the names, since it's only used during SSR\n    // so that we can continuously output only new rules\n    this.tag = undefined;\n  }\n}\n", "export const SEED = 5381;\n\n// When we have separate strings it's useful to run a progressive\n// version of djb2 where we pretend that we're still looping over\n// the same string\nexport const phash = (h: number, x: string) => {\n  let i = x.length;\n\n  while (i) {\n    h = (h * 33) ^ x.charCodeAt(--i);\n  }\n\n  return h;\n};\n\n// This is a djb2 hashing function\nexport const hash = (x: string) => {\n  return phash(SEED, x);\n};\n", "import * as stylis from 'stylis';\nimport { Stringifier } from '../types';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from './empties';\nimport throwStyledError from './error';\nimport { SEED, phash } from './hash';\n\nconst AMP_REGEX = /&/g;\nconst COMMENT_REGEX = /^\\s*\\/\\/.*$/gm;\n\nexport type ICreateStylisInstance = {\n  options?: { namespace?: string | undefined; prefix?: boolean | undefined } | undefined;\n  plugins?: stylis.Middleware[] | undefined;\n};\n\n/**\n * Takes an element and recurses through it's rules added the namespace to the start of each selector.\n * Takes into account media queries by recursing through child rules if they are present.\n */\nfunction recursivelySetNamepace(compiled: stylis.Element[], namespace: String): stylis.Element[] {\n  return compiled.map(rule => {\n    if (rule.type === 'rule') {\n      // add the namespace to the start\n      rule.value = `${namespace} ${rule.value}`;\n      // add the namespace after each comma for subsequent selectors.\n      rule.value = rule.value.replaceAll(',', `,${namespace} `);\n      rule.props = (rule.props as string[]).map(prop => {\n        return `${namespace} ${prop}`;\n      });\n    }\n\n    if (Array.isArray(rule.children) && rule.type !== '@keyframes') {\n      rule.children = recursivelySetNamepace(rule.children, namespace);\n    }\n    return rule;\n  });\n}\n\nexport default function createStylisInstance(\n  {\n    options = EMPTY_OBJECT as object,\n    plugins = EMPTY_ARRAY as unknown as stylis.Middleware[],\n  }: ICreateStylisInstance = EMPTY_OBJECT as object\n) {\n  let _componentId: string;\n  let _selector: string;\n  let _selectorRegexp: RegExp;\n\n  const selfReferenceReplacer = (match: string, offset: number, string: string) => {\n    if (\n      /**\n       * We only want to refer to the static class directly if the selector is part of a\n       * self-reference selector `& + & { color: red; }`\n       */\n      string.startsWith(_selector) &&\n      string.endsWith(_selector) &&\n      string.replaceAll(_selector, '').length > 0\n    ) {\n      return `.${_componentId}`;\n    }\n\n    return match;\n  };\n\n  /**\n   * When writing a style like\n   *\n   * & + & {\n   *   color: red;\n   * }\n   *\n   * The second ampersand should be a reference to the static component class. stylis\n   * has no knowledge of static class so we have to intelligently replace the base selector.\n   *\n   * https://github.com/thysultan/stylis.js/tree/v4.0.2#abstract-syntax-structure\n   */\n  const selfReferenceReplacementPlugin: stylis.Middleware = element => {\n    if (element.type === stylis.RULESET && element.value.includes('&')) {\n      (element.props as string[])[0] = element.props[0]\n        // catch any hanging references that stylis missed\n        .replace(AMP_REGEX, _selector)\n        .replace(_selectorRegexp, selfReferenceReplacer);\n    }\n  };\n\n  const middlewares = plugins.slice();\n\n  middlewares.push(selfReferenceReplacementPlugin);\n\n  /**\n   * Enables automatic vendor-prefixing for styles.\n   */\n  if (options.prefix) {\n    middlewares.push(stylis.prefixer);\n  }\n\n  middlewares.push(stylis.stringify);\n\n  const stringifyRules: Stringifier = (\n    css: string,\n    selector = '',\n    /**\n     * This \"prefix\" referes to a _selector_ prefix.\n     */\n    prefix = '',\n    componentId = '&'\n  ) => {\n    // stylis has no concept of state to be passed to plugins\n    // but since JS is single-threaded, we can rely on that to ensure\n    // these properties stay in sync with the current stylis run\n    _componentId = componentId;\n    _selector = selector;\n    _selectorRegexp = new RegExp(`\\\\${_selector}\\\\b`, 'g');\n\n    const flatCSS = css.replace(COMMENT_REGEX, '');\n    let compiled = stylis.compile(\n      prefix || selector ? `${prefix} ${selector} { ${flatCSS} }` : flatCSS\n    );\n\n    if (options.namespace) {\n      compiled = recursivelySetNamepace(compiled, options.namespace);\n    }\n\n    const stack: string[] = [];\n\n    stylis.serialize(\n      compiled,\n      stylis.middleware(middlewares.concat(stylis.rulesheet(value => stack.push(value))))\n    );\n\n    return stack;\n  };\n\n  stringifyRules.hash = plugins.length\n    ? plugins\n        .reduce((acc, plugin) => {\n          if (!plugin.name) {\n            throwStyledError(15);\n          }\n\n          return phash(acc, plugin.name);\n        }, SEED)\n        .toString()\n    : '';\n\n  return stringifyRules;\n}\n", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport shallowequal from 'shallowequal';\nimport type stylis from 'stylis';\nimport StyleSheet from '../sheet';\nimport { InsertionTarget, ShouldForwardProp, Stringifier } from '../types';\nimport createStylisInstance from '../utils/stylis';\n\nexport const mainSheet: StyleSheet = new StyleSheet();\nexport const mainStylis: Stringifier = createStylisInstance();\n\nexport type IStyleSheetContext = {\n  shouldForwardProp?: ShouldForwardProp<'web'> | undefined;\n  styleSheet: StyleSheet;\n  stylis: Stringifier;\n};\n\nexport const StyleSheetContext = React.createContext<IStyleSheetContext>({\n  shouldForwardProp: undefined,\n  styleSheet: mainSheet,\n  stylis: mainStylis,\n});\n\nexport const StyleSheetConsumer = StyleSheetContext.Consumer;\n\nexport type IStylisContext = Stringifier | void;\nexport const StylisContext = React.createContext<IStylisContext>(undefined);\nexport const StylisConsumer = StylisContext.Consumer;\n\nexport function useStyleSheetContext() {\n  return useContext(StyleSheetContext);\n}\n\nexport type IStyleSheetManager = React.PropsWithChildren<{\n  /**\n   * If desired, you can pass this prop to disable \"speedy\" insertion mode, which\n   * uses the browser [CSSOM APIs](https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet).\n   * When disabled, rules are inserted as simple text into style blocks.\n   */\n  disableCSSOMInjection?: undefined | boolean;\n  /**\n   * If you are working exclusively with modern browsers, vendor prefixes can often be omitted\n   * to reduce the weight of CSS on the page.\n   */\n  enableVendorPrefixes?: undefined | boolean;\n  /**\n   * Provide an optional selector to be prepended to all generated style rules.\n   */\n  namespace?: undefined | string;\n  /**\n   * Create and provide your own `StyleSheet` if necessary for advanced SSR scenarios.\n   */\n  sheet?: undefined | StyleSheet;\n  /**\n   * Starting in v6, styled-components no longer does its own prop validation\n   * and recommends use of transient props \"$prop\" to pass style-only props to\n   * components. If for some reason you are not able to use transient props, a\n   * prop validation function can be provided via `StyleSheetManager`, such as\n   * `@emotion/is-prop-valid`.\n   *\n   * When the return value is `true`, props will be forwarded to the DOM/underlying\n   * component. If return value is `false`, the prop will be discarded after styles\n   * are calculated.\n   *\n   * Manually composing `styled.{element}.withConfig({shouldForwardProp})` will\n   * override this default.\n   */\n  shouldForwardProp?: undefined | IStyleSheetContext['shouldForwardProp'];\n  /**\n   * An array of plugins to be run by stylis (style processor) during compilation.\n   * Check out [what's available on npm*](https://www.npmjs.com/search?q=keywords%3Astylis).\n   *\n   * \\* The plugin(s) must be compatible with stylis v4 or above.\n   */\n  stylisPlugins?: undefined | stylis.Middleware[];\n  /**\n   * Provide an alternate DOM node to host generated styles; useful for iframes.\n   */\n  target?: undefined | InsertionTarget;\n}>;\n\nexport function StyleSheetManager(props: IStyleSheetManager): React.JSX.Element {\n  const [plugins, setPlugins] = useState(props.stylisPlugins);\n  const { styleSheet } = useStyleSheetContext();\n\n  const resolvedStyleSheet = useMemo(() => {\n    let sheet = styleSheet;\n\n    if (props.sheet) {\n      sheet = props.sheet;\n    } else if (props.target) {\n      sheet = sheet.reconstructWithOptions({ target: props.target }, false);\n    }\n\n    if (props.disableCSSOMInjection) {\n      sheet = sheet.reconstructWithOptions({ useCSSOMInjection: false });\n    }\n\n    return sheet;\n  }, [props.disableCSSOMInjection, props.sheet, props.target, styleSheet]);\n\n  const stylis = useMemo(\n    () =>\n      createStylisInstance({\n        options: { namespace: props.namespace, prefix: props.enableVendorPrefixes },\n        plugins,\n      }),\n    [props.enableVendorPrefixes, props.namespace, plugins]\n  );\n\n  useEffect(() => {\n    if (!shallowequal(plugins, props.stylisPlugins)) setPlugins(props.stylisPlugins);\n  }, [props.stylisPlugins]);\n\n  const styleSheetContextValue = useMemo(\n    () => ({\n      shouldForwardProp: props.shouldForwardProp,\n      styleSheet: resolvedStyleSheet,\n      stylis,\n    }),\n    [props.shouldForwardProp, resolvedStyleSheet, stylis]\n  );\n\n  return (\n    <StyleSheetContext.Provider value={styleSheetContextValue}>\n      <StylisContext.Provider value={stylis}>{props.children}</StylisContext.Provider>\n    </StyleSheetContext.Provider>\n  );\n}\n", "import StyleSheet from '../sheet';\nimport { Keyframes as KeyframesType, Stringifier } from '../types';\nimport styledError from '../utils/error';\nimport { setToString } from '../utils/setToString';\nimport { mainStylis } from './StyleSheetManager';\n\nexport default class Keyframes implements KeyframesType {\n  id: string;\n  name: string;\n  rules: string;\n\n  constructor(name: string, rules: string) {\n    this.name = name;\n    this.id = `sc-keyframes-${name}`;\n    this.rules = rules;\n\n    setToString(this, () => {\n      throw styledError(12, String(this.name));\n    });\n  }\n\n  inject = (styleSheet: StyleSheet, stylisInstance: Stringifier = mainStylis): void => {\n    const resolvedName = this.name + stylisInstance.hash;\n\n    if (!styleSheet.hasNameForId(this.id, resolvedName)) {\n      styleSheet.insertRules(\n        this.id,\n        resolvedName,\n        stylisInstance(this.rules, resolvedName, '@keyframes')\n      );\n    }\n  };\n\n  getName(stylisInstance: Stringifier = mainStylis): string {\n    return this.name + stylisInstance.hash;\n  }\n}\n", "import { StyledTarget } from '../types';\n\nexport default function getComponentName(target: StyledTarget<any>) {\n  return (\n    (process.env.NODE_ENV !== 'production' ? typeof target === 'string' && target : false) ||\n    (target as Exclude<StyledTarget<any>, string>).displayName ||\n    (target as Function).name ||\n    'Component'\n  );\n}\n", "const isUpper = (c: string) => c >= 'A' && c <= 'Z';\n\n/**\n * Hyphenates a camelcased CSS property name, for example:\n *\n *   > hyphenateStyleName('backgroundColor')\n *   < \"background-color\"\n *   > hyphenateStyleName('MozTransition')\n *   < \"-moz-transition\"\n *   > hyphenateStyleName('msTransition')\n *   < \"-ms-transition\"\n *\n * As Modernizr suggests (http://modernizr.com/docs/#prefixed), an `ms` prefix\n * is converted to `-ms-`.\n */\nexport default function hyphenateStyleName(string: string): string {\n  let output = '';\n\n  for (let i = 0; i < string.length; i++) {\n    const c = string[i];\n    // Check for CSS variable prefix\n    if (i === 1 && c === '-' && string[0] === '-') {\n      return string;\n    }\n\n    if (isUpper(c)) {\n      output += '-' + c.toLowerCase();\n    } else {\n      output += c;\n    }\n  }\n\n  return output.startsWith('ms-') ? '-' + output : output;\n}\n", "export default function isFunction(test: any): test is Function {\n  return typeof test === 'function';\n}\n", "export default function isPlainObject(x: any): x is Record<any, any> {\n  return (\n    x !== null &&\n    typeof x === 'object' &&\n    x.constructor.name === Object.name &&\n    /* check for reasonable markers that the object isn't an element for react & preact/compat */\n    !('props' in x && x.$$typeof)\n  );\n}\n", "import { StyledComponentBrand } from '../types';\n\nexport default function isStyledComponent(target: any): target is StyledComponentBrand {\n  return typeof target === 'object' && 'styledComponentId' in target;\n}\n", "import Keyframes from '../models/Keyframes';\nimport StyleSheet from '../sheet';\nimport {\n  AnyComponent,\n  Dict,\n  ExecutionContext,\n  Interpolation,\n  IStyledComponent,\n  RuleSet,\n  Stringifier,\n  StyledObject,\n} from '../types';\nimport addUnitIfNeeded from './addUnitIfNeeded';\nimport { EMPTY_ARRAY } from './empties';\nimport getComponentName from './getComponentName';\nimport hyphenate from './hyphenateStyleName';\nimport isFunction from './isFunction';\nimport isPlainObject from './isPlainObject';\nimport isStatelessFunction from './isStatelessFunction';\nimport isStyledComponent from './isStyledComponent';\n\n/**\n * It's falsish not falsy because 0 is allowed.\n */\nconst isFalsish = (chunk: any): chunk is undefined | null | false | '' =>\n  chunk === undefined || chunk === null || chunk === false || chunk === '';\n\nexport const objToCssArray = (obj: Dict<any>): string[] => {\n  const rules = [];\n\n  for (const key in obj) {\n    const val = obj[key];\n    if (!obj.hasOwnProperty(key) || isFalsish(val)) continue;\n\n    // @ts-expect-error Property 'isCss' does not exist on type 'any[]'\n    if ((Array.isArray(val) && val.isCss) || isFunction(val)) {\n      rules.push(`${hyphenate(key)}:`, val, ';');\n    } else if (isPlainObject(val)) {\n      rules.push(`${key} {`, ...objToCssArray(val), '}');\n    } else {\n      rules.push(`${hyphenate(key)}: ${addUnitIfNeeded(key, val)};`);\n    }\n  }\n\n  return rules;\n};\n\nexport default function flatten<Props extends object>(\n  chunk: Interpolation<object>,\n  executionContext?: (ExecutionContext & Props) | undefined,\n  styleSheet?: StyleSheet | undefined,\n  stylisInstance?: Stringifier | undefined\n): RuleSet<Props> {\n  if (isFalsish(chunk)) {\n    return [];\n  }\n\n  /* Handle other components */\n  if (isStyledComponent(chunk)) {\n    return [`.${(chunk as unknown as IStyledComponent<'web', any>).styledComponentId}`];\n  }\n\n  /* Either execute or defer the function */\n  if (isFunction(chunk)) {\n    if (isStatelessFunction(chunk) && executionContext) {\n      const result = chunk(executionContext);\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        typeof result === 'object' &&\n        !Array.isArray(result) &&\n        !(result instanceof Keyframes) &&\n        !isPlainObject(result) &&\n        result !== null\n      ) {\n        console.error(\n          `${getComponentName(\n            chunk as AnyComponent\n          )} is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.`\n        );\n      }\n\n      return flatten<Props>(result, executionContext, styleSheet, stylisInstance);\n    } else {\n      return [chunk as unknown as IStyledComponent<'web'>];\n    }\n  }\n\n  if (chunk instanceof Keyframes) {\n    if (styleSheet) {\n      chunk.inject(styleSheet, stylisInstance);\n      return [chunk.getName(stylisInstance)];\n    } else {\n      return [chunk];\n    }\n  }\n\n  /* Handle objects */\n  if (isPlainObject(chunk)) {\n    return objToCssArray(chunk as StyledObject<Props>);\n  }\n\n  if (!Array.isArray(chunk)) {\n    return [chunk.toString()];\n  }\n\n  return flatMap(chunk, chunklet =>\n    flatten<Props>(chunklet, executionContext, styleSheet, stylisInstance)\n  );\n}\n\nfunction flatMap<T, U>(array: T[], transform: (value: T, index: number, array: T[]) => U[]): U[] {\n  return Array.prototype.concat.apply(EMPTY_ARRAY, array.map(transform));\n}\n", "import unitless from '@emotion/unitless';\n\n// Taken from https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/shared/dangerousStyleValue.js\nexport default function addUnitIfNeeded(name: string, value: any) {\n  // https://github.com/amilajack/eslint-plugin-flowtype-errors/issues/133\n  if (value == null || typeof value === 'boolean' || value === '') {\n    return '';\n  }\n\n  if (typeof value === 'number' && value !== 0 && !(name in unitless) && !name.startsWith('--')) {\n    return `${value}px`; // Presumes implicit 'px' suffix for unitless numbers except for CSS variables\n  }\n\n  return String(value).trim();\n}\n", "import isFunction from './isFunction';\n\nexport default function isStatelessFunction(test: any): test is Function {\n  return isFunction(test) && !(test.prototype && test.prototype.isReactComponent);\n}\n", "import { Interpolation } from '../types';\n\nexport default function interleave<Props extends object>(\n  strings: readonly string[],\n  interpolations: Interpolation<Props>[]\n): Interpolation<Props>[] {\n  const result: Interpolation<Props>[] = [strings[0]];\n\n  for (let i = 0, len = interpolations.length; i < len; i += 1) {\n    result.push(interpolations[i], strings[i + 1]);\n  }\n\n  return result;\n}\n", "import {\n  BaseObject,\n  Interpolation,\n  NoInfer,\n  RuleSet,\n  StyledObject,\n  StyleFunction,\n  Styles,\n} from '../types';\nimport { EMPTY_ARRAY } from '../utils/empties';\nimport flatten from '../utils/flatten';\nimport interleave from '../utils/interleave';\nimport isFunction from '../utils/isFunction';\nimport isPlainObject from '../utils/isPlainObject';\n\n/**\n * Used when flattening object styles to determine if we should\n * expand an array of styles.\n */\nconst addTag = <T extends RuleSet<any>>(arg: T): T & { isCss: true } =>\n  Object.assign(arg, { isCss: true } as const);\n\nfunction css(styles: Styles<object>, ...interpolations: Interpolation<object>[]): RuleSet<object>;\nfunction css<Props extends object>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>>;\nfunction css<Props extends object = BaseObject>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>> {\n  if (isFunction(styles) || isPlainObject(styles)) {\n    const styleFunctionOrObject = styles as StyleFunction<Props> | StyledObject<Props>;\n\n    return addTag(\n      flatten<Props>(\n        interleave<Props>(EMPTY_ARRAY, [\n          styleFunctionOrObject,\n          ...interpolations,\n        ]) as Interpolation<object>\n      )\n    );\n  }\n\n  const styleStringArray = styles as TemplateStringsArray;\n\n  if (\n    interpolations.length === 0 &&\n    styleStringArray.length === 1 &&\n    typeof styleStringArray[0] === 'string'\n  ) {\n    return flatten<Props>(styleStringArray);\n  }\n\n  return addTag(\n    flatten<Props>(interleave<Props>(styleStringArray, interpolations) as Interpolation<object>)\n  );\n}\n\nexport default css;\n", "import {\n  Attrs,\n  BaseObject,\n  ExecutionProps,\n  Interpolation,\n  IStyledComponent,\n  IStyledComponentFactory,\n  KnownTarget,\n  NoInfer,\n  Runtime,\n  StyledOptions,\n  StyledTarget,\n  Styles,\n  Substitute,\n} from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport styledError from '../utils/error';\nimport css from './css';\n\ntype AttrsResult<T extends Attrs<any>> = T extends (...args: any) => infer P\n  ? P extends object\n    ? P\n    : never\n  : T extends object\n    ? T\n    : never;\n\n/**\n * Based on Attrs being a simple object or function that returns\n * a prop object, inspect the attrs result and attempt to extract\n * any \"as\" prop usage to modify the runtime target.\n */\ntype AttrsTarget<\n  R extends Runtime,\n  T extends Attrs<any>,\n  FallbackTarget extends StyledTarget<R>,\n  Result extends ExecutionProps = AttrsResult<T>,\n> = Result extends { as: infer RuntimeTarget }\n  ? RuntimeTarget extends KnownTarget\n    ? RuntimeTarget\n    : FallbackTarget\n  : FallbackTarget;\n\nexport interface Styled<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object,\n  OuterStatics extends object = BaseObject,\n> {\n  <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, NoInfer<Props>>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, NoInfer<Props>>>[]\n  ): IStyledComponent<R, Substitute<OuterProps, Props>> &\n    OuterStatics &\n    Statics &\n    (R extends 'web'\n      ? Target extends string\n        ? {}\n        : Omit<Target, keyof React.Component<any>>\n      : {});\n\n  attrs: <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) => Styled<\n    R,\n    PrivateResolvedTarget,\n    PrivateResolvedTarget extends KnownTarget\n      ? Substitute<\n          Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n          Props\n        >\n      : PrivateMergedProps,\n    OuterStatics\n  >;\n\n  withConfig: (config: StyledOptions<R, OuterProps>) => Styled<R, Target, OuterProps, OuterStatics>;\n}\n\nexport default function constructWithOptions<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object = Target extends KnownTarget\n    ? React.ComponentPropsWithRef<Target>\n    : BaseObject,\n  OuterStatics extends object = BaseObject,\n>(\n  componentConstructor: IStyledComponentFactory<R, StyledTarget<R>, object, any>,\n  tag: StyledTarget<R>,\n  options: StyledOptions<R, OuterProps> = EMPTY_OBJECT\n): Styled<R, Target, OuterProps, OuterStatics> {\n  /**\n   * We trust that the tag is a valid component as long as it isn't\n   * falsish. Typically the tag here is a string or function (i.e.\n   * class or pure function component), however a component may also be\n   * an object if it uses another utility, e.g. React.memo. React will\n   * output an appropriate warning however if the `tag` isn't valid.\n   */\n  if (!tag) {\n    throw styledError(1, tag);\n  }\n\n  /* This is callable directly as a template function */\n  const templateFunction = <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, Props>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, Props>>[]\n  ) =>\n    componentConstructor<Substitute<OuterProps, Props>, Statics>(\n      tag,\n      options as StyledOptions<R, Substitute<OuterProps, Props>>,\n      css<Substitute<OuterProps, Props>>(initialStyles, ...interpolations)\n    );\n\n  /**\n   * Attrs allows for accomplishing two goals:\n   *\n   * 1. Backfilling props at runtime more expressively than defaultProps\n   * 2. Amending the prop interface of a wrapped styled component\n   */\n  templateFunction.attrs = <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) =>\n    constructWithOptions<\n      R,\n      PrivateResolvedTarget,\n      PrivateResolvedTarget extends KnownTarget\n        ? Substitute<\n            Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n            Props\n          >\n        : PrivateMergedProps,\n      OuterStatics\n    >(componentConstructor, tag, {\n      ...options,\n      attrs: Array.prototype.concat(options.attrs, attrs).filter(Boolean),\n    });\n\n  /**\n   * If config methods are called, wrap up a new template function\n   * and merge options.\n   */\n  templateFunction.withConfig = (config: StyledOptions<R, OuterProps>) =>\n    constructWithOptions<R, Target, OuterProps, OuterStatics>(componentConstructor, tag, {\n      ...options,\n      ...config,\n    });\n\n  return templateFunction;\n}\n", "import React, { useContext, useMemo } from 'react';\nimport styledError from '../utils/error';\nimport isFunction from '../utils/isFunction';\n\n// Helper type for the `DefaultTheme` interface that enforces an object type & exclusively allows\n// for typed keys.\ntype DefaultThemeAsObject<T = object> = Record<keyof T, any>;\n\n/**\n * Override DefaultTheme to get accurate typings for your project.\n *\n * ```\n * // create styled-components.d.ts in your project source\n * // if it isn't being picked up, check tsconfig compilerOptions.types\n * import type { CSSProp } from \"styled-components\";\n * import Theme from './theme';\n *\n * type ThemeType = typeof Theme;\n *\n * declare module \"styled-components\" {\n *  export interface DefaultTheme extends ThemeType {}\n * }\n *\n * declare module \"react\" {\n *  interface DOMAttributes<T> {\n *    css?: CSSProp;\n *  }\n * }\n * ```\n */\nexport interface DefaultTheme extends DefaultThemeAsObject {}\n\ntype ThemeFn = (outerTheme?: DefaultTheme | undefined) => DefaultTheme;\ntype ThemeArgument = DefaultTheme | ThemeFn;\n\ntype Props = {\n  children?: React.ReactNode;\n  theme: ThemeArgument;\n};\n\nexport const ThemeContext = React.createContext<DefaultTheme | undefined>(undefined);\n\nexport const ThemeConsumer = ThemeContext.Consumer;\n\nfunction mergeTheme(theme: ThemeArgument, outerTheme?: DefaultTheme | undefined): DefaultTheme {\n  if (!theme) {\n    throw styledError(14);\n  }\n\n  if (isFunction(theme)) {\n    const themeFn = theme as ThemeFn;\n    const mergedTheme = themeFn(outerTheme);\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      (mergedTheme === null || Array.isArray(mergedTheme) || typeof mergedTheme !== 'object')\n    ) {\n      throw styledError(7);\n    }\n\n    return mergedTheme;\n  }\n\n  if (Array.isArray(theme) || typeof theme !== 'object') {\n    throw styledError(8);\n  }\n\n  return outerTheme ? { ...outerTheme, ...theme } : theme;\n}\n\n/**\n * Returns the current theme (as provided by the closest ancestor `ThemeProvider`.)\n *\n * If no `ThemeProvider` is found, the function will error. If you need access to the theme in an\n * uncertain composition scenario, `React.useContext(ThemeContext)` will not emit an error if there\n * is no `ThemeProvider` ancestor.\n */\nexport function useTheme(): DefaultTheme {\n  const theme = useContext(ThemeContext);\n\n  if (!theme) {\n    throw styledError(18);\n  }\n\n  return theme;\n}\n\n/**\n * Provide a theme to an entire react component tree via context\n */\nexport default function ThemeProvider(props: Props): React.JSX.Element | null {\n  const outerTheme = React.useContext(ThemeContext);\n  const themeContext = useMemo(\n    () => mergeTheme(props.theme, outerTheme),\n    [props.theme, outerTheme]\n  );\n\n  if (!props.children) {\n    return null;\n  }\n\n  return <ThemeContext.Provider value={themeContext}>{props.children}</ThemeContext.Provider>;\n}\n", "import { DefaultTheme, ExecutionProps } from '../types';\nimport { EMPTY_OBJECT } from './empties';\n\nexport default function determineTheme(\n  props: ExecutionProps,\n  providedTheme?: DefaultTheme | undefined,\n  defaultProps: { theme?: DefaultTheme | undefined } = EMPTY_OBJECT\n): DefaultTheme | undefined {\n  return (props.theme !== defaultProps.theme && props.theme) || providedTheme || defaultProps.theme;\n}\n", "import React from 'react';\nimport { AnyComponent } from '../types';\n\nconst hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\n// copied from react-is\nconst REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nconst REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\n/**\n * Adapted from hoist-non-react-statics to avoid the react-is dependency.\n */\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n};\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n};\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n};\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n};\n\nconst TYPE_STATICS = {\n  [REACT_FORWARD_REF_TYPE]: FORWARD_REF_STATICS,\n  [REACT_MEMO_TYPE]: MEMO_STATICS,\n};\n\ntype OmniComponent = AnyComponent;\n\n// adapted from react-is\nfunction isMemo(\n  object: OmniComponent | React.MemoExoticComponent<any>\n): object is React.MemoExoticComponent<any> {\n  const $$typeofType = 'type' in object && object.type.$$typeof;\n\n  return $$typeofType === REACT_MEMO_TYPE;\n}\n\nfunction getStatics(component: OmniComponent) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS;\n  }\n\n  // React v16.12 and above\n  return '$$typeof' in component\n    ? TYPE_STATICS[component['$$typeof'] as unknown as string]\n    : REACT_STATICS;\n}\n\nconst defineProperty = Object.defineProperty;\nconst getOwnPropertyNames = Object.getOwnPropertyNames;\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols;\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nconst getPrototypeOf = Object.getPrototypeOf;\nconst objectPrototype = Object.prototype;\n\ntype ExcludeList = {\n  [key: string]: true;\n};\n\nexport type NonReactStatics<S extends OmniComponent, C extends ExcludeList = {}> = {\n  [key in Exclude<\n    keyof S,\n    S extends React.MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : S extends React.ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: S[key];\n};\n\nexport default function hoistNonReactStatics<\n  T extends OmniComponent,\n  S extends OmniComponent,\n  C extends ExcludeList = {},\n>(targetComponent: T, sourceComponent: S, excludelist?: C | undefined) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, excludelist);\n      }\n    }\n\n    let keys: (String | Symbol)[] = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    const targetStatics = getStatics(targetComponent);\n    const sourceStatics = getStatics(sourceComponent);\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i] as unknown as string;\n      if (\n        !(key in KNOWN_STATICS) &&\n        !(excludelist && excludelist[key]) &&\n        !(sourceStatics && key in sourceStatics) &&\n        !(targetStatics && key in targetStatics)\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!);\n        } catch (e) {\n          /* ignore */\n        }\n      }\n    }\n  }\n\n  return targetComponent as T & NonReactStatics<S, C>;\n}\n", "const AD_REPLACER_R = /(a)(d)/gi;\n\n/* This is the \"capacity\" of our alphabet i.e. 2x26 for all letters plus their capitalised\n * counterparts */\nconst charsLength = 52;\n\n/* start at 75 for 'a' until 'z' (25) and then start at 65 for capitalised letters */\nconst getAlphabeticChar = (code: number) => String.fromCharCode(code + (code > 25 ? 39 : 97));\n\n/* input a number, usually a hash and convert it to base-52 */\nexport default function generateAlphabeticName(code: number) {\n  let name = '';\n  let x;\n\n  /* get a char and divide by alphabet-length */\n  for (x = Math.abs(code); x > charsLength; x = (x / charsLength) | 0) {\n    name = getAlphabeticChar(x % charsLength) + name;\n  }\n\n  return (getAlphabeticChar(x % charsLength) + name).replace(AD_REPLACER_R, '$1-$2');\n}\n", "/**\n * Convenience function for joining strings to form className chains\n */\nexport function joinStrings(a?: string | undefined, b?: string | undefined): string {\n  return a && b ? `${a} ${b}` : a || b || '';\n}\n\nexport function joinStringArray(arr: string[], sep?: string | undefined): string {\n  if (arr.length === 0) {\n    return '';\n  }\n\n  let result = arr[0];\n  for (let i = 1; i < arr.length; i++) {\n    result += sep ? sep + arr[i] : arr[i];\n  }\n  return result;\n}\n", "import transformDeclPairs from 'css-to-react-native';\nimport { parse } from 'postcss';\nimport {\n  Dict,\n  ExecutionContext,\n  IInlineStyle,\n  IInlineStyleConstructor,\n  RuleSet,\n  StyleSheet,\n} from '../types';\nimport flatten from '../utils/flatten';\nimport generateComponentId from '../utils/generateComponentId';\nimport { joinStringArray } from '../utils/joinStrings';\n\n// List of CSS values not supported by React Native\nexport const RN_UNSUPPORTED_VALUES = ['fit-content', 'min-content', 'max-content'];\n\nlet generated: Dict<any> = {};\n\nexport const resetStyleCache = (): void => {\n  generated = {};\n};\n\n/**\n * InlineStyle takes arbitrary CSS and generates a flat object\n */\nexport default function makeInlineStyleClass<Props extends object>(styleSheet: StyleSheet) {\n  const InlineStyle: IInlineStyleConstructor<Props> = class InlineStyle\n    implements IInlineStyle<Props>\n  {\n    rules: RuleSet<Props>;\n\n    constructor(rules: RuleSet<Props>) {\n      this.rules = rules;\n    }\n\n    generateStyleObject(executionContext: ExecutionContext & Props) {\n      // keyframes, functions, and component selectors are not allowed for React Native\n      const flatCSS = joinStringArray(\n        flatten(this.rules as RuleSet<object>, executionContext) as string[]\n      );\n      const hash = generateComponentId(flatCSS);\n\n      if (!generated[hash]) {\n        const root = parse(flatCSS);\n        const declPairs: [string, string][] = [];\n\n        root.each(node => {\n          if (node.type === 'decl') {\n            if (RN_UNSUPPORTED_VALUES.includes(node.value)) {\n              if (process.env.NODE_ENV !== 'production') {\n                console.warn(\n                  `[styled-components/native] The value \"${node.value}\" for property \"${node.prop}\" is not supported in React Native and will be ignored.`\n                );\n              }\n              return;\n            }\n            declPairs.push([node.prop, node.value]);\n          } else if (process.env.NODE_ENV !== 'production' && node.type !== 'comment') {\n            console.warn(`Node of type ${node.type} not supported as an inline style`);\n          }\n        });\n\n        // RN currently does not support differing values for the border color of Image\n        // components (but does for View). It is almost impossible to tell whether we'll have\n        // support, so we'll just disable multiple values here.\n        // https://github.com/styled-components/styled-components/issues/4181\n\n        const styleObject = transformDeclPairs(declPairs, ['borderWidth', 'borderColor']);\n\n        const styles = styleSheet.create({\n          generated: styleObject,\n        });\n\n        generated[hash] = styles.generated;\n      }\n      return generated[hash];\n    }\n  };\n\n  return InlineStyle;\n}\n", "import isPlainObject from './isPlainObject';\n\nfunction mixinRecursively(target: any, source: any, forceMerge = false) {\n  /* only merge into POJOs, Arrays, but for top level objects only\n   * allow to merge into anything by passing forceMerge = true */\n  if (!forceMerge && !isPlainObject(target) && !Array.isArray(target)) {\n    return source;\n  }\n\n  if (Array.isArray(source)) {\n    for (let key = 0; key < source.length; key++) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  } else if (isPlainObject(source)) {\n    for (const key in source) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Arrays & POJOs merged recursively, other objects and value types are overridden\n * If target is not a POJO or an Array, it will get source properties injected via shallow merge\n * Source objects applied left to right.  Mutates & returns target.  Similar to lodash merge.\n */\nexport default function mixinDeep(target: any, ...sources: any[]) {\n  for (const source of sources) {\n    mixinRecursively(target, source, true);\n  }\n\n  return target;\n}\n", "import React, { createElement, Ref, useMemo } from 'react';\nimport type {\n  Attrs,\n  BaseObject,\n  Dict,\n  ExecutionContext,\n  ExecutionProps,\n  IInlineStyleConstructor,\n  IStyledComponent,\n  IStyledComponentFactory,\n  IStyledStatics,\n  NativeTarget,\n  OmitNever,\n  RuleSet,\n  StyledOptions,\n} from '../types';\nimport determineTheme from '../utils/determineTheme';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from '../utils/empties';\nimport generateDisplayName from '../utils/generateDisplayName';\nimport hoist from '../utils/hoist';\nimport isFunction from '../utils/isFunction';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport merge from '../utils/mixinDeep';\nimport { DefaultTheme, ThemeContext } from './ThemeProvider';\n\nfunction useResolvedAttrs<Props extends object>(\n  theme: DefaultTheme = EMPTY_OBJECT,\n  props: Props,\n  attrs: Attrs<Props>[]\n) {\n  // NOTE: can't memoize this\n  // returns [context, resolvedAttrs]\n  // where resolvedAttrs is only the things injected by the attrs themselves\n  const context: ExecutionContext & Props = { ...props, theme };\n  const resolvedAttrs: Dict<any> = {};\n\n  attrs.forEach(attrDef => {\n    let resolvedAttrDef = isFunction(attrDef) ? attrDef(context) : attrDef;\n    let key;\n\n    for (key in resolvedAttrDef) {\n      // @ts-expect-error bad types\n      context[key] = resolvedAttrs[key] = resolvedAttrDef[key];\n    }\n  });\n\n  return [context, resolvedAttrs] as const;\n}\n\ninterface StyledComponentImplProps extends ExecutionProps {\n  style?: any;\n}\n\nfunction useStyledComponentImpl<Props extends StyledComponentImplProps>(\n  forwardedComponent: IStyledComponent<'native', Props>,\n  props: Props,\n  forwardedRef: Ref<any>\n) {\n  const {\n    attrs: componentAttrs,\n    inlineStyle,\n    defaultProps,\n    shouldForwardProp,\n    target,\n  } = forwardedComponent;\n\n  const contextTheme = React.useContext(ThemeContext);\n\n  // NOTE: the non-hooks version only subscribes to this when !componentStyle.isStatic,\n  // but that'd be against the rules-of-hooks. We could be naughty and do it anyway as it\n  // should be an immutable value, but behave for now.\n  const theme = determineTheme(props, contextTheme, defaultProps);\n\n  const [context, attrs] = useResolvedAttrs<Props>(theme || EMPTY_OBJECT, props, componentAttrs);\n\n  const generatedStyles = inlineStyle.generateStyleObject(context);\n\n  const refToForward = forwardedRef;\n\n  const elementToBeCreated: NativeTarget = attrs.as || props.as || target;\n\n  const computedProps: Dict<any> = attrs !== props ? { ...props, ...attrs } : props;\n  const propsForElement: Dict<any> = {};\n\n  for (const key in computedProps) {\n    if (key[0] === '$' || key === 'as') continue;\n    else if (key === 'forwardedAs') {\n      propsForElement.as = computedProps[key];\n    } else if (!shouldForwardProp || shouldForwardProp(key, elementToBeCreated)) {\n      propsForElement[key] = computedProps[key];\n    }\n  }\n\n  propsForElement.style = useMemo(\n    () =>\n      isFunction(props.style)\n        ? (state: any) => [generatedStyles].concat(props.style(state))\n        : props.style\n          ? [generatedStyles].concat(props.style)\n          : generatedStyles,\n    [props.style, generatedStyles]\n  );\n  // forwardedRef is coming from React.forwardRef.\n  // But it might not exist. Since React 19 handles `ref` like a prop, it only define it if there is a value.\n  // We don't want to inject an empty ref.\n  if (forwardedRef) {\n    propsForElement.ref = refToForward;\n  }\n\n  return createElement(elementToBeCreated, propsForElement);\n}\n\nexport default (InlineStyle: IInlineStyleConstructor<any>) => {\n  const createStyledNativeComponent = <\n    Target extends NativeTarget,\n    OuterProps extends ExecutionProps,\n    Statics extends object = BaseObject,\n  >(\n    target: Target,\n    options: StyledOptions<'native', OuterProps>,\n    rules: RuleSet<OuterProps>\n  ): ReturnType<IStyledComponentFactory<'native', Target, OuterProps, Statics>> => {\n    const isTargetStyledComp = isStyledComponent(target);\n    const styledComponentTarget = target as IStyledComponent<'native', OuterProps>;\n\n    const { displayName = generateDisplayName(target), attrs = EMPTY_ARRAY } = options;\n\n    // fold the underlying StyledComponent attrs up (implicit extend)\n    const finalAttrs =\n      isTargetStyledComp && styledComponentTarget.attrs\n        ? styledComponentTarget.attrs.concat(attrs).filter(Boolean)\n        : (attrs as Attrs<OuterProps>[]);\n\n    let shouldForwardProp = options.shouldForwardProp;\n\n    if (isTargetStyledComp && styledComponentTarget.shouldForwardProp) {\n      const shouldForwardPropFn = styledComponentTarget.shouldForwardProp;\n\n      if (options.shouldForwardProp) {\n        const passedShouldForwardPropFn = options.shouldForwardProp;\n\n        // compose nested shouldForwardProp calls\n        shouldForwardProp = (prop, elementToBeCreated) =>\n          shouldForwardPropFn(prop, elementToBeCreated) &&\n          passedShouldForwardPropFn(prop, elementToBeCreated);\n      } else {\n        shouldForwardProp = shouldForwardPropFn;\n      }\n    }\n\n    const forwardRefRender = (props: ExecutionProps & OuterProps, ref: React.Ref<any>) =>\n      useStyledComponentImpl<OuterProps>(WrappedStyledComponent, props, ref);\n\n    forwardRefRender.displayName = displayName;\n\n    /**\n     * forwardRef creates a new interim component, which we'll take advantage of\n     * instead of extending ParentComponent to create _another_ interim class\n     */\n    let WrappedStyledComponent = React.forwardRef(forwardRefRender) as unknown as IStyledComponent<\n      'native',\n      any\n    > &\n      Statics;\n\n    WrappedStyledComponent.attrs = finalAttrs;\n    WrappedStyledComponent.inlineStyle = new InlineStyle(\n      isTargetStyledComp ? styledComponentTarget.inlineStyle.rules.concat(rules) : rules\n    ) as InstanceType<IInlineStyleConstructor<OuterProps>>;\n    WrappedStyledComponent.displayName = displayName;\n    WrappedStyledComponent.shouldForwardProp = shouldForwardProp;\n\n    // @ts-expect-error we don't actually need this for anything other than detection of a styled-component\n    WrappedStyledComponent.styledComponentId = true;\n\n    // fold the underlying StyledComponent target up since we folded the styles\n    WrappedStyledComponent.target = isTargetStyledComp ? styledComponentTarget.target : target;\n\n    Object.defineProperty(WrappedStyledComponent, 'defaultProps', {\n      get() {\n        return this._foldedDefaultProps;\n      },\n\n      set(obj) {\n        this._foldedDefaultProps = isTargetStyledComp\n          ? merge({}, styledComponentTarget.defaultProps, obj)\n          : obj;\n      },\n    });\n\n    hoist<typeof WrappedStyledComponent, typeof target>(WrappedStyledComponent, target, {\n      // all SC-specific things should not be hoisted\n      attrs: true,\n      inlineStyle: true,\n      displayName: true,\n      shouldForwardProp: true,\n      target: true,\n    } as { [key in keyof OmitNever<IStyledStatics<'native', Target>>]: true });\n\n    return WrappedStyledComponent;\n  };\n\n  return createStyledNativeComponent;\n};\n", "import transformDeclPairs from 'css-to-react-native';\nimport { parse } from 'postcss';\nimport React from 'react';\nimport constructWithOptions, { Styled } from '../constructors/constructWithOptions';\nimport css from '../constructors/css';\nimport withTheme from '../hoc/withTheme';\nimport _InlineStyle from '../models/InlineStyle';\nimport _StyledNativeComponent from '../models/StyledNativeComponent';\nimport ThemeProvider, { ThemeConsumer, ThemeContext, useTheme } from '../models/ThemeProvider';\nimport { NativeTarget, RuleSet } from '../types';\nimport flatten from '../utils/flatten';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport { joinStringArray } from '../utils/joinStrings';\n\nconst reactNative = require('react-native') as Awaited<typeof import('react-native')>;\n\nconst InlineStyle = _InlineStyle(reactNative.StyleSheet);\nconst StyledNativeComponent = _StyledNativeComponent(InlineStyle);\n\nconst baseStyled = <Target extends NativeTarget>(tag: Target) =>\n  constructWithOptions<'native', Target>(StyledNativeComponent, tag);\n\n/* React native lazy-requires each of these modules for some reason, so let's\n *  assume it's for a good reason and not eagerly load them all */\nconst aliases = [\n  'ActivityIndicator',\n  'Button',\n  'DatePickerIOS',\n  'DrawerLayoutAndroid',\n  'FlatList',\n  'Image',\n  'ImageBackground',\n  'KeyboardAvoidingView',\n  'Modal',\n  'Pressable',\n  'ProgressBarAndroid',\n  'ProgressViewIOS',\n  'RefreshControl',\n  'SafeAreaView',\n  'ScrollView',\n  'SectionList',\n  'Slider',\n  'Switch',\n  'Text',\n  'TextInput',\n  'TouchableHighlight',\n  'TouchableOpacity',\n  'View',\n  'VirtualizedList',\n] as const;\n\ntype KnownComponents = (typeof aliases)[number];\n\n/** Isolates RN-provided components since they don't expose a helper type for this. */\ntype RNComponents = {\n  [K in keyof typeof reactNative]: (typeof reactNative)[K] extends React.ComponentType<any>\n    ? (typeof reactNative)[K]\n    : never;\n};\n\nconst styled = baseStyled as typeof baseStyled & {\n  [E in KnownComponents]: Styled<'native', RNComponents[E], React.ComponentProps<RNComponents[E]>>;\n};\n\n/* Define a getter for each alias which simply gets the reactNative component\n * and passes it to styled */\naliases.forEach(alias =>\n  Object.defineProperty(styled, alias, {\n    enumerable: true,\n    configurable: false,\n    get() {\n      if (alias in reactNative && reactNative[alias]) {\n        return styled(reactNative[alias]);\n      }\n\n      throw new Error(\n        `${alias} is not available in the currently-installed version of react-native`\n      );\n    },\n  })\n);\n\nconst toStyleSheet = (rules: RuleSet<object>) => {\n  const flatCSS = joinStringArray(flatten(rules) as string[]);\n\n  const root = parse(flatCSS);\n  const declPairs: [string, string][] = [];\n\n  root.each(node => {\n    if (node.type === 'decl') {\n      declPairs.push([node.prop, node.value]);\n    } else if (process.env.NODE_ENV !== 'production' && node.type !== 'comment') {\n      console.warn(`Node of type ${node.type} not supported as an inline style`);\n    }\n  });\n\n  const styleObject = transformDeclPairs(declPairs, ['borderWidth', 'borderColor']);\n\n  return reactNative.StyleSheet.create({ style: styleObject }).style;\n};\n\nexport {\n  CSSKeyframes,\n  CSSObject,\n  CSSProperties,\n  CSSPseudos,\n  DefaultTheme,\n  ExecutionContext,\n  ExecutionProps,\n  IStyledComponent,\n  IStyledComponentFactory,\n  IStyledStatics,\n  NativeTarget,\n  PolymorphicComponent,\n  PolymorphicComponentProps,\n  Runtime,\n  StyledObject,\n  StyledOptions,\n} from '../types';\nexport {\n  ThemeConsumer,\n  ThemeContext,\n  ThemeProvider,\n  css,\n  styled as default,\n  isStyledComponent,\n  styled,\n  toStyleSheet,\n  useTheme,\n  withTheme,\n};\n", "import generateAlphabeticName from './generateAlphabeticName';\nimport { hash } from './hash';\n\nexport default function generateComponentId(str: string) {\n  return generateAlphabeticName(hash(str) >>> 0);\n}\n", "import { StyledTarget } from '../types';\nimport getComponentName from './getComponentName';\nimport isTag from './isTag';\n\nexport default function generateDisplayName(target: StyledTarget<any>) {\n  return isTag(target) ? `styled.${target}` : `Styled(${getComponentName(target)})`;\n}\n", "import { StyledTarget } from '../types';\n\nexport default function isTag(target: StyledTarget<'web'>): target is string {\n  return (\n    typeof target === 'string' &&\n    (process.env.NODE_ENV !== 'production'\n      ? target.charAt(0) === target.charAt(0).toLowerCase()\n      : true)\n  );\n}\n", "import React from 'react';\nimport { ThemeContext } from '../models/ThemeProvider';\nimport { AnyComponent, ExecutionProps } from '../types';\nimport determineTheme from '../utils/determineTheme';\nimport getComponentName from '../utils/getComponentName';\nimport hoist, { NonReactStatics } from '../utils/hoist';\n\nexport default function withTheme<T extends AnyComponent>(\n  Component: T\n): React.ForwardRefExoticComponent<\n  React.PropsWithoutRef<React.JSX.LibraryManagedAttributes<T, ExecutionProps>> &\n    React.RefAttributes<T>\n> &\n  NonReactStatics<T> {\n  const WithTheme = React.forwardRef<T, React.JSX.LibraryManagedAttributes<T, ExecutionProps>>(\n    (props, ref) => {\n      const theme = React.useContext(ThemeContext);\n      const themeProp = determineTheme(props, theme, Component.defaultProps);\n\n      if (process.env.NODE_ENV !== 'production' && themeProp === undefined) {\n        console.warn(\n          `[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"${getComponentName(\n            Component\n          )}\"`\n        );\n      }\n\n      return <Component {...props} theme={themeProp} ref={ref} />;\n    }\n  );\n\n  WithTheme.displayName = `WithTheme(${getComponentName(Component)})`;\n\n  return hoist(WithTheme, Component);\n}\n"], "names": ["EMPTY_ARRAY", "Object", "freeze", "EMPTY_OBJECT", "ERRORS", "process", "env", "NODE_ENV", "format", "args", "_i", "arguments", "length", "a", "b", "c", "len", "push", "for<PERSON>ach", "d", "replace", "throwStyledComponentsError", "code", "interpolations", "Error", "concat", "join", "trim", "setToString", "object", "toStringFn", "defineProperty", "value", "SC_ATTR", "REACT_APP_SC_ATTR", "SC_ATTR_ACTIVE", "SC_ATTR_VERSION", "SC_VERSION", "SPLITTER", "IS_BROWSER", "window", "document", "DISABLE_SPEEDY", "Boolean", "SC_DISABLE_SPEEDY", "REACT_APP_SC_DISABLE_SPEEDY", "DefaultGroupedTag", "tag", "this", "groupSizes", "Uint32Array", "prototype", "indexOfGroup", "group", "index", "i", "insertRules", "rules", "<PERSON><PERSON><PERSON><PERSON>", "oldSize", "newSize", "styledError", "set", "ruleIndex", "l", "insertRule", "clearGroup", "length_1", "startIndex", "endIndex", "deleteRule", "getGroup", "css", "getRule", "MAX_SMI", "groupIDRegister", "Map", "reverseRegister", "nextFreeGroup", "getGroupForId", "id", "has", "get", "setGroupForId", "SELECTOR", "MARKER_RE", "RegExp", "rehydrateNamesFromContent", "sheet", "content", "name", "names", "split", "registerName", "rehydrateSheetFromTag", "style", "parts", "_a", "textContent", "part", "marker", "match", "parseInt", "getTag", "rehydrateSheet", "nodes", "querySelectorAll", "node", "getAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "makeStyleTag", "target", "head", "parent", "createElement", "prevStyle", "arr", "Array", "from", "findLastStyleTag", "nextS<PERSON>ling", "undefined", "setAttribute", "nonce", "__webpack_nonce__", "insertBefore", "CSSOMTag", "element", "append<PERSON><PERSON><PERSON>", "createTextNode", "styleSheets", "ownerNode", "getSheet", "rule", "_error", "cssRules", "cssText", "TextTag", "childNodes", "VirtualTag", "_target", "splice", "SHOULD_REHYDRATE", "defaultOptions", "isServer", "useCSSOMInjection", "StyleSheet", "options", "globalStyles", "_this", "__assign", "gs", "server", "getIdForGroup", "size", "selector", "outputSheet", "registerId", "rehydrate", "reconstructWithOptions", "with<PERSON><PERSON>s", "allocateGSInstance", "makeTag", "hasNameForId", "add", "groupNames", "Set", "clearNames", "clear", "clearRules", "clearTag", "phash", "h", "x", "charCodeAt", "AMP_REGEX", "COMMENT_REGEX", "recursivelySetNamepace", "compiled", "namespace", "map", "type", "replaceAll", "props", "prop", "isArray", "children", "mainSheet", "mainStylis", "_componentId", "_selector", "_selectorRegexp", "_c", "_d", "plugins", "selfReferenceReplacer", "offset", "string", "startsWith", "endsWith", "middlewares", "slice", "stylis", "RULESET", "includes", "prefix", "prefixer", "stringify", "stringifyRules", "componentId", "flatCSS", "compile", "stack", "serialize", "middleware", "rulesheet", "hash", "reduce", "acc", "plugin", "throwStyledError", "toString", "createStylisInstance", "Keyframes", "React", "default", "createContext", "shouldForwardProp", "styleSheet", "inject", "stylisInstance", "resolvedName", "String", "getName", "getComponentName", "displayName", "isUpper", "hyphenateStyleName", "output", "toLowerCase", "isFunction", "test", "isPlainObject", "constructor", "$$typeof", "isStyledComponent", "isFalsish", "chunk", "objToCssArray", "obj", "key", "val", "hasOwnProperty", "isCss", "hyphenate", "apply", "unitless", "flatten", "executionContext", "styledComponentId", "isReactComponent", "result", "console", "error", "chunklet", "interleave", "strings", "addTag", "arg", "assign", "styles", "__spread<PERSON><PERSON>y", "styleStringArray", "constructWithOptions", "componentConstructor", "templateFunction", "initialStyles", "attrs", "filter", "withConfig", "config", "ThemeContext", "ThemeConsumer", "Consumer", "determineTheme", "providedTheme", "defaultProps", "theme", "hasSymbol", "Symbol", "for", "REACT_MEMO_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "KNOWN_STATICS", "caller", "callee", "arity", "MEMO_STATICS", "compare", "TYPE_STATICS", "render", "getStatics", "component", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "excludelist", "inheritedComponent", "keys", "targetStatics", "sourceStatics", "descriptor", "e", "AD_REPLACER_R", "getAlphabeticChar", "fromCharCode", "joinStringArray", "sep", "RN_UNSUPPORTED_VALUES", "generated", "mixinRecursively", "source", "forceMerge", "InlineStyle", "reactNative", "require", "StyledNativeComponent", "generateStyleObject", "Math", "abs", "generateAlphabeticName", "root", "parse", "declPairs_1", "each", "warn", "styleObject", "transformDeclPairs", "create", "isTargetStyledComp", "styledComponentTarget", "char<PERSON>t", "isTag", "generateDisplayName", "_b", "finalAttrs", "shouldForwardPropFn_1", "passedShouldForwardPropFn_1", "elementToBeCreated", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "forwardedComponent", "forwardedRef", "componentAttrs", "inlineStyle", "contextTheme", "useContext", "context", "resolvedAttrs", "attrDef", "resolvedAttrDef", "useResolvedAttrs", "generatedStyles", "refToForward", "as", "computedProps", "propsForElement", "useMemo", "state", "useStyledComponentImpl", "WrappedStyledComponent", "forwardRef", "_foldedDefaultProps", "sources", "sources_1", "merge", "hoist", "styled", "alias", "enumerable", "configurable", "outerTheme", "themeContext", "mergedTheme", "mergeTheme", "Provider", "declPairs", "Component", "WithTheme", "themeProp"], "mappings": "upBAEaA,EAAcC,OAAOC,OAAO,IAC5BC,EAAeF,OAAOC,OAAO,ICApCE,EAA6C,eAAzBC,QAAQC,IAAIC,SCHvB,CACb,EAAK,wDACL,EAAK,gQACL,EAAK,sHACL,EAAK,sMACL,EAAK,kKACL,EAAK,4OACL,EAAK,qHACL,EAAK,8DACL,EAAK,gCACL,GAAM,iUACN,GAAM,wNACN,GAAM,qWACN,GAAM,yLACN,GAAM,+CACN,GAAM,2ZACN,GAAM,uQACN,GAAM,yIACN,GAAM,oFDfqE,GAK7E,SAASC,QAAO,IAAyBC,EAAA,GAAAC,EAAA,EAAzBA,EAAyBC,UAAAC,OAAzBF,IAAAD,EAAyBC,GAAAC,UAAAD,GAIvC,IAHA,IAAIG,EAAIJ,EAAK,GACPK,EAAI,GAEDC,EAAI,EAAGC,EAAMP,EAAKG,OAAQG,EAAIC,EAAKD,GAAK,EAC/CD,EAAEG,KAAKR,EAAKM,IAOd,OAJAD,EAAEI,QAAQ,SAAAC,GACRN,EAAIA,EAAEO,QAAQ,SAAUD,EAC1B,GAEON,CACT,CAMwB,SAAAQ,EACtBC,OACA,IAAwBC,EAAA,GAAAb,EAAA,EAAxBA,EAAwBC,UAAAC,OAAxBF,IAAAa,EAAwBb,EAAA,GAAAC,UAAAD,GAExB,MAA6B,eAAzBL,QAAQC,IAAIC,SACP,IAAIiB,MACT,0IAAAC,OAA0IH,EAAI,0BAAAG,OAC5IF,EAAeX,OAAS,EAAI,UAAUa,OAAAF,EAAeG,KAAK,OAAU,KAIjE,IAAIF,MAAMhB,gCAAOJ,EAAOkB,IAAUC,GAAc,IAAEI,OAE7D,CExBgB,SAAAC,EAAYC,EAAgBC,GAC1C7B,OAAO8B,eAAeF,EAAQ,WAAY,CAAEG,MAAOF,GACrD,CCfO,IAAMG,EACS,oBAAZ5B,cACiB,IAAhBA,QAAQC,MACdD,QAAQC,IAAI4B,mBAAqB7B,QAAQC,IAAI2B,UAChD,cAEWE,EAAiB,SACjBC,EAAkB,sBAClBC,EAAa,SACbC,EAAW,YAEXC,EAA+B,oBAAXC,QAA8C,oBAAbC,SAErDC,EAAiBC,QACC,kBAAtBC,kBACHA,kBACmB,oBAAZvC,cACkB,IAAhBA,QAAQC,UACoC,IAA5CD,QAAQC,IAAIuC,6BACyB,KAA5CxC,QAAQC,IAAIuC,4BACgC,UAA5CxC,QAAQC,IAAIuC,6BAEVxC,QAAQC,IAAIuC,4BACK,oBAAZxC,cACkB,IAAhBA,QAAQC,UAC0B,IAAlCD,QAAQC,IAAIsC,mBACe,KAAlCvC,QAAQC,IAAIsC,kBACsB,UAAlCvC,QAAQC,IAAIsC,mBAEVvC,QAAQC,IAAIsC,kBACW,eAAzBvC,QAAQC,IAAIC,UCtBhBuC,EAAiB,WAKrB,SAAAA,EAAYC,GACVC,KAAKC,WAAa,IAAIC,YARR,KASdF,KAAKpC,OATS,IAUdoC,KAAKD,IAAMA,CACZ,CAyEH,OAvEED,EAAYK,UAAAC,aAAZ,SAAaC,GAEX,IADA,IAAIC,EAAQ,EACHC,EAAI,EAAGA,EAAIF,EAAOE,IACzBD,GAASN,KAAKC,WAAWM,GAG3B,OAAOD,GAGTR,EAAAK,UAAAK,YAAA,SAAYH,EAAeI,GACzB,GAAIJ,GAASL,KAAKC,WAAWrC,OAAQ,CAKnC,IAJA,IAAM8C,EAAYV,KAAKC,WACjBU,EAAUD,EAAU9C,OAEtBgD,EAAUD,EACPN,GAASO,GAEd,IADAA,IAAY,GACE,EACZ,MAAMC,EAAY,GAAI,UAAGR,IAI7BL,KAAKC,WAAa,IAAIC,YAAYU,GAClCZ,KAAKC,WAAWa,IAAIJ,GACpBV,KAAKpC,OAASgD,EAEd,IAAK,IAAIL,EAAII,EAASJ,EAAIK,EAASL,IACjCP,KAAKC,WAAWM,GAAK,CAExB,CAID,IAFA,IAAIQ,EAAYf,KAAKI,aAAaC,EAAQ,GAE1BW,GAAPT,EAAI,EAAOE,EAAM7C,QAAQ2C,EAAIS,EAAGT,IACnCP,KAAKD,IAAIkB,WAAWF,EAAWN,EAAMF,MACvCP,KAAKC,WAAWI,KAChBU,MAKNjB,EAAUK,UAAAe,WAAV,SAAWb,GACT,GAAIA,EAAQL,KAAKpC,OAAQ,CACvB,IAAMuD,EAASnB,KAAKC,WAAWI,GACzBe,EAAapB,KAAKI,aAAaC,GAC/BgB,EAAWD,EAAaD,EAE9BnB,KAAKC,WAAWI,GAAS,EAEzB,IAAK,IAAIE,EAAIa,EAAYb,EAAIc,EAAUd,IACrCP,KAAKD,IAAIuB,WAAWF,EAEvB,GAGHtB,EAAQK,UAAAoB,SAAR,SAASlB,GACP,IAAImB,EAAM,GACV,GAAInB,GAASL,KAAKpC,QAAqC,IAA3BoC,KAAKC,WAAWI,GAC1C,OAAOmB,EAOT,IAJA,IAAM5D,EAASoC,KAAKC,WAAWI,GACzBe,EAAapB,KAAKI,aAAaC,GAC/BgB,EAAWD,EAAaxD,EAErB2C,EAAIa,EAAYb,EAAIc,EAAUd,IACrCiB,GAAO,GAAA/C,OAAGuB,KAAKD,IAAI0B,QAAQlB,IAAK9B,OAAAa,GAGlC,OAAOkC,GAEV1B,CAAD,IC3FM4B,EAAU,GAAC,GAEbC,EAAuC,IAAIC,IAC3CC,EAAuC,IAAID,IAC3CE,EAAgB,EAQPC,EAAgB,SAACC,GAC5B,GAAIL,EAAgBM,IAAID,GACtB,OAAOL,EAAgBO,IAAIF,GAG7B,KAAOH,EAAgBI,IAAIH,IACzBA,IAGF,IAAMzB,EAAQyB,IAEd,GAA6B,eAAzBzE,QAAQC,IAAIC,YAAuC,EAAR8C,GAAa,GAAKA,EAAQqB,GACvE,MAAMb,EAAY,GAAI,UAAGR,IAK3B,OAFAsB,EAAgBb,IAAIkB,EAAI3B,GACxBwB,EAAgBf,IAAIT,EAAO2B,GACpB3B,CACT,EAMa8B,EAAgB,SAACH,EAAY3B,GAExCyB,EAAgBzB,EAAQ,EAExBsB,EAAgBb,IAAIkB,EAAI3B,GACxBwB,EAAgBf,IAAIT,EAAO2B,EAC7B,ECxCMI,EAAW,SAAS3D,OAAAQ,eAAYG,EAAe,MAAAX,OAAKY,EAAU,MAC9DgD,EAAY,IAAIC,OAAO,IAAI7D,OAAAQ,EAAqD,iDAkChFsD,EAA4B,SAACC,EAAcR,EAAYS,GAI3D,IAHA,IACIC,EADEC,EAAQF,EAAQG,MAAM,KAGnBrC,EAAI,EAAGS,EAAI2B,EAAM/E,OAAQ2C,EAAIS,EAAGT,KAClCmC,EAAOC,EAAMpC,KAChBiC,EAAMK,aAAab,EAAIU,EAG7B,EAEMI,EAAwB,SAACN,EAAcO,GAI3C,UAHMC,GAA8B,QAArBC,EAAAF,EAAMG,mBAAe,IAAAD,EAAAA,EAAA,IAAIL,MAAMtD,GACxCmB,EAAkB,GAEfF,EAAI,EAAGS,EAAIgC,EAAMpF,OAAQ2C,EAAIS,EAAGT,IAAK,CAC5C,IAAM4C,EAAOH,EAAMzC,GAAG5B,OACtB,GAAKwE,EAAL,CAEA,IAAMC,EAASD,EAAKE,MAAMhB,GAE1B,GAAIe,EAAQ,CACV,IAAM/C,EAAkC,EAA1BiD,SAASF,EAAO,GAAI,IAC5BpB,EAAKoB,EAAO,GAEJ,IAAV/C,IAEF8B,EAAcH,EAAI3B,GAGlBkC,EAA0BC,EAAOR,EAAIoB,EAAO,IAC5CZ,EAAMe,SAAS/C,YAAYH,EAAOI,IAGpCA,EAAM7C,OAAS,CAChB,MACC6C,EAAMxC,KAAKkF,EAnBO,CAqBrB,CACH,EAEaK,EAAiB,SAAChB,GAG7B,IAFA,IAAMiB,EAAQhE,SAASiE,iBAAiBtB,GAE/B7B,EAAI,EAAGS,EAAIyC,EAAM7F,OAAQ2C,EAAIS,EAAGT,IAAK,CAC5C,IAAMoD,EAAOF,EAAMlD,GACfoD,GAAQA,EAAKC,aAAa3E,KAAaE,IACzC2D,EAAsBN,EAAOmB,GAEzBA,EAAKE,YACPF,EAAKE,WAAWC,YAAYH,GAGjC,CACH,EChFaI,EAAe,SAACC,GAC3B,IAAMC,EAAOxE,SAASwE,KAChBC,EAASF,GAAUC,EACnBlB,EAAQtD,SAAS0E,cAAc,SAC/BC,EAXiB,SAACJ,GACxB,IAAMK,EAAMC,MAAMC,KAAKP,EAAON,iBAAmC,SAASjF,OAAAQ,EAAU,OAEpF,OAAOoF,EAAIA,EAAIzG,OAAS,EAC1B,CAOoB4G,CAAiBN,GAC7BO,OAA4BC,IAAdN,EAA0BA,EAAUK,YAAc,KAEtE1B,EAAM4B,aAAa1F,EAASE,GAC5B4D,EAAM4B,aAAavF,EAAiBC,GAEpC,IAAMuF,ECpB8B,oBAAtBC,kBAAoCA,kBAAoB,KD0BtE,OAJID,GAAO7B,EAAM4B,aAAa,QAASC,GAEvCV,EAAOY,aAAa/B,EAAO0B,GAEpB1B,CACT,EEfagC,EAAQ,WAOnB,SAAAA,EAAYf,GACVhE,KAAKgF,QAAUjB,EAAaC,GAG5BhE,KAAKgF,QAAQC,YAAYxF,SAASyF,eAAe,KAEjDlF,KAAKwC,MFKe,SAACzC,GACvB,GAAIA,EAAIyC,MACN,OAAOzC,EAAIyC,MAKb,IADQ,IAAA2C,EAAgB1F,SAAQ0F,YACvB5E,EAAI,EAAGS,EAAImE,EAAYvH,OAAQ2C,EAAIS,EAAGT,IAAK,CAClD,IAAMiC,EAAQ2C,EAAY5E,GAC1B,GAAIiC,EAAM4C,YAAcrF,EACtB,OAAOyC,CAEV,CAED,MAAM3B,EAAY,GACpB,CEpBiBwE,CAASrF,KAAKgF,SAC3BhF,KAAKpC,OAAS,CACf,CA2BH,OAzBEmH,EAAA5E,UAAAc,WAAA,SAAWX,EAAegF,GACxB,IAGE,OAFAtF,KAAKwC,MAAMvB,WAAWqE,EAAMhF,GAC5BN,KAAKpC,UACE,CACR,CAAC,MAAO2H,GACP,OAAO,CACR,GAGHR,EAAU5E,UAAAmB,WAAV,SAAWhB,GACTN,KAAKwC,MAAMlB,WAAWhB,GACtBN,KAAKpC,UAGPmH,EAAO5E,UAAAsB,QAAP,SAAQnB,GACN,IAAMgF,EAAOtF,KAAKwC,MAAMgD,SAASlF,GAGjC,OAAIgF,GAAQA,EAAKG,QACRH,EAAKG,QAEL,IAGZV,CAAD,IAGaW,EAAO,WAKlB,SAAAA,EAAY1B,GACVhE,KAAKgF,QAAUjB,EAAaC,GAC5BhE,KAAKyD,MAAQzD,KAAKgF,QAAQW,WAC1B3F,KAAKpC,OAAS,CACf,CA0BH,OAxBE8H,EAAAvF,UAAAc,WAAA,SAAWX,EAAegF,GACxB,GAAIhF,GAASN,KAAKpC,QAAU0C,GAAS,EAAG,CACtC,IAAMqD,EAAOlE,SAASyF,eAAeI,GAIrC,OAFAtF,KAAKgF,QAAQF,aAAanB,EADV3D,KAAKyD,MAAMnD,IACgB,MAC3CN,KAAKpC,UACE,CACR,CACC,OAAO,GAIX8H,EAAUvF,UAAAmB,WAAV,SAAWhB,GACTN,KAAKgF,QAAQlB,YAAY9D,KAAKyD,MAAMnD,IACpCN,KAAKpC,UAGP8H,EAAOvF,UAAAsB,QAAP,SAAQnB,GACN,OAAIA,EAAQN,KAAKpC,OACRoC,KAAKyD,MAAMnD,GAAO4C,YAElB,IAGZwC,CAAD,IAGaE,EAAU,WAKrB,SAAAA,EAAYC,GACV7F,KAAKS,MAAQ,GACbT,KAAKpC,OAAS,CACf,CAwBH,OAtBEgI,EAAAzF,UAAAc,WAAA,SAAWX,EAAegF,GACxB,OAAIhF,GAASN,KAAKpC,SAChBoC,KAAKS,MAAMqF,OAAOxF,EAAO,EAAGgF,GAC5BtF,KAAKpC,UACE,IAMXgI,EAAUzF,UAAAmB,WAAV,SAAWhB,GACTN,KAAKS,MAAMqF,OAAOxF,EAAO,GACzBN,KAAKpC,UAGPgI,EAAOzF,UAAAsB,QAAP,SAAQnB,GACN,OAAIA,EAAQN,KAAKpC,OACRoC,KAAKS,MAAMH,GAEX,IAGZsF,CAAD,ICxHIG,EAAmBxG,EAajByG,EAA+B,CACnCC,UAAW1G,EACX2G,mBAAoBxG,GAItByG,EAAA,WAYE,SAAAA,EACEC,EACAC,EACA1D,QAFA,IAAAyD,IAAAA,EAAgCjJ,QAChC,IAAAkJ,IAAAA,EAA4C,CAAA,GAF9C,IAqBCC,EAAAtG,KAhBCA,KAAKoG,QAAOG,WAAAA,EAAAA,SAAA,CAAA,EACPP,GACAI,GAGLpG,KAAKwG,GAAKH,EACVrG,KAAK2C,MAAQ,IAAIf,IAAIe,GACrB3C,KAAKyG,SAAWL,EAAQH,UAGnBjG,KAAKyG,QAAUlH,GAAcwG,IAChCA,GAAmB,EACnBvC,EAAexD,OAGjBpB,EAAYoB,KAAM,WAAM,OJtDD,SAACwC,GAK1B,IAJA,IAAMzC,EAAMyC,EAAMe,SACV3F,EAAWmC,EAAGnC,OAElB4D,EAAM,cACDnB,GACP,IAAM2B,EDqBmB,SAAC3B,GAC5B,OAAOwB,EAAgBK,IAAI7B,EAC7B,CCvBeqG,CAAcrG,GACzB,QAAWqE,IAAP1C,EAA2B,MAAA,WAE/B,IAAMW,EAAQH,EAAMG,MAAMT,IAAIF,GACxBvB,EAAQV,EAAIwB,SAASlB,GAC3B,QAAcqE,IAAV/B,IAAwBA,EAAMgE,MAAyB,IAAjBlG,EAAM7C,OAAuB,MAAA,WAEvE,IAAMgJ,EAAW,GAAGnI,OAAAQ,eAAYoB,EAAK,SAAA5B,OAAQuD,EAAE,MAE3CS,EAAU,QACAiC,IAAV/B,GACFA,EAAMzE,QAAQ,SAAAwE,GACRA,EAAK9E,OAAS,IAChB6E,GAAW,GAAAhE,OAAGiE,EAAI,KAEtB,GAKFlB,GAAO,GAAG/C,OAAAgC,GAAQhC,OAAAmI,uBAAqBnE,EAAO,MAAAhE,OAAKa,IArB5Ce,EAAQ,EAAGA,EAAQzC,EAAQyC,MAA3BA,GAwBT,OAAOmB,CACT,CIwB4BqF,CAAYP,EAAK,EAC1C,CAoEH,OA7FSH,EAAUW,WAAjB,SAAkB9E,GAChB,OAAOD,EAAcC,IA0BvBmE,EAAAhG,UAAA4G,UAAA,YACO/G,KAAKyG,QAAUlH,GAClBiE,EAAexD,OAInBmG,EAAAhG,UAAA6G,uBAAA,SAAuBZ,EAA+Ba,GACpD,YADoD,IAAAA,IAAAA,GAAgB,GAC7D,IAAId,EACJI,EAAAA,SAAAA,EAAAA,SAAA,CAAA,EAAAvG,KAAKoG,SAAYA,GACtBpG,KAAKwG,GACJS,GAAajH,KAAK2C,YAAU+B,IAIjCyB,EAAkBhG,UAAA+G,mBAAlB,SAAmBlF,GACjB,OAAQhC,KAAKwG,GAAGxE,IAAOhC,KAAKwG,GAAGxE,IAAO,GAAK,GAI7CmE,EAAAhG,UAAAoD,OAAA,WACE,OAAOvD,KAAKD,MAAQC,KAAKD,KN/EEA,EKAR,SAACkD,GAAE,IAAUiD,EAAiBjD,EAAAiD,kBAAElC,EAAMf,EAAAe,OAC3D,kBACS,IAAI4B,EAAW5B,GACbkC,EACF,IAAInB,EAASf,GAEb,IAAI0B,EAAQ1B,EAEvB,CCuEkDmD,CAAQnH,KAAKoG,SN9EtD,IAAItG,EAAkBC,KADD,IAACA,GMmF7BoG,EAAAhG,UAAAiH,aAAA,SAAapF,EAAYU,GACvB,OAAO1C,KAAK2C,MAAMV,IAAID,IAAQhC,KAAK2C,MAAMT,IAAIF,GAAYC,IAAIS,IAI/DyD,EAAAhG,UAAA0C,aAAA,SAAab,EAAYU,GAGvB,GAFAX,EAAcC,GAEThC,KAAK2C,MAAMV,IAAID,GAKjBhC,KAAK2C,MAAMT,IAAIF,GAAYqF,IAAI3E,OALT,CACvB,IAAM4E,EAAa,IAAIC,IACvBD,EAAWD,IAAI3E,GACf1C,KAAK2C,MAAM7B,IAAIkB,EAAIsF,EACpB,GAMHnB,EAAAhG,UAAAK,YAAA,SAAYwB,EAAYU,EAAcjC,GACpCT,KAAK6C,aAAab,EAAIU,GACtB1C,KAAKuD,SAAS/C,YAAYuB,EAAcC,GAAKvB,IAI/C0F,EAAUhG,UAAAqH,WAAV,SAAWxF,GACLhC,KAAK2C,MAAMV,IAAID,IAChBhC,KAAK2C,MAAMT,IAAIF,GAAYyF,SAKhCtB,EAAUhG,UAAAuH,WAAV,SAAW1F,GACThC,KAAKuD,SAASrC,WAAWa,EAAcC,IACvChC,KAAKwH,WAAWxF,IAIlBmE,EAAAhG,UAAAwH,SAAA,WAGE3H,KAAKD,SAAM2E,GAEdyB,CAAD,IC7HayB,EAAQ,SAACC,EAAWC,GAG/B,IAFA,IAAIvH,EAAIuH,EAAElK,OAEH2C,GACLsH,EAAS,GAAJA,EAAUC,EAAEC,aAAaxH,GAGhC,OAAOsH,CACT,ECPMG,EAAY,KACZC,EAAgB,gBAWtB,SAASC,EAAuBC,EAA4BC,GAC1D,OAAOD,EAASE,IAAI,SAAA/C,GAclB,MAbkB,SAAdA,EAAKgD,OAEPhD,EAAKtG,MAAQ,GAAGP,OAAA2J,cAAa9C,EAAKtG,OAElCsG,EAAKtG,MAAQsG,EAAKtG,MAAMuJ,WAAW,IAAK,IAAA9J,OAAI2J,EAAS,MACrD9C,EAAKkD,MAASlD,EAAKkD,MAAmBH,IAAI,SAAAI,GACxC,MAAO,GAAGhK,OAAA2J,EAAa,KAAA3J,OAAAgK,EACzB,IAGEnE,MAAMoE,QAAQpD,EAAKqD,WAA2B,eAAdrD,EAAKgD,OACvChD,EAAKqD,SAAWT,EAAuB5C,EAAKqD,SAAUP,IAEjD9C,CACT,EACF,CC5BO,IAAMsD,EAAwB,IAAIzC,EAC5B0C,ED6BW,SACtB5F,GAAA,IAKI6F,EACAC,EACAC,EANFC,EAEyB9L,EAFzBiJ,QAAAA,OAAO,IAAA6C,EAAG9L,EAAsB8L,EAChCC,EACyB/L,EAD8BgM,QAAvDA,OAAO,IAAAD,EAAGlM,EAA6CkM,EAOnDE,EAAwB,SAAC/F,EAAegG,EAAgBC,GAC5D,OAKEA,EAAOC,WAAWR,IAClBO,EAAOE,SAAST,IAChBO,EAAOf,WAAWQ,EAAW,IAAInL,OAAS,EAEnC,IAAAa,OAAIqK,GAGNzF,CACT,EAuBMoG,EAAcN,EAAQO,QAE5BD,EAAYxL,KAX8C,SAAA+G,GACpDA,EAAQsD,OAASqB,EAAOC,SAAW5E,EAAQhG,MAAM6K,SAAS,OAC3D7E,EAAQwD,MAAmB,GAAKxD,EAAQwD,MAAM,GAE5CpK,QAAQ4J,EAAWe,GACnB3K,QAAQ4K,EAAiBI,GAEhC,GASIhD,EAAQ0D,QACVL,EAAYxL,KAAK0L,EAAOI,UAG1BN,EAAYxL,KAAK0L,EAAOK,WAExB,IAAMC,EAA8B,SAClCzI,EACAoF,EAIAkD,EACAI,QALA,IAAAtD,IAAAA,EAAa,SAIb,IAAAkD,IAAAA,EAAW,SACX,IAAAI,IAAAA,EAAiB,KAKjBpB,EAAeoB,EACfnB,EAAYnC,EACZoC,EAAkB,IAAI1G,OAAO,KAAA7D,OAAKsK,EAAc,OAAE,KAElD,IAAMoB,EAAU3I,EAAIpD,QAAQ6J,EAAe,IACvCE,EAAWwB,EAAOS,QACpBN,GAAUlD,EAAW,UAAGkD,EAAM,KAAArL,OAAImI,EAAQ,OAAAnI,OAAM0L,EAAO,MAAOA,GAG5D/D,EAAQgC,YACVD,EAAWD,EAAuBC,EAAU/B,EAAQgC,YAGtD,IAAMiC,EAAkB,GAOxB,OALAV,EAAOW,UACLnC,EACAwB,EAAOY,WAAWd,EAAYhL,OAAOkL,EAAOa,UAAU,SAAAxL,GAAS,OAAAqL,EAAMpM,KAAKe,EAAM,MAG3EqL,CACT,EAcA,OAZAJ,EAAeQ,KAAOtB,EAAQvL,OAC1BuL,EACGuB,OAAO,SAACC,EAAKC,GAKZ,OAJKA,EAAOlI,MACVmI,EAAiB,IAGZjD,EAAM+C,EAAKC,EAAOlI,KAC1B,ED5IW,MC6IXoI,WACH,GAEGb,CACT,CCzIuCc,GCFvCC,GDUiCC,EAAKC,QAACC,cAAkC,CACvEC,uBAAmB1G,EACnB2G,WAAYzC,EACZe,OAAQd,IAMmBoC,EAAKC,QAACC,mBAA8BzG,GCnBjE,WAKE,SAAYsG,EAAAtI,EAAcjC,GAA1B,IAQC6F,EAAAtG,KAEDA,KAAAsL,OAAS,SAACD,EAAwBE,QAAA,IAAAA,IAAAA,EAAwC1C,GACxE,IAAM2C,EAAelF,EAAK5D,KAAO6I,EAAed,KAE3CY,EAAWjE,aAAad,EAAKtE,GAAIwJ,IACpCH,EAAW7K,YACT8F,EAAKtE,GACLwJ,EACAD,EAAejF,EAAK7F,MAAO+K,EAAc,cAG/C,EAnBExL,KAAK0C,KAAOA,EACZ1C,KAAKgC,GAAK,gBAAgBvD,OAAAiE,GAC1B1C,KAAKS,MAAQA,EAEb7B,EAAYoB,KAAM,WAChB,MAAMa,EAAY,GAAI4K,OAAOnF,EAAK5D,MACpC,EACD,CAiBH,OAHEsI,EAAO7K,UAAAuL,QAAP,SAAQH,GACN,YADM,IAAAA,IAAAA,EAAwC1C,GACvC7I,KAAK0C,KAAO6I,EAAed,MAErCO,CAAD,KClCwB,SAAAW,EAAiB3H,GACvC,MAC4B,eAAzB3G,QAAQC,IAAIC,UAA8C,iBAAXyG,GAAuBA,GACtEA,EAA8C4H,aAC9C5H,EAAoBtB,MACrB,WAEJ,CCTA,IAAMmJ,EAAU,SAAC9N,GAAc,OAAAA,GAAK,KAAOA,GAAK,KAexB,SAAA+N,EAAmBxC,GAGzC,IAFA,IAAIyC,EAAS,GAEJxL,EAAI,EAAGA,EAAI+I,EAAO1L,OAAQ2C,IAAK,CACtC,IAAMxC,EAAIuL,EAAO/I,GAEjB,GAAU,IAANA,GAAiB,MAANxC,GAA2B,MAAduL,EAAO,GACjC,OAAOA,EAGLuC,EAAQ9N,GACVgO,GAAU,IAAMhO,EAAEiO,cAElBD,GAAUhO,CAEb,CAED,OAAOgO,EAAOxC,WAAW,OAAS,IAAMwC,EAASA,CACnD,CCjCwB,SAAAE,GAAWC,GACjC,MAAuB,mBAATA,CAChB,CCFwB,SAAAC,GAAcrE,GACpC,OACQ,OAANA,GACa,iBAANA,GACPA,EAAEsE,YAAY1J,OAASzF,OAAOyF,QAE5B,UAAWoF,GAAKA,EAAEuE,SAExB,CCNwB,SAAAC,GAAkBtI,GACxC,MAAyB,iBAAXA,GAAuB,sBAAuBA,CAC9D,CCoBA,IAAMuI,GAAY,SAACC,GACjB,OAAAA,UAAmD,IAAVA,GAA6B,KAAVA,CAA5D,EAEWC,GAAgB,SAACC,GAC5B,ICzBsChK,EAAc1D,EDyB9CyB,EAAQ,GAEd,IAAK,IAAMkM,KAAOD,EAAK,CACrB,IAAME,EAAMF,EAAIC,GACXD,EAAIG,eAAeF,KAAQJ,GAAUK,KAGrCtI,MAAMoE,QAAQkE,IAAQA,EAAIE,OAAUb,GAAWW,GAClDnM,EAAMxC,KAAK,GAAAQ,OAAGsO,EAAUJ,GAAI,KAAKC,EAAK,KAC7BT,GAAcS,GACvBnM,EAAMxC,KAAN+O,MAAAvM,mCAAW,GAAGhC,OAAAkO,EAAO,OAAKF,GAAcG,IAAI,GAAA,CAAE,MAAK,IAEnDnM,EAAMxC,KAAK,GAAGQ,OAAAsO,EAAUJ,GAAS,MAAAlO,QCrCCiE,EDqCeiK,ECnCxC,OAFuC3N,EDqCM4N,ICnCpB,kBAAV5N,GAAiC,KAAVA,EAC1C,GAGY,iBAAVA,GAAgC,IAAVA,GAAiB0D,KAAQuK,EAAAA,SAAcvK,EAAK6G,WAAW,MAIjFkC,OAAOzM,GAAOL,OAHZ,GAAGF,OAAAO,EAAS,OD8ByC,MAE7D,CAED,OAAOyB,CACT,EAEc,SAAUyM,GACtBV,EACAW,EACA9B,EACAE,GAEA,GAAIgB,GAAUC,GACZ,MAAO,GAIT,GAAIF,GAAkBE,GACpB,MAAO,CAAC,IAAK/N,OAAA+N,EAAkDY,oBAIjE,GAAInB,GAAWO,GAAQ,CACrB,IE7DKP,GADmCC,EF8DhBM,IE7DGN,EAAK/L,WAAa+L,EAAK/L,UAAUkN,mBF6D1BF,EAoBhC,MAAO,CAACX,GAnBR,IAAMc,EAASd,EAAMW,GAiBrB,MAd2B,eAAzB9P,QAAQC,IAAIC,UACM,iBAAX+P,GACNhJ,MAAMoE,QAAQ4E,IACbA,aAAkBtC,GACnBmB,GAAcmB,IACJ,OAAXA,GAEAC,QAAQC,MACN,GAAG/O,OAAAkN,EACDa,GACiL,qLAIhLU,GAAeI,EAAQH,EAAkB9B,EAAYE,EAI/D,CEpFqB,IAAoBW,EFsF1C,OAAIM,aAAiBxB,EACfK,GACFmB,EAAMlB,OAAOD,EAAYE,GAClB,CAACiB,EAAMd,QAAQH,KAEf,CAACiB,GAKRL,GAAcK,GACTC,GAAcD,GAGlBlI,MAAMoE,QAAQ8D,GAUZlI,MAAMnE,UAAU1B,OAAOuO,MAAMhQ,EANrBwP,EAMwCnE,IANjC,SAAAoF,GACpB,OAAAP,GAAeO,EAAUN,EAAkB9B,EAAYE,EAAvD,IAJO,CAACiB,EAAM1B,WAMlB,CG3Gc,SAAU4C,GACtBC,EACApP,GAIA,IAFA,IAAM+O,EAAiC,CAACK,EAAQ,IAEvCpN,EAAI,EAAGvC,EAAMO,EAAeX,OAAQ2C,EAAIvC,EAAKuC,GAAK,EACzD+M,EAAOrP,KAAKM,EAAegC,GAAIoN,EAAQpN,EAAI,IAG7C,OAAO+M,CACT,CCMA,IAAMM,GAAS,SAAyBC,GACtC,OAAA5Q,OAAO6Q,OAAOD,EAAK,CAAEf,OAAO,GAA5B,EAOF,SAAStL,GACPuM,OACA,IAAkDxP,EAAA,GAAAb,EAAA,EAAlDA,EAAkDC,UAAAC,OAAlDF,IAAAa,EAAkDb,EAAA,GAAAC,UAAAD,GAElD,GAAIuO,GAAW8B,IAAW5B,GAAc4B,GAGtC,OAAOH,GACLV,GACEQ,GAAkB1Q,EAAWgR,EAAAA,cAAA,CAJHD,GAMrBxP,GAAc,MAMzB,IAAM0P,EAAmBF,EAEzB,OAC4B,IAA1BxP,EAAeX,QACa,IAA5BqQ,EAAiBrQ,QACc,iBAAxBqQ,EAAiB,GAEjBf,GAAee,GAGjBL,GACLV,GAAeQ,GAAkBO,EAAkB1P,IAEvD,CC0BwB,SAAA2P,GAQtBC,EACApO,EACAqG,GASA,QATA,IAAAA,IAAAA,EAAoDjJ,IAS/C4C,EACH,MAAMc,EAAY,EAAGd,GAIvB,IAAMqO,EAAmB,SACvBC,OACA,IAAiE9P,EAAA,GAAAb,EAAA,EAAjEA,EAAiEC,UAAAC,OAAjEF,IAAAa,EAAiEb,EAAA,GAAAC,UAAAD,GAEjE,OAAAyQ,EACEpO,EACAqG,EACA5E,GAAmCwL,WAAA,EAAAgB,EAAAA,cAAA,CAAAK,GAAkB9P,GACtD,IAJD,EA6CF,OAjCA6P,EAAiBE,MAAQ,SAMvBA,GAEA,OAAAJ,GAUEC,EAAsBpO,EACnBwG,EAAAA,SAAAA,EAAAA,SAAA,CAAA,EAAAH,GACH,CAAAkI,MAAOhK,MAAMnE,UAAU1B,OAAO2H,EAAQkI,MAAOA,GAAOC,OAAO5O,WAZ7D,EAmBFyO,EAAiBI,WAAa,SAACC,GAC7B,OAAAP,GAA0DC,EAAsBpO,EAC3EwG,EAAAA,SAAAA,WAAA,CAAA,EAAAH,GACAqI,GAFL,EAKKL,CACT,CCrHa,OAAAM,GAAezD,EAAAA,QAAME,mBAAwCzG,GAE7DiK,GAAgBD,GAAaE,SCvClB,SAAAC,GACtBrG,EACAsG,EACAC,GAEA,YAFA,IAAAA,IAAAA,EAAiE5R,GAEzDqL,EAAMwG,QAAUD,EAAaC,OAASxG,EAAMwG,OAAUF,GAAiBC,EAAaC,KAC9F,CCNA,IAAMC,GAA8B,mBAAXC,QAAyBA,OAAOC,IAGnDC,GAAkBH,GAAYC,OAAOC,IAAI,cAAgB,MACzDE,GAAyBJ,GAAYC,OAAOC,IAAI,qBAAuB,MAKvEG,GAAgB,CACpBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdV,cAAc,EACdnD,aAAa,EACb8D,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,QAAQ,EACRC,WAAW,EACXxH,MAAM,GAGFyH,GAAgB,CACpBrN,MAAM,EACN9E,QAAQ,EACRuC,WAAW,EACX6P,QAAQ,EACRC,QAAQ,EACRtS,WAAW,EACXuS,OAAO,GAWHC,GAAe,CACnB9D,UAAU,EACV+D,SAAS,EACTrB,cAAc,EACdnD,aAAa,EACbkE,WAAW,EACXxH,MAAM,GAGF+H,KAAYpN,GAAA,CAAA,GACfoM,IAlByB,CAC1BhD,UAAU,EACViE,QAAQ,EACRvB,cAAc,EACdnD,aAAa,EACbkE,WAAW,GAcX7M,GAACmM,IAAkBe,OAcrB,SAASI,GAAWC,GAElB,OAPqB,SAFrB3R,EASW2R,IAP8B3R,EAAOyJ,KAAK+D,YAE7B+C,GAMfe,GAIF,aAAcK,EACjBH,GAAaG,EAAoB,UACjClB,GAjBN,IACEzQ,CAiBF,CAEA,IAAME,GAAiB9B,OAAO8B,eACxB0R,GAAsBxT,OAAOwT,oBAC7BC,GAAwBzT,OAAOyT,sBAC/BC,GAA2B1T,OAAO0T,yBAClCC,GAAiB3T,OAAO2T,eACxBC,GAAkB5T,OAAOkD,UAiBP,SAAA2Q,GAItBC,EAAoBC,EAAoBC,GACxC,GAA+B,iBAApBD,EAA8B,CAGvC,GAAIH,GAAiB,CACnB,IAAMK,EAAqBN,GAAeI,GACtCE,GAAsBA,IAAuBL,IAC/CC,GAAqBC,EAAiBG,EAAoBD,EAE7D,CAED,IAAIE,EAA4BV,GAAoBO,GAEhDN,KACFS,EAAOA,EAAK1S,OAAOiS,GAAsBM,KAM3C,IAHA,IAAMI,EAAgBb,GAAWQ,GAC3BM,EAAgBd,GAAWS,GAExBzQ,EAAI,EAAGA,EAAI4Q,EAAKvT,SAAU2C,EAAG,CACpC,IAAMoM,EAAMwE,EAAK5Q,GACjB,KACIoM,KAAOoD,IACPkB,GAAeA,EAAYtE,IAC3B0E,GAAiB1E,KAAO0E,GACxBD,GAAiBzE,KAAOyE,GAC1B,CACA,IAAME,EAAaX,GAAyBK,EAAiBrE,GAE7D,IAEE5N,GAAegS,EAAiBpE,EAAK2E,EACtC,CAAC,MAAOC,GAER,CACF,CACF,CACF,CAED,OAAOR,CACT,CCpJA,IAAMS,GAAgB,WAOhBC,GAAoB,SAACnT,GAAiB,OAAAmN,OAAOiG,aAAapT,GAAQA,EAAO,GAAK,GAAK,IAA7C,ECA5B,SAAAqT,GAAgBtN,EAAeuN,GAC7C,GAAmB,IAAfvN,EAAIzG,OACN,MAAO,GAIT,IADA,IAAI0P,EAASjJ,EAAI,GACR9D,EAAI,EAAGA,EAAI8D,EAAIzG,OAAQ2C,IAC9B+M,GAAUsE,EAAMA,EAAMvN,EAAI9D,GAAK8D,EAAI9D,GAErC,OAAO+M,CACT,CCFO,IAAMuE,GAAwB,CAAC,cAAe,cAAe,eAEhEC,GAAuB,CAAA,ECf3B,SAASC,GAAiB/N,EAAagO,EAAaC,GAGlD,QAHkD,IAAAA,IAAAA,GAAkB,IAG/DA,IAAe9F,GAAcnI,KAAYM,MAAMoE,QAAQ1E,GAC1D,OAAOgO,EAGT,GAAI1N,MAAMoE,QAAQsJ,GAChB,IAAK,IAAIrF,EAAM,EAAGA,EAAMqF,EAAOpU,OAAQ+O,IACrC3I,EAAO2I,GAAOoF,GAAiB/N,EAAO2I,GAAMqF,EAAOrF,SAEhD,GAAIR,GAAc6F,GACvB,IAAK,IAAMrF,KAAOqF,EAChBhO,EAAO2I,GAAOoF,GAAiB/N,EAAO2I,GAAMqF,EAAOrF,IAIvD,OAAO3I,CACT,CC4FA,IFtFmEqH,GEsFnD6G,GClGVC,GAAcC,QAAQ,gBAGtBC,IHS6DhH,GGVlC8G,GAAYhM,WDgG7B+L,GFrFG,WAKf,SAAAA,EAAYzR,GACVT,KAAKS,MAAQA,CACd,CA4CH,OA1CEyR,EAAmB/R,UAAAmS,oBAAnB,SAAoBnF,GAElB,IAAMhD,EAAUwH,GACdzE,GAAQlN,KAAKS,MAA0B0M,IAEnC1C,EF/BY,SAAuBnM,GAC7C,IACIwJ,EADApF,EAAO,GAIX,IAAKoF,EAAIyK,KAAKC,IAAIlU,GAAOwJ,EAXP,GAWwBA,EAAKA,EAX7B,GAWgD,EAChEpF,EAAO+O,GAAkB3J,EAZT,IAY4BpF,EAG9C,OAAQ+O,GAAkB3J,EAfR,IAe2BpF,GAAMtE,QAAQoT,GAAe,QAC5E,CMhBSiB,CxBaA7K,EAjBW,KoByCmBuC,KIrCO,GJuCxC,IAAK2H,GAAUrH,GAAO,CACpB,IAAMiI,EAAOC,QAAMxI,GACbyI,EAAgC,GAEtCF,EAAKG,KAAK,SAAAlP,GACR,GAAkB,SAAdA,EAAK2E,KAAiB,CACxB,GAAIuJ,GAAsBhI,SAASlG,EAAK3E,OAMtC,YAL6B,eAAzB3B,QAAQC,IAAIC,UACdgQ,QAAQuF,KACN,yCAAArU,OAAyCkF,EAAK3E,MAAK,oBAAAP,OAAmBkF,EAAK8E,KAAI,6DAKrFmK,EAAU3U,KAAK,CAAC0F,EAAK8E,KAAM9E,EAAK3E,OACjC,KAAmC,eAAzB3B,QAAQC,IAAIC,UAA2C,YAAdoG,EAAK2E,MACvDiF,QAAQuF,KAAK,gBAAArU,OAAgBkF,EAAK2E,KAAuC,qCAE7E,GAOA,IAAMyK,EAAcC,EAAAA,QAAmBJ,EAAW,CAAC,cAAe,gBAE5D7E,EAAS1C,GAAW4H,OAAO,CAC/BnB,UAAWiB,IAGbjB,GAAUrH,GAAQsD,EAAO+D,SAC1B,CACD,OAAOA,GAAUrH,IAEpByH,CAnDmD,CAAnC,GEsFmB,SAKlClO,EACAoC,EACA3F,GAEA,IAAMyS,EAAqB5G,GAAkBtI,GACvCmP,EAAwBnP,EAEtBf,EAAmEmD,EAA1BwF,YAAzCA,OAAc,IAAA3I,EGzHF,SAAoBe,GAC1C,OCHsB,SAAMA,GAC5B,MACoB,iBAAXA,IACmB,eAAzB3G,QAAQC,IAAIC,UACTyG,EAAOoP,OAAO,KAAOpP,EAAOoP,OAAO,GAAGpH,cAG9C,CDJSqH,CAAMrP,GAAU,UAAUvF,OAAAuF,GAAW,UAAUvF,OAAAkN,EAAiB3H,OACzE,CHuH0BsP,CAAoBtP,KAASuP,EAAwBnN,EAALkI,MAAnBA,OAAQ,IAAAiF,EAAAvW,IAGrDwW,EACJN,GAAsBC,EAAsB7E,MACxC6E,EAAsB7E,MAAM7P,OAAO6P,GAAOC,OAAO5O,SAChD2O,EAEHlD,EAAoBhF,EAAQgF,kBAEhC,GAAI8H,GAAsBC,EAAsB/H,kBAAmB,CACjE,IAAMqI,EAAsBN,EAAsB/H,kBAElD,GAAIhF,EAAQgF,kBAAmB,CAC7B,IAAMsI,EAA4BtN,EAAQgF,kBAG1CA,EAAoB,SAAC3C,EAAMkL,GACzB,OAAAF,EAAoBhL,EAAMkL,IAC1BD,EAA0BjL,EAAMkL,EADhC,CAEH,MACCvI,EAAoBqI,CAEvB,CAED,IAAMG,EAAmB,SAACpL,EAAoCqL,GAC5D,OAlGN,SACEC,EACAtL,EACAuL,GAGE,IAAOC,EAKLF,EALmBxF,MACrB2F,EAIEH,EAJSG,YACXlF,EAGE+E,EAHU/E,aACZ3D,EAEE0I,EAAkB1I,kBADpBpH,EACE8P,EAAkB9P,OAEhBkQ,EAAejJ,EAAAA,QAAMkJ,WAAWzF,IAOhCzL,EAhDR,SACE+L,EACAxG,EACA8F,QAFA,IAAAU,IAAAA,EAAkC7R,GAOlC,IAAMiX,EAAyC7N,WAAAA,EAAAA,SAAA,CAAA,EAAAiC,IAAOwG,MAAKA,IACrDqF,EAA2B,CAAA,EAYjC,OAVA/F,EAAMpQ,QAAQ,SAAAoW,GACZ,IACI3H,EADA4H,EAAkBtI,GAAWqI,GAAWA,EAAQF,GAAWE,EAG/D,IAAK3H,KAAO4H,EAEVH,EAAQzH,GAAO0H,EAAc1H,GAAO4H,EAAgB5H,EAExD,GAEO,CAACyH,EAASC,EACnB,CA0B2BG,CAFX3F,GAAerG,EAAO0L,EAAcnF,IAEQ5R,EAAcqL,EAAOwL,GAA/D1F,OAEVmG,EAAkBR,EAAY3B,0BAE9BoC,EAAeX,EAEfJ,EAAmCrF,EAAMqG,IAAMnM,EAAMmM,IAAM3Q,EAE3D4Q,EAA2BtG,IAAU9F,EAAajC,WAAAA,EAAAA,SAAA,CAAA,EAAAiC,GAAU8F,GAAU9F,EACtEqM,EAA6B,CAAA,EAEnC,IAAK,IAAMlI,KAAOiI,EACD,MAAXjI,EAAI,IAAsB,OAARA,IACL,gBAARA,EACPkI,EAAgBF,GAAKC,EAAcjI,GACzBvB,IAAqBA,EAAkBuB,EAAKgH,KACtDkB,EAAgBlI,GAAOiI,EAAcjI,KAoBzC,OAhBAkI,EAAgB9R,MAAQ+R,EAAAA,QACtB,WACE,OAAA7I,GAAWzD,EAAMzF,OACb,SAACgS,GAAe,MAAA,CAACN,GAAiBhW,OAAO+J,EAAMzF,MAAMgS,GAAO,EAC5DvM,EAAMzF,MACJ,CAAC0R,GAAiBhW,OAAO+J,EAAMzF,OAC/B0R,CAAe,EACvB,CAACjM,EAAMzF,MAAO0R,IAKZV,IACFc,EAAgBhB,IAAMa,GAGjBvQ,EAAaA,cAACwP,EAAoBkB,EAC3C,CAyCMG,CAAmCC,EAAwBzM,EAAOqL,EAAlE,EAEFD,EAAiBhI,YAAcA,EAM/B,IAAIqJ,EAAyBhK,EAAAA,QAAMiK,WAAWtB,GAwC9C,OAlCAqB,EAAuB3G,MAAQkF,EAC/ByB,EAAuBhB,YAAc,IAAI/B,GACvCgB,EAAqBC,EAAsBc,YAAYxT,MAAMhC,OAAOgC,GAASA,GAE/EwU,EAAuBrJ,YAAcA,EACrCqJ,EAAuB7J,kBAAoBA,EAG3C6J,EAAuB7H,mBAAoB,EAG3C6H,EAAuBjR,OAASkP,EAAqBC,EAAsBnP,OAASA,EAEpF/G,OAAO8B,eAAekW,EAAwB,eAAgB,CAC5D/S,IAAG,WACD,OAAOlC,KAAKmV,mBACb,EAEDrU,aAAI4L,GACF1M,KAAKmV,oBAAsBjC,ED7JX,SAAUlP,OAAa,IAAiBoR,EAAA,GAAA1X,EAAA,EAAjBA,EAAiBC,UAAAC,OAAjBF,IAAA0X,EAAiB1X,EAAA,GAAAC,UAAAD,GAC9D,IAAqB,IAAAuF,EAAA,EAAAoS,EAAOD,EAAPnS,WAAAA,IACnB8O,GAAiB/N,EADFqR,EAAApS,IACkB,GAGnC,OAAOe,CACT,CCwJYsR,CAAM,CAAE,EAAEnC,EAAsBpE,aAAcrC,GAC9CA,CACL,IAGH6I,GAAoDN,EAAwBjR,EAAQ,CAElFsK,OAAO,EACP2F,aAAa,EACbrI,aAAa,EACbR,mBAAmB,EACnBpH,QAAQ,IAGHiR,CACT,GC5IIO,GAzCa,SAA8BzV,GAC/C,OAAAmO,GAAuCmE,GAAuBtS,EAA9D,EAIc,CACd,oBACA,SACA,gBACA,sBACA,WACA,QACA,kBACA,uBACA,QACA,YACA,qBACA,kBACA,iBACA,eACA,aACA,cACA,SACA,SACA,OACA,YACA,qBACA,mBACA,OACA,mBAkBM7B,QAAQ,SAAAuX,GACd,OAAAxY,OAAO8B,eAAeyW,GAAQC,EAAO,CACnCC,YAAY,EACZC,cAAc,EACdzT,IAAG,WACD,GAAIuT,KAAStD,IAAeA,GAAYsD,GACtC,OAAOD,GAAOrD,GAAYsD,IAG5B,MAAM,IAAIjX,MACR,UAAGiX,EAAK,wEAEX,GAXH,0ERuBsB,SAAcjN,GACpC,IAAMoN,EAAa3K,EAAAA,QAAMkJ,WAAWzF,IAC9BmH,EAAef,EAAOA,QAC1B,WAAM,OAjDV,SAAoB9F,EAAsB4G,GACxC,IAAK5G,EACH,MAAMnO,EAAY,IAGpB,GAAIoL,GAAW+C,GAAQ,CACrB,IACM8G,EADU9G,EACY4G,GAE5B,GAC2B,eAAzBvY,QAAQC,IAAIC,WACK,OAAhBuY,GAAwBxR,MAAMoE,QAAQoN,IAAuC,iBAAhBA,GAE9D,MAAMjV,EAAY,GAGpB,OAAOiV,CACR,CAED,GAAIxR,MAAMoE,QAAQsG,IAA2B,iBAAVA,EACjC,MAAMnO,EAAY,GAGpB,OAAO+U,EAAkBrP,EAAAA,SAAAA,WAAA,CAAA,EAAAqP,GAAe5G,GAAUA,CACpD,CAyBU+G,CAAWvN,EAAMwG,MAAO4G,EAAW,EACzC,CAACpN,EAAMwG,MAAO4G,IAGhB,OAAKpN,EAAMG,SAIJsC,EAACC,QAAA/G,cAAAuK,GAAasH,SAAS,CAAAhX,MAAO6W,GAAerN,EAAMG,UAHjD,IAIX,wGQpBqB,SAAClI,GACpB,IAAM0J,EAAUwH,GAAgBzE,GAAQzM,IAElCiS,EAAOC,QAAMxI,GACb8L,EAAgC,GAEtCvD,EAAKG,KAAK,SAAAlP,GACU,SAAdA,EAAK2E,KACP2N,EAAUhY,KAAK,CAAC0F,EAAK8E,KAAM9E,EAAK3E,QACE,eAAzB3B,QAAQC,IAAIC,UAA2C,YAAdoG,EAAK2E,MACvDiF,QAAQuF,KAAK,gBAAArU,OAAgBkF,EAAK2E,KAAuC,qCAE7E,GAEA,IAAMyK,EAAcC,EAAAA,QAAmBiD,EAAW,CAAC,cAAe,gBAElE,OAAO9D,GAAYhM,WAAW8M,OAAO,CAAElQ,MAAOgQ,IAAehQ,KAC/D,8BRrBE,IAAMiM,EAAQmF,aAAWzF,IAEzB,IAAKM,EACH,MAAMnO,EAAY,IAGpB,OAAOmO,CACT,oBY9EwB,SACtBkH,GAMA,IAAMC,EAAYlL,EAAKC,QAACgK,WACtB,SAAC1M,EAAOqL,GACN,IACMuC,EAAYvH,GAAerG,EADnByC,EAAAA,QAAMkJ,WAAWzF,IACgBwH,EAAUnH,cAUzD,MAR6B,eAAzB1R,QAAQC,IAAIC,eAA2CmH,IAAd0R,GAC3C7I,QAAQuF,KACN,yHAAyHrU,OAAAkN,EACvHuK,GACE,MAIDjL,EAACC,QAAA/G,cAAA+R,EAAc3P,EAAAA,SAAA,CAAA,EAAAiC,EAAO,CAAAwG,MAAOoH,EAAWvC,IAAKA,IACtD,GAKF,OAFAsC,EAAUvK,YAAc,aAAAnN,OAAakN,EAAiBuK,GAAU,KAEzDX,GAAMY,EAAWD,EAC1B"}