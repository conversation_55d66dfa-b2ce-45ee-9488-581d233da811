{"ast": null, "code": "var scope = typeof global !== \"undefined\" && global || typeof self !== \"undefined\" && self || window;\nvar apply = Function.prototype.apply;\n\n// DOM APIs, for completeness\n\nexports.setTimeout = function () {\n  return new Timeout(apply.call(setTimeout, scope, arguments), clearTimeout);\n};\nexports.setInterval = function () {\n  return new Timeout(apply.call(setInterval, scope, arguments), clearInterval);\n};\nexports.clearTimeout = exports.clearInterval = function (timeout) {\n  if (timeout) {\n    timeout.close();\n  }\n};\nfunction Timeout(id, clearFn) {\n  this._id = id;\n  this._clearFn = clearFn;\n}\nTimeout.prototype.unref = Timeout.prototype.ref = function () {};\nTimeout.prototype.close = function () {\n  this._clearFn.call(scope, this._id);\n};\n\n// Does not start the time, just sets up the members needed.\nexports.enroll = function (item, msecs) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = msecs;\n};\nexports.unenroll = function (item) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = -1;\n};\nexports._unrefActive = exports.active = function (item) {\n  clearTimeout(item._idleTimeoutId);\n  var msecs = item._idleTimeout;\n  if (msecs >= 0) {\n    item._idleTimeoutId = setTimeout(function onTimeout() {\n      if (item._onTimeout) item._onTimeout();\n    }, msecs);\n  }\n};\n\n// setimmediate attaches itself to the global object\nrequire(\"setimmediate\");\n// On some exotic environments, it's not clear which object `setimmediate` was\n// able to install onto.  Search each possibility in the same order as the\n// `setimmediate` library.\nexports.setImmediate = typeof self !== \"undefined\" && self.setImmediate || typeof global !== \"undefined\" && global.setImmediate || this && this.setImmediate;\nexports.clearImmediate = typeof self !== \"undefined\" && self.clearImmediate || typeof global !== \"undefined\" && global.clearImmediate || this && this.clearImmediate;", "map": {"version": 3, "names": ["scope", "global", "self", "window", "apply", "Function", "prototype", "exports", "setTimeout", "Timeout", "call", "arguments", "clearTimeout", "setInterval", "clearInterval", "timeout", "close", "id", "clearFn", "_id", "_clearFn", "unref", "ref", "enroll", "item", "msecs", "_idleTimeoutId", "_idleTimeout", "unenroll", "_unrefActive", "active", "onTimeout", "_onTimeout", "require", "setImmediate", "clearImmediate"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/timers-browserify/main.js"], "sourcesContent": ["var scope = (typeof global !== \"undefined\" && global) ||\n            (typeof self !== \"undefined\" && self) ||\n            window;\nvar apply = Function.prototype.apply;\n\n// DOM APIs, for completeness\n\nexports.setTimeout = function() {\n  return new Timeout(apply.call(setTimeout, scope, arguments), clearTimeout);\n};\nexports.setInterval = function() {\n  return new Timeout(apply.call(setInterval, scope, arguments), clearInterval);\n};\nexports.clearTimeout =\nexports.clearInterval = function(timeout) {\n  if (timeout) {\n    timeout.close();\n  }\n};\n\nfunction Timeout(id, clearFn) {\n  this._id = id;\n  this._clearFn = clearFn;\n}\nTimeout.prototype.unref = Timeout.prototype.ref = function() {};\nTimeout.prototype.close = function() {\n  this._clearFn.call(scope, this._id);\n};\n\n// Does not start the time, just sets up the members needed.\nexports.enroll = function(item, msecs) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = msecs;\n};\n\nexports.unenroll = function(item) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = -1;\n};\n\nexports._unrefActive = exports.active = function(item) {\n  clearTimeout(item._idleTimeoutId);\n\n  var msecs = item._idleTimeout;\n  if (msecs >= 0) {\n    item._idleTimeoutId = setTimeout(function onTimeout() {\n      if (item._onTimeout)\n        item._onTimeout();\n    }, msecs);\n  }\n};\n\n// setimmediate attaches itself to the global object\nrequire(\"setimmediate\");\n// On some exotic environments, it's not clear which object `setimmediate` was\n// able to install onto.  Search each possibility in the same order as the\n// `setimmediate` library.\nexports.setImmediate = (typeof self !== \"undefined\" && self.setImmediate) ||\n                       (typeof global !== \"undefined\" && global.setImmediate) ||\n                       (this && this.setImmediate);\nexports.clearImmediate = (typeof self !== \"undefined\" && self.clearImmediate) ||\n                         (typeof global !== \"undefined\" && global.clearImmediate) ||\n                         (this && this.clearImmediate);\n"], "mappings": "AAAA,IAAIA,KAAK,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IACvC,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAK,IACrCC,MAAM;AAClB,IAAIC,KAAK,GAAGC,QAAQ,CAACC,SAAS,CAACF,KAAK;;AAEpC;;AAEAG,OAAO,CAACC,UAAU,GAAG,YAAW;EAC9B,OAAO,IAAIC,OAAO,CAACL,KAAK,CAACM,IAAI,CAACF,UAAU,EAAER,KAAK,EAAEW,SAAS,CAAC,EAAEC,YAAY,CAAC;AAC5E,CAAC;AACDL,OAAO,CAACM,WAAW,GAAG,YAAW;EAC/B,OAAO,IAAIJ,OAAO,CAACL,KAAK,CAACM,IAAI,CAACG,WAAW,EAAEb,KAAK,EAAEW,SAAS,CAAC,EAAEG,aAAa,CAAC;AAC9E,CAAC;AACDP,OAAO,CAACK,YAAY,GACpBL,OAAO,CAACO,aAAa,GAAG,UAASC,OAAO,EAAE;EACxC,IAAIA,OAAO,EAAE;IACXA,OAAO,CAACC,KAAK,CAAC,CAAC;EACjB;AACF,CAAC;AAED,SAASP,OAAOA,CAACQ,EAAE,EAAEC,OAAO,EAAE;EAC5B,IAAI,CAACC,GAAG,GAAGF,EAAE;EACb,IAAI,CAACG,QAAQ,GAAGF,OAAO;AACzB;AACAT,OAAO,CAACH,SAAS,CAACe,KAAK,GAAGZ,OAAO,CAACH,SAAS,CAACgB,GAAG,GAAG,YAAW,CAAC,CAAC;AAC/Db,OAAO,CAACH,SAAS,CAACU,KAAK,GAAG,YAAW;EACnC,IAAI,CAACI,QAAQ,CAACV,IAAI,CAACV,KAAK,EAAE,IAAI,CAACmB,GAAG,CAAC;AACrC,CAAC;;AAED;AACAZ,OAAO,CAACgB,MAAM,GAAG,UAASC,IAAI,EAAEC,KAAK,EAAE;EACrCb,YAAY,CAACY,IAAI,CAACE,cAAc,CAAC;EACjCF,IAAI,CAACG,YAAY,GAAGF,KAAK;AAC3B,CAAC;AAEDlB,OAAO,CAACqB,QAAQ,GAAG,UAASJ,IAAI,EAAE;EAChCZ,YAAY,CAACY,IAAI,CAACE,cAAc,CAAC;EACjCF,IAAI,CAACG,YAAY,GAAG,CAAC,CAAC;AACxB,CAAC;AAEDpB,OAAO,CAACsB,YAAY,GAAGtB,OAAO,CAACuB,MAAM,GAAG,UAASN,IAAI,EAAE;EACrDZ,YAAY,CAACY,IAAI,CAACE,cAAc,CAAC;EAEjC,IAAID,KAAK,GAAGD,IAAI,CAACG,YAAY;EAC7B,IAAIF,KAAK,IAAI,CAAC,EAAE;IACdD,IAAI,CAACE,cAAc,GAAGlB,UAAU,CAAC,SAASuB,SAASA,CAAA,EAAG;MACpD,IAAIP,IAAI,CAACQ,UAAU,EACjBR,IAAI,CAACQ,UAAU,CAAC,CAAC;IACrB,CAAC,EAAEP,KAAK,CAAC;EACX;AACF,CAAC;;AAED;AACAQ,OAAO,CAAC,cAAc,CAAC;AACvB;AACA;AACA;AACA1B,OAAO,CAAC2B,YAAY,GAAI,OAAOhC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACgC,YAAY,IAChD,OAAOjC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACiC,YAAa,IACrD,IAAI,IAAI,IAAI,CAACA,YAAa;AAClD3B,OAAO,CAAC4B,cAAc,GAAI,OAAOjC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACiC,cAAc,IAClD,OAAOlC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACkC,cAAe,IACvD,IAAI,IAAI,IAAI,CAACA,cAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}