{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var assign,\n    getValue,\n    isArray,\n    isEmpty,\n    isFunction,\n    isObject,\n    isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n  assign = function () {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n  isFunction = function (val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n  isObject = function (val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n  isArray = function (val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n  isEmpty = function (val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n  isPlainObject = function (val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && typeof ctor === 'function' && ctor instanceof ctor && Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object);\n  };\n  getValue = function (obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n  module.exports.assign = assign;\n  module.exports.isFunction = isFunction;\n  module.exports.isObject = isObject;\n  module.exports.isArray = isArray;\n  module.exports.isEmpty = isEmpty;\n  module.exports.isPlainObject = isPlainObject;\n  module.exports.getValue = getValue;\n}).call(this);", "map": {"version": 3, "names": ["assign", "getValue", "isArray", "isEmpty", "isFunction", "isObject", "isPlainObject", "slice", "hasProp", "hasOwnProperty", "i", "key", "len", "source", "sources", "target", "arguments", "length", "call", "Object", "apply", "val", "prototype", "toString", "ref", "Array", "ctor", "proto", "getPrototypeOf", "constructor", "Function", "obj", "valueOf", "module", "exports"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/node_modules/xmlbuilder/lib/Utility.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,OAAO;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,aAAa;IACzEC,KAAK,GAAG,EAAE,CAACA,KAAK;IAChBC,OAAO,GAAG,CAAC,CAAC,CAACC,cAAc;EAE7BT,MAAM,GAAG,SAAAA,CAAA,EAAW;IAClB,IAAIU,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM;IACxCA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC,EAAEF,OAAO,GAAG,CAAC,IAAIE,SAAS,CAACC,MAAM,GAAGV,KAAK,CAACW,IAAI,CAACF,SAAS,EAAE,CAAC,CAAC,GAAG,EAAE;IACtF,IAAIZ,UAAU,CAACe,MAAM,CAACnB,MAAM,CAAC,EAAE;MAC7BmB,MAAM,CAACnB,MAAM,CAACoB,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;IACtC,CAAC,MAAM;MACL,KAAKN,CAAC,GAAG,CAAC,EAAEE,GAAG,GAAGE,OAAO,CAACG,MAAM,EAAEP,CAAC,GAAGE,GAAG,EAAEF,CAAC,EAAE,EAAE;QAC9CG,MAAM,GAAGC,OAAO,CAACJ,CAAC,CAAC;QACnB,IAAIG,MAAM,IAAI,IAAI,EAAE;UAClB,KAAKF,GAAG,IAAIE,MAAM,EAAE;YAClB,IAAI,CAACL,OAAO,CAACU,IAAI,CAACL,MAAM,EAAEF,GAAG,CAAC,EAAE;YAChCI,MAAM,CAACJ,GAAG,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC;UAC3B;QACF;MACF;IACF;IACA,OAAOI,MAAM;EACf,CAAC;EAEDX,UAAU,GAAG,SAAAA,CAASiB,GAAG,EAAE;IACzB,OAAO,CAAC,CAACA,GAAG,IAAIF,MAAM,CAACG,SAAS,CAACC,QAAQ,CAACL,IAAI,CAACG,GAAG,CAAC,KAAK,mBAAmB;EAC7E,CAAC;EAEDhB,QAAQ,GAAG,SAAAA,CAASgB,GAAG,EAAE;IACvB,IAAIG,GAAG;IACP,OAAO,CAAC,CAACH,GAAG,KAAK,CAACG,GAAG,GAAG,OAAOH,GAAG,MAAM,UAAU,IAAIG,GAAG,KAAK,QAAQ,CAAC;EACzE,CAAC;EAEDtB,OAAO,GAAG,SAAAA,CAASmB,GAAG,EAAE;IACtB,IAAIjB,UAAU,CAACqB,KAAK,CAACvB,OAAO,CAAC,EAAE;MAC7B,OAAOuB,KAAK,CAACvB,OAAO,CAACmB,GAAG,CAAC;IAC3B,CAAC,MAAM;MACL,OAAOF,MAAM,CAACG,SAAS,CAACC,QAAQ,CAACL,IAAI,CAACG,GAAG,CAAC,KAAK,gBAAgB;IACjE;EACF,CAAC;EAEDlB,OAAO,GAAG,SAAAA,CAASkB,GAAG,EAAE;IACtB,IAAIV,GAAG;IACP,IAAIT,OAAO,CAACmB,GAAG,CAAC,EAAE;MAChB,OAAO,CAACA,GAAG,CAACJ,MAAM;IACpB,CAAC,MAAM;MACL,KAAKN,GAAG,IAAIU,GAAG,EAAE;QACf,IAAI,CAACb,OAAO,CAACU,IAAI,CAACG,GAAG,EAAEV,GAAG,CAAC,EAAE;QAC7B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb;EACF,CAAC;EAEDL,aAAa,GAAG,SAAAA,CAASe,GAAG,EAAE;IAC5B,IAAIK,IAAI,EAAEC,KAAK;IACf,OAAOtB,QAAQ,CAACgB,GAAG,CAAC,KAAKM,KAAK,GAAGR,MAAM,CAACS,cAAc,CAACP,GAAG,CAAC,CAAC,KAAKK,IAAI,GAAGC,KAAK,CAACE,WAAW,CAAC,IAAK,OAAOH,IAAI,KAAK,UAAW,IAAKA,IAAI,YAAYA,IAAK,IAAKI,QAAQ,CAACR,SAAS,CAACC,QAAQ,CAACL,IAAI,CAACQ,IAAI,CAAC,KAAKI,QAAQ,CAACR,SAAS,CAACC,QAAQ,CAACL,IAAI,CAACC,MAAM,CAAE;EAC/O,CAAC;EAEDlB,QAAQ,GAAG,SAAAA,CAAS8B,GAAG,EAAE;IACvB,IAAI3B,UAAU,CAAC2B,GAAG,CAACC,OAAO,CAAC,EAAE;MAC3B,OAAOD,GAAG,CAACC,OAAO,CAAC,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,GAAG;IACZ;EACF,CAAC;EAEDE,MAAM,CAACC,OAAO,CAAClC,MAAM,GAAGA,MAAM;EAE9BiC,MAAM,CAACC,OAAO,CAAC9B,UAAU,GAAGA,UAAU;EAEtC6B,MAAM,CAACC,OAAO,CAAC7B,QAAQ,GAAGA,QAAQ;EAElC4B,MAAM,CAACC,OAAO,CAAChC,OAAO,GAAGA,OAAO;EAEhC+B,MAAM,CAACC,OAAO,CAAC/B,OAAO,GAAGA,OAAO;EAEhC8B,MAAM,CAACC,OAAO,CAAC5B,aAAa,GAAGA,aAAa;EAE5C2B,MAAM,CAACC,OAAO,CAACjC,QAAQ,GAAGA,QAAQ;AAEpC,CAAC,EAAEiB,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}