import type { BaseObject, ExecutionProps, IInlineStyleConstructor, IStyledComponentFactory, NativeTarget, RuleSet, StyledOptions } from '../types';
declare const _default: (InlineStyle: IInlineStyleConstructor<any>) => <Target extends NativeTarget, OuterProps extends ExecutionProps, Statics extends object = BaseObject>(target: Target, options: StyledOptions<'native', OuterProps>, rules: RuleSet<OuterProps>) => ReturnType<IStyledComponentFactory<'native', Target, OuterProps, Statics>>;
export default _default;
