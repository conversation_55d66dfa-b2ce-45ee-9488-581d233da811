{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\brsr_reports\\\\brsr-viewer\\\\src\\\\BRSRViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport { parseStringPromise } from \"xml2js\";\nimport DataTable from \"react-data-table-component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BRSRViewer() {\n  _s();\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({\n    Environment: [],\n    Social: [],\n    Governance: []\n  });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = evt => {\n      const workbook = XLSX.read(evt.target.result, {\n        type: \"binary\"\n      });\n      const sheetName = workbook.SheetNames[0];\n      const worksheet = workbook.Sheets[sheetName];\n      const json = XLSX.utils.sheet_to_json(worksheet);\n      setCompanyData(json);\n    };\n    reader.readAsBinaryString(file);\n  };\n\n  /** Categorize field names **/\n  const categorizeField = name => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\")) return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\")) return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = async xmlText => {\n    const result = await parseStringPromise(xmlText, {\n      explicitArray: false\n    });\n    const xbrl = result[\"xbrli:xbrl\"];\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextArr = Array.isArray(xbrl[\"xbrli:context\"]) ? xbrl[\"xbrli:context\"] : [xbrl[\"xbrli:context\"]];\n    contextArr.forEach(ctx => {\n      contexts[ctx.$.id] = {\n        entity: ctx[\"xbrli:entity\"][\"xbrli:identifier\"]._,\n        startDate: ctx[\"xbrli:period\"][\"xbrli:startDate\"],\n        endDate: ctx[\"xbrli:period\"][\"xbrli:endDate\"]\n      };\n    });\n\n    // Parse units\n    if (xbrl[\"xbrli:unit\"]) {\n      const unitArr = Array.isArray(xbrl[\"xbrli:unit\"]) ? xbrl[\"xbrli:unit\"] : [xbrl[\"xbrli:unit\"]];\n      unitArr.forEach(u => {\n        units[u.$.id] = u[\"xbrli:measure\"];\n      });\n    }\n\n    // Group data into categories\n    const categories = {\n      Environment: [],\n      Social: [],\n      Governance: []\n    };\n    Object.keys(xbrl).forEach(tag => {\n      if (tag.startsWith(\"in-capmkt:\")) {\n        const element = xbrl[tag];\n        const elements = Array.isArray(element) ? element : [element];\n        elements.forEach(el => {\n          const context = contexts[el.$.contextRef];\n          const unit = el.$.unitRef ? units[el.$.unitRef] : null;\n          const category = categorizeField(tag);\n          categories[category].push({\n            metric: tag.replace(\"in-capmkt:\", \"\"),\n            value: el._,\n            unit,\n            period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n            company: context === null || context === void 0 ? void 0 : context.entity\n          });\n        });\n      }\n    });\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async row => {\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({\n      Environment: [],\n      Social: [],\n      Governance: []\n    });\n    if (row[\"XML Link\"]) {\n      try {\n        const res = await axios.get(row[\"XML Link\"]);\n        const categories = await parseBRSRXML(res.data);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n      } finally {\n        setLoadingSingle(false);\n      }\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = await parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach(cat => {\n            categories[cat].forEach(d => allData.push({\n              ...d,\n              category: cat,\n              companyName: row[\"Company Name\"],\n              year: row[\"Year\"]\n            }));\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(row => row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) || row.value.toLowerCase().includes(searchBRSR.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid py-4\",\n    style: {\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"display-4 fw-bold text-center mb-5 text-dark\",\n      children: \"\\uD83D\\uDCCA BRSR Report Viewer (Excel + XML)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \".xlsx,.xls\",\n          onChange: handleExcelUpload,\n          className: \"form-control shadow-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search company...\",\n          value: searchCompany,\n          onChange: e => setSearchCompany(e.target.value),\n          className: \"form-control shadow-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-2\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchAllCompanies,\n          disabled: loadingAll || companyData.length === 0,\n          className: `btn w-100 ${loadingAll ? \"btn-secondary\" : \"btn-success\"}`,\n          children: loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card shadow mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(DataTable, {\n          title: \"Companies\",\n          columns: [{\n            name: \"Company\",\n            selector: row => row[\"Company Name\"],\n            sortable: true\n          }, {\n            name: \"Year\",\n            selector: row => row[\"Year\"],\n            sortable: true\n          }, {\n            name: \"Actions\",\n            cell: row => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCompanySelect(row),\n              className: \"btn btn-primary btn-sm\",\n              children: \"View BRSR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this)\n          }],\n          data: companyData.filter(r => {\n            var _r$CompanyName;\n            return (_r$CompanyName = r[\"Company Name\"]) === null || _r$CompanyName === void 0 ? void 0 : _r$CompanyName.toLowerCase().includes(searchCompany.toLowerCase());\n          }),\n          pagination: true,\n          highlightOnHover: true,\n          dense: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), unifiedData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card shadow mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title h4 mb-4\",\n          children: \"Unified BRSR Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n          columns: [{\n            name: \"Company\",\n            selector: row => row.companyName,\n            sortable: true\n          }, {\n            name: \"Year\",\n            selector: row => row.year,\n            sortable: true\n          }, {\n            name: \"Category\",\n            selector: row => row.category,\n            sortable: true\n          }, {\n            name: \"Metric\",\n            selector: row => row.metric,\n            sortable: true\n          }, {\n            name: \"Value\",\n            selector: row => row.value,\n            sortable: true\n          }],\n          data: unifiedData,\n          pagination: true,\n          dense: true,\n          highlightOnHover: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this), selectedCompany && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title h4 mb-4\",\n          children: [selectedCompany[\"Company Name\"], \" (\", selectedCompany[\"Year\"], \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav nav-tabs mb-4\",\n          children: [\"Environment\", \"Social\", \"Governance\"].map(tab => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab),\n              className: `nav-link ${activeTab === tab ? \"active\" : \"\"}`,\n              style: {\n                backgroundColor: activeTab === tab ? tab === \"Environment\" ? \"#198754\" : tab === \"Social\" ? \"#6f42c1\" : \"#fd7e14\" : \"\",\n                color: activeTab === tab ? \"white\" : \"\",\n                border: \"none\"\n              },\n              children: tab\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this)\n          }, tab, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row justify-content-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: `Search in ${activeTab}...`,\n              value: searchBRSR,\n              onChange: e => setSearchBRSR(e.target.value),\n              className: \"form-control shadow-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), loadingSingle ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center py-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border text-primary\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(DataTable, {\n          columns: [{\n            name: \"Metric\",\n            selector: row => row.metric,\n            sortable: true\n          }, {\n            name: \"Value\",\n            selector: row => row.value,\n            sortable: true\n          }, {\n            name: \"Unit\",\n            selector: row => row.unit || \"-\",\n            sortable: true\n          }, {\n            name: \"Period\",\n            selector: row => row.period,\n            sortable: true\n          }],\n          data: filteredTabData,\n          pagination: true,\n          dense: true,\n          highlightOnHover: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n}\n_s(BRSRViewer, \"XqWeNAlGtEHiedqF0JxGSKVABwY=\");\n_c = BRSRViewer;\nvar _c;\n$RefreshReg$(_c, \"BRSRViewer\");", "map": {"version": 3, "names": ["React", "useState", "XLSX", "axios", "parseStringPromise", "DataTable", "jsxDEV", "_jsxDEV", "BRSRViewer", "_s", "companyData", "setCompanyData", "searchCompany", "setSearchCompany", "selectedCompany", "setSelectedCompany", "brsrData", "setBrsrData", "Environment", "Social", "Governance", "searchBRSR", "setSearchBRSR", "activeTab", "setActiveTab", "unifiedData", "setUnifiedData", "loadingAll", "setLoadingAll", "loadingSingle", "setLoading<PERSON>ingle", "handleExcelUpload", "e", "file", "target", "files", "reader", "FileReader", "onload", "evt", "workbook", "read", "result", "type", "sheetName", "SheetNames", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "readAsBinaryString", "categorizeField", "name", "lower", "toLowerCase", "includes", "parseBRSRXML", "xmlText", "explicitArray", "xbrl", "contexts", "units", "contextArr", "Array", "isArray", "for<PERSON>ach", "ctx", "$", "id", "entity", "_", "startDate", "endDate", "unitArr", "u", "categories", "Object", "keys", "tag", "startsWith", "element", "elements", "el", "context", "contextRef", "unit", "unitRef", "category", "push", "metric", "replace", "value", "period", "company", "handleCompanySelect", "row", "res", "get", "data", "err", "console", "error", "fetchAllCompanies", "allData", "cat", "d", "companyName", "year", "warn", "filteredTabData", "filter", "className", "style", "backgroundColor", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "accept", "onChange", "placeholder", "onClick", "disabled", "length", "title", "columns", "selector", "sortable", "cell", "r", "_r$CompanyName", "pagination", "highlightOnHover", "dense", "map", "tab", "color", "border", "role", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/brsr_reports/brsr-viewer/src/BRSRViewer.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport * as XLSX from \"xlsx\";\nimport axios from \"axios\";\nimport { parseStringPromise } from \"xml2js\";\nimport DataTable from \"react-data-table-component\";\n\nexport default function BRSRViewer() {\n  const [companyData, setCompanyData] = useState([]);\n  const [searchCompany, setSearchCompany] = useState(\"\");\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [brsrData, setBrsrData] = useState({ Environment: [], Social: [], Governance: [] });\n  const [searchBRSR, setSearchBRSR] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"Environment\");\n  const [unifiedData, setUnifiedData] = useState([]);\n  const [loadingAll, setLoadingAll] = useState(false);\n  const [loadingSingle, setLoadingSingle] = useState(false);\n\n  /** Read Excel **/\n  const handleExcelUpload = (e) => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = (evt) => {\n      const workbook = XLSX.read(evt.target.result, { type: \"binary\" });\n      const sheetName = workbook.SheetNames[0];\n      const worksheet = workbook.Sheets[sheetName];\n      const json = XLSX.utils.sheet_to_json(worksheet);\n      setCompanyData(json);\n    };\n    reader.readAsBinaryString(file);\n  };\n\n  /** Categorize field names **/\n  const categorizeField = (name) => {\n    const lower = name.toLowerCase();\n    if (lower.includes(\"energy\") || lower.includes(\"emission\") || lower.includes(\"water\") || lower.includes(\"waste\"))\n      return \"Environment\";\n    if (lower.includes(\"employee\") || lower.includes(\"social\") || lower.includes(\"community\") || lower.includes(\"csr\"))\n      return \"Social\";\n    return \"Governance\";\n  };\n\n  /** Parse XML into categories **/\n  const parseBRSRXML = async (xmlText) => {\n    const result = await parseStringPromise(xmlText, { explicitArray: false });\n    const xbrl = result[\"xbrli:xbrl\"];\n    const contexts = {};\n    const units = {};\n\n    // Parse contexts\n    const contextArr = Array.isArray(xbrl[\"xbrli:context\"])\n      ? xbrl[\"xbrli:context\"]\n      : [xbrl[\"xbrli:context\"]];\n    contextArr.forEach((ctx) => {\n      contexts[ctx.$.id] = {\n        entity: ctx[\"xbrli:entity\"][\"xbrli:identifier\"]._,\n        startDate: ctx[\"xbrli:period\"][\"xbrli:startDate\"],\n        endDate: ctx[\"xbrli:period\"][\"xbrli:endDate\"],\n      };\n    });\n\n    // Parse units\n    if (xbrl[\"xbrli:unit\"]) {\n      const unitArr = Array.isArray(xbrl[\"xbrli:unit\"]) ? xbrl[\"xbrli:unit\"] : [xbrl[\"xbrli:unit\"]];\n      unitArr.forEach((u) => {\n        units[u.$.id] = u[\"xbrli:measure\"];\n      });\n    }\n\n    // Group data into categories\n    const categories = { Environment: [], Social: [], Governance: [] };\n\n    Object.keys(xbrl).forEach((tag) => {\n      if (tag.startsWith(\"in-capmkt:\")) {\n        const element = xbrl[tag];\n        const elements = Array.isArray(element) ? element : [element];\n        elements.forEach((el) => {\n          const context = contexts[el.$.contextRef];\n          const unit = el.$.unitRef ? units[el.$.unitRef] : null;\n          const category = categorizeField(tag);\n\n          categories[category].push({\n            metric: tag.replace(\"in-capmkt:\", \"\"),\n            value: el._,\n            unit,\n            period: context ? `${context.startDate} → ${context.endDate}` : \"\",\n            company: context?.entity,\n          });\n        });\n      }\n    });\n\n    return categories;\n  };\n\n  /** View single company **/\n  const handleCompanySelect = async (row) => {\n    setSelectedCompany(row);\n    setLoadingSingle(true);\n    setBrsrData({ Environment: [], Social: [], Governance: [] });\n\n    if (row[\"XML Link\"]) {\n      try {\n        const res = await axios.get(row[\"XML Link\"]);\n        const categories = await parseBRSRXML(res.data);\n        setBrsrData(categories);\n      } catch (err) {\n        console.error(\"Error loading XML:\", err);\n      } finally {\n        setLoadingSingle(false);\n      }\n    }\n  };\n\n  /** Fetch all companies **/\n  const fetchAllCompanies = async () => {\n    setLoadingAll(true);\n    const allData = [];\n    for (const row of companyData) {\n      if (row[\"XML Link\"]) {\n        try {\n          const res = await axios.get(row[\"XML Link\"]);\n          const categories = await parseBRSRXML(res.data);\n          [\"Environment\", \"Social\", \"Governance\"].forEach((cat) => {\n            categories[cat].forEach((d) =>\n              allData.push({\n                ...d,\n                category: cat,\n                companyName: row[\"Company Name\"],\n                year: row[\"Year\"],\n              })\n            );\n          });\n        } catch (err) {\n          console.warn(\"Failed to fetch:\", row[\"Company Name\"]);\n        }\n      }\n    }\n    setUnifiedData(allData);\n    setLoadingAll(false);\n  };\n\n  /** Filter data **/\n  const filteredTabData = brsrData[activeTab].filter(\n    (row) =>\n      row.metric.toLowerCase().includes(searchBRSR.toLowerCase()) ||\n      row.value.toLowerCase().includes(searchBRSR.toLowerCase())\n  );\n\n  return (\n    <div className=\"container-fluid py-4\" style={{backgroundColor: '#f8f9fa', minHeight: '100vh'}}>\n      <h1 className=\"display-4 fw-bold text-center mb-5 text-dark\">\n        📊 BRSR Report Viewer (Excel + XML)\n      </h1>\n\n      {/* Upload Excel */}\n      <div className=\"row justify-content-center mb-4\">\n        <div className=\"col-md-6\">\n          <input\n            type=\"file\"\n            accept=\".xlsx,.xls\"\n            onChange={handleExcelUpload}\n            className=\"form-control shadow-sm\"\n          />\n        </div>\n      </div>\n\n      {/* Search and Fetch All */}\n      <div className=\"row justify-content-center mb-4\">\n        <div className=\"col-md-4\">\n          <input\n            type=\"text\"\n            placeholder=\"Search company...\"\n            value={searchCompany}\n            onChange={(e) => setSearchCompany(e.target.value)}\n            className=\"form-control shadow-sm\"\n          />\n        </div>\n        <div className=\"col-md-2\">\n          <button\n            onClick={fetchAllCompanies}\n            disabled={loadingAll || companyData.length === 0}\n            className={`btn w-100 ${\n              loadingAll ? \"btn-secondary\" : \"btn-success\"\n            }`}\n          >\n            {loadingAll ? \"Fetching All...\" : \"Fetch All Companies\"}\n          </button>\n        </div>\n      </div>\n\n      {/* Company Table */}\n      <div className=\"card shadow mb-4\">\n        <div className=\"card-body\">\n          <DataTable\n            title=\"Companies\"\n            columns={[\n              { name: \"Company\", selector: (row) => row[\"Company Name\"], sortable: true },\n              { name: \"Year\", selector: (row) => row[\"Year\"], sortable: true },\n              {\n                name: \"Actions\",\n                cell: (row) => (\n                  <button\n                    onClick={() => handleCompanySelect(row)}\n                    className=\"btn btn-primary btn-sm\"\n                  >\n                    View BRSR\n                  </button>\n                ),\n              },\n            ]}\n            data={companyData.filter((r) =>\n              r[\"Company Name\"]?.toLowerCase().includes(searchCompany.toLowerCase())\n            )}\n            pagination\n            highlightOnHover\n            dense\n          />\n        </div>\n      </div>\n\n      {/* Unified Data Table */}\n      {unifiedData.length > 0 && (\n        <div className=\"card shadow mb-4\">\n          <div className=\"card-body\">\n            <h2 className=\"card-title h4 mb-4\">Unified BRSR Data</h2>\n            <DataTable\n              columns={[\n                { name: \"Company\", selector: (row) => row.companyName, sortable: true },\n                { name: \"Year\", selector: (row) => row.year, sortable: true },\n                { name: \"Category\", selector: (row) => row.category, sortable: true },\n                { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n                { name: \"Value\", selector: (row) => row.value, sortable: true },\n              ]}\n              data={unifiedData}\n              pagination\n              dense\n              highlightOnHover\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Tabs for single company */}\n      {selectedCompany && (\n        <div className=\"card shadow\">\n          <div className=\"card-body\">\n            <h2 className=\"card-title h4 mb-4\">\n              {selectedCompany[\"Company Name\"]} ({selectedCompany[\"Year\"]})\n            </h2>\n\n            {/* Tabs */}\n            <ul className=\"nav nav-tabs mb-4\">\n              {[\"Environment\", \"Social\", \"Governance\"].map((tab) => (\n                <li className=\"nav-item\" key={tab}>\n                  <button\n                    onClick={() => setActiveTab(tab)}\n                    className={`nav-link ${\n                      activeTab === tab ? \"active\" : \"\"\n                    }`}\n                    style={{\n                      backgroundColor: activeTab === tab\n                        ? tab === \"Environment\"\n                          ? \"#198754\"\n                          : tab === \"Social\"\n                          ? \"#6f42c1\"\n                          : \"#fd7e14\"\n                        : \"\",\n                      color: activeTab === tab ? \"white\" : \"\",\n                      border: \"none\"\n                    }}\n                  >\n                    {tab}\n                  </button>\n                </li>\n              ))}\n            </ul>\n\n            {/* Search */}\n            <div className=\"row justify-content-center mb-4\">\n              <div className=\"col-md-6\">\n                <input\n                  type=\"text\"\n                  placeholder={`Search in ${activeTab}...`}\n                  value={searchBRSR}\n                  onChange={(e) => setSearchBRSR(e.target.value)}\n                  className=\"form-control shadow-sm\"\n                />\n              </div>\n            </div>\n\n            {/* Table */}\n            {loadingSingle ? (\n              <div className=\"d-flex justify-content-center py-5\">\n                <div className=\"spinner-border text-primary\" role=\"status\">\n                  <span className=\"visually-hidden\">Loading...</span>\n                </div>\n              </div>\n            ) : (\n              <DataTable\n                columns={[\n                  { name: \"Metric\", selector: (row) => row.metric, sortable: true },\n                  { name: \"Value\", selector: (row) => row.value, sortable: true },\n                  { name: \"Unit\", selector: (row) => row.unit || \"-\", sortable: true },\n                  { name: \"Period\", selector: (row) => row.period, sortable: true },\n                ]}\n                data={filteredTabData}\n                pagination\n                dense\n                highlightOnHover\n              />\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,kBAAkB,QAAQ,QAAQ;AAC3C,OAAOC,SAAS,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IAAEiB,WAAW,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC;EACzF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM8B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;IACX,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,GAAG,IAAK;MACvB,MAAMC,QAAQ,GAAGtC,IAAI,CAACuC,IAAI,CAACF,GAAG,CAACL,MAAM,CAACQ,MAAM,EAAE;QAAEC,IAAI,EAAE;MAAS,CAAC,CAAC;MACjE,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,UAAU,CAAC,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGN,QAAQ,CAACO,MAAM,CAACH,SAAS,CAAC;MAC5C,MAAMI,IAAI,GAAG9C,IAAI,CAAC+C,KAAK,CAACC,aAAa,CAACJ,SAAS,CAAC;MAChDnC,cAAc,CAACqC,IAAI,CAAC;IACtB,CAAC;IACDZ,MAAM,CAACe,kBAAkB,CAAClB,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,KAAK,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;IAChC,IAAID,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,EAC9G,OAAO,aAAa;IACtB,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAC,EAChH,OAAO,QAAQ;IACjB,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOC,OAAO,IAAK;IACtC,MAAMhB,MAAM,GAAG,MAAMtC,kBAAkB,CAACsD,OAAO,EAAE;MAAEC,aAAa,EAAE;IAAM,CAAC,CAAC;IAC1E,MAAMC,IAAI,GAAGlB,MAAM,CAAC,YAAY,CAAC;IACjC,MAAMmB,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,MAAMC,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,eAAe,CAAC,CAAC,GACnDA,IAAI,CAAC,eAAe,CAAC,GACrB,CAACA,IAAI,CAAC,eAAe,CAAC,CAAC;IAC3BG,UAAU,CAACG,OAAO,CAAEC,GAAG,IAAK;MAC1BN,QAAQ,CAACM,GAAG,CAACC,CAAC,CAACC,EAAE,CAAC,GAAG;QACnBC,MAAM,EAAEH,GAAG,CAAC,cAAc,CAAC,CAAC,kBAAkB,CAAC,CAACI,CAAC;QACjDC,SAAS,EAAEL,GAAG,CAAC,cAAc,CAAC,CAAC,iBAAiB,CAAC;QACjDM,OAAO,EAAEN,GAAG,CAAC,cAAc,CAAC,CAAC,eAAe;MAC9C,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,IAAIP,IAAI,CAAC,YAAY,CAAC,EAAE;MACtB,MAAMc,OAAO,GAAGV,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,YAAY,CAAC,CAAC,GAAGA,IAAI,CAAC,YAAY,CAAC,GAAG,CAACA,IAAI,CAAC,YAAY,CAAC,CAAC;MAC7Fc,OAAO,CAACR,OAAO,CAAES,CAAC,IAAK;QACrBb,KAAK,CAACa,CAAC,CAACP,CAAC,CAACC,EAAE,CAAC,GAAGM,CAAC,CAAC,eAAe,CAAC;MACpC,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,UAAU,GAAG;MAAE1D,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;IAElEyD,MAAM,CAACC,IAAI,CAAClB,IAAI,CAAC,CAACM,OAAO,CAAEa,GAAG,IAAK;MACjC,IAAIA,GAAG,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QAChC,MAAMC,OAAO,GAAGrB,IAAI,CAACmB,GAAG,CAAC;QACzB,MAAMG,QAAQ,GAAGlB,KAAK,CAACC,OAAO,CAACgB,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;QAC7DC,QAAQ,CAAChB,OAAO,CAAEiB,EAAE,IAAK;UACvB,MAAMC,OAAO,GAAGvB,QAAQ,CAACsB,EAAE,CAACf,CAAC,CAACiB,UAAU,CAAC;UACzC,MAAMC,IAAI,GAAGH,EAAE,CAACf,CAAC,CAACmB,OAAO,GAAGzB,KAAK,CAACqB,EAAE,CAACf,CAAC,CAACmB,OAAO,CAAC,GAAG,IAAI;UACtD,MAAMC,QAAQ,GAAGpC,eAAe,CAAC2B,GAAG,CAAC;UAErCH,UAAU,CAACY,QAAQ,CAAC,CAACC,IAAI,CAAC;YACxBC,MAAM,EAAEX,GAAG,CAACY,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;YACrCC,KAAK,EAAET,EAAE,CAACZ,CAAC;YACXe,IAAI;YACJO,MAAM,EAAET,OAAO,GAAG,GAAGA,OAAO,CAACZ,SAAS,MAAMY,OAAO,CAACX,OAAO,EAAE,GAAG,EAAE;YAClEqB,OAAO,EAAEV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEd;UACpB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOM,UAAU;EACnB,CAAC;;EAED;EACA,MAAMmB,mBAAmB,GAAG,MAAOC,GAAG,IAAK;IACzCjF,kBAAkB,CAACiF,GAAG,CAAC;IACvBlE,gBAAgB,CAAC,IAAI,CAAC;IACtBb,WAAW,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC,CAAC;IAE5D,IAAI4E,GAAG,CAAC,UAAU,CAAC,EAAE;MACnB,IAAI;QACF,MAAMC,GAAG,GAAG,MAAM9F,KAAK,CAAC+F,GAAG,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAMpB,UAAU,GAAG,MAAMnB,YAAY,CAACwC,GAAG,CAACE,IAAI,CAAC;QAC/ClF,WAAW,CAAC2D,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOwB,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,SAAS;QACRtE,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF;EACF,CAAC;;EAED;EACA,MAAMyE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC3E,aAAa,CAAC,IAAI,CAAC;IACnB,MAAM4E,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMR,GAAG,IAAItF,WAAW,EAAE;MAC7B,IAAIsF,GAAG,CAAC,UAAU,CAAC,EAAE;QACnB,IAAI;UACF,MAAMC,GAAG,GAAG,MAAM9F,KAAK,CAAC+F,GAAG,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;UAC5C,MAAMpB,UAAU,GAAG,MAAMnB,YAAY,CAACwC,GAAG,CAACE,IAAI,CAAC;UAC/C,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACjC,OAAO,CAAEuC,GAAG,IAAK;YACvD7B,UAAU,CAAC6B,GAAG,CAAC,CAACvC,OAAO,CAAEwC,CAAC,IACxBF,OAAO,CAACf,IAAI,CAAC;cACX,GAAGiB,CAAC;cACJlB,QAAQ,EAAEiB,GAAG;cACbE,WAAW,EAAEX,GAAG,CAAC,cAAc,CAAC;cAChCY,IAAI,EAAEZ,GAAG,CAAC,MAAM;YAClB,CAAC,CACH,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZC,OAAO,CAACQ,IAAI,CAAC,kBAAkB,EAAEb,GAAG,CAAC,cAAc,CAAC,CAAC;QACvD;MACF;IACF;IACAtE,cAAc,CAAC8E,OAAO,CAAC;IACvB5E,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMkF,eAAe,GAAG9F,QAAQ,CAACO,SAAS,CAAC,CAACwF,MAAM,CAC/Cf,GAAG,IACFA,GAAG,CAACN,MAAM,CAACnC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAAC,IAC3DyC,GAAG,CAACJ,KAAK,CAACrC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAC7D,CAAC;EAED,oBACEhD,OAAA;IAAKyG,SAAS,EAAC,sBAAsB;IAACC,KAAK,EAAE;MAACC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC5F7G,OAAA;MAAIyG,SAAS,EAAC,8CAA8C;MAAAI,QAAA,EAAC;IAE7D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLjH,OAAA;MAAKyG,SAAS,EAAC,iCAAiC;MAAAI,QAAA,eAC9C7G,OAAA;QAAKyG,SAAS,EAAC,UAAU;QAAAI,QAAA,eACvB7G,OAAA;UACEoC,IAAI,EAAC,MAAM;UACX8E,MAAM,EAAC,YAAY;UACnBC,QAAQ,EAAE3F,iBAAkB;UAC5BiF,SAAS,EAAC;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjH,OAAA;MAAKyG,SAAS,EAAC,iCAAiC;MAAAI,QAAA,gBAC9C7G,OAAA;QAAKyG,SAAS,EAAC,UAAU;QAAAI,QAAA,eACvB7G,OAAA;UACEoC,IAAI,EAAC,MAAM;UACXgF,WAAW,EAAC,mBAAmB;UAC/B/B,KAAK,EAAEhF,aAAc;UACrB8G,QAAQ,EAAG1F,CAAC,IAAKnB,gBAAgB,CAACmB,CAAC,CAACE,MAAM,CAAC0D,KAAK,CAAE;UAClDoB,SAAS,EAAC;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNjH,OAAA;QAAKyG,SAAS,EAAC,UAAU;QAAAI,QAAA,eACvB7G,OAAA;UACEqH,OAAO,EAAErB,iBAAkB;UAC3BsB,QAAQ,EAAElG,UAAU,IAAIjB,WAAW,CAACoH,MAAM,KAAK,CAAE;UACjDd,SAAS,EAAE,aACTrF,UAAU,GAAG,eAAe,GAAG,aAAa,EAC3C;UAAAyF,QAAA,EAEFzF,UAAU,GAAG,iBAAiB,GAAG;QAAqB;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjH,OAAA;MAAKyG,SAAS,EAAC,kBAAkB;MAAAI,QAAA,eAC/B7G,OAAA;QAAKyG,SAAS,EAAC,WAAW;QAAAI,QAAA,eACxB7G,OAAA,CAACF,SAAS;UACR0H,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAE,CACP;YAAE3E,IAAI,EAAE,SAAS;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAAC,cAAc,CAAC;YAAEkC,QAAQ,EAAE;UAAK,CAAC,EAC3E;YAAE7E,IAAI,EAAE,MAAM;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAAC,MAAM,CAAC;YAAEkC,QAAQ,EAAE;UAAK,CAAC,EAChE;YACE7E,IAAI,EAAE,SAAS;YACf8E,IAAI,EAAGnC,GAAG,iBACRzF,OAAA;cACEqH,OAAO,EAAEA,CAAA,KAAM7B,mBAAmB,CAACC,GAAG,CAAE;cACxCgB,SAAS,EAAC,wBAAwB;cAAAI,QAAA,EACnC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAEZ,CAAC,CACD;UACFrB,IAAI,EAAEzF,WAAW,CAACqG,MAAM,CAAEqB,CAAC;YAAA,IAAAC,cAAA;YAAA,QAAAA,cAAA,GACzBD,CAAC,CAAC,cAAc,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmB9E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5C,aAAa,CAAC2C,WAAW,CAAC,CAAC,CAAC;UAAA,CACxE,CAAE;UACF+E,UAAU;UACVC,gBAAgB;UAChBC,KAAK;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/F,WAAW,CAACqG,MAAM,GAAG,CAAC,iBACrBvH,OAAA;MAAKyG,SAAS,EAAC,kBAAkB;MAAAI,QAAA,eAC/B7G,OAAA;QAAKyG,SAAS,EAAC,WAAW;QAAAI,QAAA,gBACxB7G,OAAA;UAAIyG,SAAS,EAAC,oBAAoB;UAAAI,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDjH,OAAA,CAACF,SAAS;UACR2H,OAAO,EAAE,CACP;YAAE3E,IAAI,EAAE,SAAS;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACW,WAAW;YAAEuB,QAAQ,EAAE;UAAK,CAAC,EACvE;YAAE7E,IAAI,EAAE,MAAM;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACY,IAAI;YAAEsB,QAAQ,EAAE;UAAK,CAAC,EAC7D;YAAE7E,IAAI,EAAE,UAAU;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACR,QAAQ;YAAE0C,QAAQ,EAAE;UAAK,CAAC,EACrE;YAAE7E,IAAI,EAAE,QAAQ;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACN,MAAM;YAAEwC,QAAQ,EAAE;UAAK,CAAC,EACjE;YAAE7E,IAAI,EAAE,OAAO;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACJ,KAAK;YAAEsC,QAAQ,EAAE;UAAK,CAAC,CAC/D;UACF/B,IAAI,EAAE1E,WAAY;UAClB6G,UAAU;UACVE,KAAK;UACLD,gBAAgB;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA1G,eAAe,iBACdP,OAAA;MAAKyG,SAAS,EAAC,aAAa;MAAAI,QAAA,eAC1B7G,OAAA;QAAKyG,SAAS,EAAC,WAAW;QAAAI,QAAA,gBACxB7G,OAAA;UAAIyG,SAAS,EAAC,oBAAoB;UAAAI,QAAA,GAC/BtG,eAAe,CAAC,cAAc,CAAC,EAAC,IAAE,EAACA,eAAe,CAAC,MAAM,CAAC,EAAC,GAC9D;QAAA;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGLjH,OAAA;UAAIyG,SAAS,EAAC,mBAAmB;UAAAI,QAAA,EAC9B,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACqB,GAAG,CAAEC,GAAG,iBAC/CnI,OAAA;YAAIyG,SAAS,EAAC,UAAU;YAAAI,QAAA,eACtB7G,OAAA;cACEqH,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAACkH,GAAG,CAAE;cACjC1B,SAAS,EAAE,YACTzF,SAAS,KAAKmH,GAAG,GAAG,QAAQ,GAAG,EAAE,EAChC;cACHzB,KAAK,EAAE;gBACLC,eAAe,EAAE3F,SAAS,KAAKmH,GAAG,GAC9BA,GAAG,KAAK,aAAa,GACnB,SAAS,GACTA,GAAG,KAAK,QAAQ,GAChB,SAAS,GACT,SAAS,GACX,EAAE;gBACNC,KAAK,EAAEpH,SAAS,KAAKmH,GAAG,GAAG,OAAO,GAAG,EAAE;gBACvCE,MAAM,EAAE;cACV,CAAE;cAAAxB,QAAA,EAEDsB;YAAG;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAnBmBkB,GAAG;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoB7B,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGLjH,OAAA;UAAKyG,SAAS,EAAC,iCAAiC;UAAAI,QAAA,eAC9C7G,OAAA;YAAKyG,SAAS,EAAC,UAAU;YAAAI,QAAA,eACvB7G,OAAA;cACEoC,IAAI,EAAC,MAAM;cACXgF,WAAW,EAAE,aAAapG,SAAS,KAAM;cACzCqE,KAAK,EAAEvE,UAAW;cAClBqG,QAAQ,EAAG1F,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACE,MAAM,CAAC0D,KAAK,CAAE;cAC/CoB,SAAS,EAAC;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL3F,aAAa,gBACZtB,OAAA;UAAKyG,SAAS,EAAC,oCAAoC;UAAAI,QAAA,eACjD7G,OAAA;YAAKyG,SAAS,EAAC,6BAA6B;YAAC6B,IAAI,EAAC,QAAQ;YAAAzB,QAAA,eACxD7G,OAAA;cAAMyG,SAAS,EAAC,iBAAiB;cAAAI,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENjH,OAAA,CAACF,SAAS;UACR2H,OAAO,EAAE,CACP;YAAE3E,IAAI,EAAE,QAAQ;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACN,MAAM;YAAEwC,QAAQ,EAAE;UAAK,CAAC,EACjE;YAAE7E,IAAI,EAAE,OAAO;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACJ,KAAK;YAAEsC,QAAQ,EAAE;UAAK,CAAC,EAC/D;YAAE7E,IAAI,EAAE,MAAM;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACV,IAAI,IAAI,GAAG;YAAE4C,QAAQ,EAAE;UAAK,CAAC,EACpE;YAAE7E,IAAI,EAAE,QAAQ;YAAE4E,QAAQ,EAAGjC,GAAG,IAAKA,GAAG,CAACH,MAAM;YAAEqC,QAAQ,EAAE;UAAK,CAAC,CACjE;UACF/B,IAAI,EAAEW,eAAgB;UACtBwB,UAAU;UACVE,KAAK;UACLD,gBAAgB;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC/G,EAAA,CAvTuBD,UAAU;AAAAsI,EAAA,GAAVtI,UAAU;AAAA,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}